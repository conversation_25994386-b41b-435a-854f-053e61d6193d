package org.befun.auth.utils;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExcelParserHelper测试类
 */
class ExcelParserHelperTest {

    @Test
    void testParseExcel_EmptyFile() {
        MultipartFile emptyFile = new MockMultipartFile("test.xlsx", new byte[0]);
        
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelParserHelper.parseExcel(emptyFile);
        });
    }

    @Test
    void testParseExcel_InvalidFileType() {
        MultipartFile txtFile = new MockMultipartFile("test.txt", "test.txt", "text/plain", "test content".getBytes());
        
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelParserHelper.parseExcel(txtFile);
        });
    }

    @Test
    void testParseExcel_NullFile() {
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelParserHelper.parseExcel(null);
        });
    }

    @Test
    void testParseExcel_WithHeaderRowIndex() {
        // 创建一个简单的Excel文件内容（这里只是模拟，实际测试需要真实的Excel文件）
        byte[] excelContent = createSimpleExcelContent();
        MultipartFile file = new MockMultipartFile("test.xlsx", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelContent);
        
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelParserHelper.parseExcel(file, -1);
        });
    }

    @Test
    void testParseExcel_WithClass() {
        byte[] excelContent = createSimpleExcelContent();
        MultipartFile file = new MockMultipartFile("test.xlsx", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelContent);
        
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelParserHelper.parseExcel(file, null);
        });
    }

    @Test
    void testExcelParseResult_Success() {
        List<String> headers = List.of("姓名", "年龄", "部门");
        List<Map<String, Object>> dataRows = List.of(
            Map.of("姓名", "张三", "年龄", "25", "部门", "技术部"),
            Map.of("姓名", "李四", "年龄", "30", "部门", "市场部")
        );
        List<List<Object>> rawDataRows = List.of(
            List.of("张三", "25", "技术部"),
            List.of("李四", "30", "市场部")
        );

        ExcelParserHelper.ExcelParseResult result = ExcelParserHelper.ExcelParseResult.success(headers, dataRows, rawDataRows);

        assertTrue(result.isSuccess());
        assertEquals(3, result.getHeaders().size());
        assertEquals(2, result.getDataRows().size());
        assertEquals(2, result.getRawDataRows().size());
        assertEquals(3, result.getTotalRows()); // 2 data rows + 1 header row
        assertEquals(2, result.getDataRowCount());
        assertEquals(3, result.getColumnCount());
        assertNull(result.getErrorMessage());
    }

    @Test
    void testExcelParseResult_Failure() {
        String errorMessage = "解析失败";
        ExcelParserHelper.ExcelParseResult result = ExcelParserHelper.ExcelParseResult.failure(errorMessage);

        assertFalse(result.isSuccess());
        assertEquals(errorMessage, result.getErrorMessage());
        assertTrue(result.getHeaders().isEmpty());
        assertTrue(result.getDataRows().isEmpty());
        assertTrue(result.getRawDataRows().isEmpty());
        assertEquals(0, result.getTotalRows());
        assertEquals(0, result.getDataRowCount());
        assertEquals(0, result.getColumnCount());
    }

    /**
     * 创建简单的Excel内容（模拟）
     * 注意：这里只是返回空字节数组，实际测试需要真实的Excel文件内容
     */
    private byte[] createSimpleExcelContent() {
        // 在实际测试中，这里应该创建真实的Excel文件内容
        // 或者使用测试资源文件
        return new byte[0];
    }
}
