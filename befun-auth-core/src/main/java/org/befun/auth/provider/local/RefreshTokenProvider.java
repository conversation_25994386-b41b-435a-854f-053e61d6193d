package org.befun.auth.provider.local;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.constant.LoginPlatform;
import org.befun.auth.constant.LoginRefreshTokenType;
import org.befun.auth.dto.LoginPasswordMfaCacheDto;
import org.befun.auth.dto.LoginRefreshTokenRequestDto;
import org.befun.auth.dto.LoginResponseDto;
import org.befun.auth.dto.auth.authcode.AuthCodeCache;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.AuthService;
import org.befun.auth.utils.PasswordLoginHelper;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.EnumHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;

import static org.befun.auth.constant.VerifyCodeUseFor.MFA;

@Service("befun_auth_refresh_token")
public class RefreshTokenProvider extends BaseAuthProvider {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private PasswordLoginHelper passwordLoginHelper;
    @Autowired
    private EmailVerifyCodeProvider emailVerifyCodeProvider;
    @Autowired
    private MobileVerifyCodeProvider mobileVerifyCodeProvider;
    @Lazy
    @Autowired
    private AuthService authService;

    @Override
    public Object login(String source, String app, Object callback) {
        LoginRefreshTokenRequestDto refreshTokenDto = (LoginRefreshTokenRequestDto) callback;
        LoginPlatform platform;
        String refreshToken = refreshTokenDto.getRefreshToken();
        String[] as = refreshToken.split("\\.");
        if (as.length > 1) {
            platform = EnumHelper.parse(LoginPlatform.values(), as[0], LoginPlatform.pc);
        } else {
            platform = LoginPlatform.pc;
        }
        if (refreshTokenDto.getRefreshTokenType() != null && refreshTokenDto.getRefreshTokenType() == LoginRefreshTokenType.MFA) {
            return refreshTokenLoginByMfa(platform, refreshToken, refreshTokenDto.getCode(), app);
        } else if (refreshTokenDto.getRefreshTokenType() != null && refreshTokenDto.getRefreshTokenType() == LoginRefreshTokenType.AUTH_CODE) {
            return refreshTokenLoginByAuthCode(platform, refreshTokenDto.getRefreshToken(), app);
        } else {
            return checkLoginInfo(platform, getUserByRefreshToken(refreshToken), app);
        }
    }

    private Object refreshTokenLoginByAuthCode(LoginPlatform platform, String authCode, String app) {
        AuthCodeCache cache = authService.checkAuthCodeByLogin(authCode);
        User user = requireUserById(cache.getUserId());
        authService.clearAuthCode(authCode);
        return checkLoginInfo(platform, user, app);
    }

    private Object refreshTokenLoginByMfa(LoginPlatform platform, String refreshToken, String code, String app) {
        LoginPasswordMfaCacheDto dto = passwordLoginHelper.getMfaLoginCacheInfo(refreshToken);
        if (dto != null && platform.name().equals(dto.getPlatform())
                && dto.getOrgId() != null
                && dto.getUserId() != null
        ) {
            VerifyCodeProvider provider = null;
            String account = null;
            if ("email".equals(dto.getMfaVerifyType())) {
                provider = emailVerifyCodeProvider;
                account = dto.getEmail();
            } else if ("mobile".equals(dto.getMfaVerifyType())) {
                provider = mobileVerifyCodeProvider;
                account = dto.getMobile();
            }
            if (provider != null && StringUtils.isNotEmpty(account)) {
                return provider.verifyCodeAndApply(MFA, account, code, () -> {
                    User user = requireUserById(dto.getUserId());
                    LoginResponseDto response = checkLoginInfo(platform, user, app);
                    passwordLoginHelper.clearMfaParams(refreshToken);
                    passwordLoginHelper.clearFailTimes(user.getId());
                    passwordLoginHelper.markLastLoginTimeByMfa(user.getOrgId(), user.getId());
                    return response;
                }, i -> true);
            }
        }
        throw new BadRequestException("验证已超时");
    }

    public String cacheRefreshToken(Long userId, LoginPlatform platform) {
        userId = userId == null ? 0 : userId;
        String refreshToken = platform.name() + "." + UUID.randomUUID();
        stringRedisTemplate.opsForValue().set(String.format("refresh-token:%s", refreshToken), userId.toString());
        return refreshToken;
    }

    public User getUserByRefreshToken(String refreshToken) {
        String key = String.format("refresh-token:%s", refreshToken);
        Object value = stringRedisTemplate.opsForValue().get(key);
        if (value != null && NumberUtils.isDigits(value.toString())) {
            Long userId = Long.parseLong(value.toString());
            stringRedisTemplate.delete(key);
            return requireUserById(userId);
        }
        throw new BadRequestException("无效的token");
    }
}
