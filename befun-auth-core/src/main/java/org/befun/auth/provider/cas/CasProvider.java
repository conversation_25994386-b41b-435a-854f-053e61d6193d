package org.befun.auth.provider.cas;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.CasProperty;
import org.befun.auth.dto.LoginSSOVerifyResponseDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.service.*;
import org.befun.auth.service.auth.config.CasConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.jasig.cas.client.validation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

import static org.befun.auth.constant.ThirdPartyAuthType.CAS;

@Slf4j
@Service("befun_auth_cas")
@ConditionalOnProperty(name = "befun.auth.enable-provider", havingValue = "true")
public class CasProvider extends BaseAuthProvider {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private ThirdPartyAuthWhiteListService thirdPartyAuthWhiteListService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService userService;

    private CasConfig requireConfig(ThirdPartyAuth auth) {
        return parseConfig(auth, true);
    }

    private CasConfig parseConfig(ThirdPartyAuth auth, boolean throwable) {
        CasConfig config = null;
        if (auth != null) {
            config = JsonHelper.toObject(auth.getConfig(), CasConfig.class);
        }
        if (config != null && StringUtils.isNotEmpty(config.getCasUrl())) {
            return config;
        }
        if (throwable) {
            throw new BadRequestException("未设置cas登录方式");
        } else {
            return null;
        }
    }

    private String parseOrgCode(String source) {
        source = source.replaceFirst(CAS.getSourcePrefix(), "");
        if (source.endsWith(CAS.getSourceSuffix())) {
            return source.substring(0, source.length() - CAS.getSourceSuffix().length());
        }
        return source;
    }

    private boolean isSecondary(String source) {
        return source.endsWith(CAS.getSourceSuffix());
    }

    private Organization getOrg(String source) {
        String orgCode = parseOrgCode(source);
        return organizationService.getByOrgCode(orgCode);
    }

    private long requireOrg(String source) {
        Organization org = getOrg(source);
        if (org == null) {
            throw new BadRequestException("无效的企业编号");
        }
        return org.getId();
    }

    private String buildLoginUrl(String source, String orgCode, String app) {
        CasProperty property = authProperties.getCas();
        Assert.notNull(property, "未配置cas登录方式");
        StringSubstitutor match = new StringSubstitutor(Map.of("orgCode", orgCode, "app", app), "#{", "}");
        if (isSecondary(source)) {
            return match.replace(property.getCasUrlSecondary());
        } else {
            return match.replace(property.getCasUrl());
        }
    }

    private String buildCasUrl(String source, CasConfig config) {
        String[] urls = config.getCasUrl().split(",");
        if (urls.length > 1 && isSecondary(source)) {
            return urls[1];
        } else {
            return urls[0];
        }
    }

    @Override
    public Object verify(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        Organization org = getOrg(source);
        if (org == null) {
            return LoginSSOVerifyResponseDto.orgCodeError();
        }
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(org.getId(), CAS, app);
        CasConfig config = parseConfig(auth, false);
        if (config == null || StringUtils.isEmpty(config.getCasUrl())) {
            return LoginSSOVerifyResponseDto.notConfigError();
        }
        return LoginSSOVerifyResponseDto.success();
    }

    @Override
    public String authorize(String source, String app) {
        String orgCode = parseOrgCode(source);
        long orgId = requireOrg(source);
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, CAS, app);
        CasConfig config = requireConfig(auth);
        String loginUrl = buildLoginUrl(source, orgCode, app);
        String casUrl = buildCasUrl(source, config);
        String casLoginUrl;
        if (casUrl.endsWith("/")) {
            casLoginUrl = casUrl + "login?service=%s";
        } else {
            casLoginUrl = casUrl + "/login?service=%s";
        }
        return String.format(casLoginUrl, URLEncoder.encode(loginUrl));
    }

    private TicketValidator getValidator(String source, CasConfig config) {
        String casUrl = buildCasUrl(source, config);
        if ("v1".equalsIgnoreCase(config.getCasVersion())) {
            return new Cas10TicketValidator(casUrl);
        } else if ("v2".equalsIgnoreCase(config.getCasVersion())) {
            return new Cas20ServiceTicketValidator(casUrl);
        } else {
            return new Cas30ServiceTicketValidator(casUrl);
        }
    }

    @Override
    public Object login(String source, String app, Object callback) {
        String ticket = (String) callback;
        try {
            String orgCode = parseOrgCode(source);
            long orgId = requireOrg(source);
            String finalSource = CAS.getSourcePrefix() + orgId;
            ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, CAS, app);
            CasConfig config = requireConfig(auth);
            TicketValidator validator = getValidator(source, config);
            Assertion assertion = validator.validate(ticket, buildLoginUrl(source, orgCode, app));
            if (assertion.isValid() && assertion.getPrincipal() != null) {
                AttributePrincipal principal = assertion.getPrincipal();
                String openId = getThirdPartyParams(assertion, config::getCasParamId);
                if (StringUtils.isEmpty(openId)) {
                    openId = principal.getName();
                }
                if (ignoreWhiteList(auth) || thirdPartyAuthWhiteListService.isInWhiteList(auth.getId(), openId)) {
                    ThirdPartyUser thirdPartyUser = thirdPartyUserService.getBySourceAppOpenId(finalSource, app, openId);
                    List<String> roleCodes = getRoleCodes(assertion, config::getParamRoleCode);
                    if (thirdPartyUser != null) {
                        return loginByThirdPartyUser(thirdPartyUser, roleCodes, app);
                    } else {
                        String name = getThirdPartyParams(assertion, config::getCasParamName);
                        String email = getThirdPartyParams(assertion, config::getCasParamEmail);
                        String mobile = getThirdPartyParams(assertion, config::getCasParamMobile);
                        Set<String> departmentCode = Set.of(getThirdPartyParams(assertion, config::getCasParamDepartment));
                        String employeeNo = getThirdPartyParams(assertion, config::getCasParamEmployeeNo);
                        User user = userService.addUserByThirdParty(auth, openId, name == null ? openId : name, mobile, email, departmentCode, employeeNo);
                        return checkLoginInfo(user, roleCodes, app);
                    }
                } else {
                    log.warn("第三方cas登录失败，账号未加入白名单，source={}, app={}, openId={}", source, app, openId);
                }
            }
        } catch (Exception e) {
            log.error("第三方cas登录失败 source={}, app={}, ticket={}", source, app, ticket, e);
            if (e instanceof BadRequestException) {
                throw (BadRequestException) e;
            }
        }
        throw new BadRequestException("cas登录失败");
    }

    private boolean ignoreWhiteList(ThirdPartyAuth auth) {
        return auth == null || auth.getEnableWhiteList() == null || auth.getEnableWhiteList() != 1;
    }

    private List<String> getRoleCodes(Assertion assertion, Supplier<String> getKey) {
        return parseRoleCodes(getThirdPartyParams0(assertion, getKey));
    }

    private String getThirdPartyParams(Assertion assertion, Supplier<String> getKey) {
        Object param = getThirdPartyParams0(assertion, getKey);
        if (param != null) {
            return param.toString();
        }
        return null;
    }

    private Object getThirdPartyParams0(Assertion assertion, Supplier<String> getKey) {
        String key = getKey.get();
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        Map<String, Object> params = assertion.getAttributes();
        Object param = null;
        if (MapUtils.isNotEmpty(params)) {
            param = params.get(key);
        }
        if (param == null && MapUtils.isNotEmpty(params = assertion.getPrincipal().getAttributes())) {
            param = params.get(key);
        }
        return param;
    }
}
