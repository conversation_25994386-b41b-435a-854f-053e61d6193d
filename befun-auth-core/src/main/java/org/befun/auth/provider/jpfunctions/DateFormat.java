package org.befun.auth.provider.jpfunctions;

import cn.hutool.core.date.DateUtil;
import com.jayway.jsonpath.internal.EvaluationContext;
import com.jayway.jsonpath.internal.PathRef;
import com.jayway.jsonpath.internal.function.Parameter;
import com.jayway.jsonpath.internal.function.PathFunction;

import java.util.ArrayList;
import java.util.List;

public class DateFormat implements PathFunction {
    @Override
    public Object invoke(String currentPath, PathRef parent, Object model, EvaluationContext ctx, List<Parameter> parameters) {
        StringBuilder result = new StringBuilder();

        if (parameters != null) {
            ArrayList<String> pre = new ArrayList<>();
            for (Parameter param : parameters) {
                Object value = param.getValue();
                if (value != null) {
                   pre.add(value.toString());
                }
            }

           String date = pre.get(0);
           String format = pre.get(1);
           result.append(DateUtil.format(DateUtil.parse(date), format));

        }

        return result.toString();
    }
}
