package org.befun.auth.provider.jpfunctions;

import com.jayway.jsonpath.internal.EvaluationContext;
import com.jayway.jsonpath.internal.PathRef;
import com.jayway.jsonpath.internal.function.Parameter;
import com.jayway.jsonpath.internal.function.PathFunction;

import java.util.ArrayList;
import java.util.List;

public class Substring implements PathFunction {
    @Override
    public Object invoke(String currentPath, PathRef parent, Object model, EvaluationContext ctx, List<Parameter> parameters) {
        Object result = null;
        if(ctx.configuration().jsonProvider().isArray(model)){
            Iterable<?> objects = ctx.configuration().jsonProvider().toIterable(model);
            List<String> strings = new ArrayList<>();
            for (Object obj : objects) {
                if (obj instanceof String) {
                    Integer start = (Integer) parameters.get(0).getValue();
                    Integer end = (Integer) parameters.get(1).getValue();
                    String target = (String) obj;
                    if (end < start) {
                        end = target.length();
                    }
                    strings.add(target.substring(start, end));
                }
            }
            result = strings;
        }else{
            Integer start = (Integer) parameters.get(0).getValue();
            Integer end = (Integer) parameters.get(1).getValue();
            String target = (String) model;
            if (end < start) {
                end = target.length();
            }
            result = target.substring(start, end);
        }


        return result;
    }
}
