package org.befun.auth.provider.wechat.work;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.WechatWorkProperty;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.WechatWorkOrderStatus;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.wework.WeworkOrder;
import org.befun.auth.entity.wework.WeworkOrderAccount;
import org.befun.auth.repository.WeworkOrderAccountRepository;
import org.befun.auth.repository.WeworkOrderRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.auth.config.WechatWorkConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ThirdPartyAuthType.WECHAT_WORK;

@Slf4j
@Service
public class WeChatWorkAccountHelper {
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private WeworkOrderRepository weworkOrderRepository;
    @Autowired
    private WeworkOrderAccountRepository weworkOrderAccountRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;

    private static final String URL_CORP_ACCESS_TOKEN = "https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token";
    private static final String URL_ORG_ACCESS_TOKEN = "https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=%s";
    private static final String URL_SUITE_ACCESS_TOKEN = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token";
    // 下单购买帐号
    private static final String URL_PLACE_ORDER = "https://qyapi.weixin.qq.com/cgi-bin/license/create_new_order?provider_access_token=%s";
    // 获取订单详情
    private static final String URL_ORDER_DETAIL = "https://qyapi.weixin.qq.com/cgi-bin/license/get_order?provider_access_token=%s";
    // 获取订单中的帐号列表
    private static final String URL_ORDER_ACTIVE_CODE = "https://qyapi.weixin.qq.com/cgi-bin/license/list_order_account?provider_access_token=%s";
    // 激活帐号
    private static final String URL_ACTIVE_ACCOUNT = "https://qyapi.weixin.qq.com/cgi-bin/license/active_account?provider_access_token=%s";
    private static final String URL_ACTIVE_REUSE_ACCOUNT = "https://qyapi.weixin.qq.com/cgi-bin/license/batch_transfer_license?provider_access_token=%s";
    private static final String URL_ACCOUNT_STATUS = "https://qyapi.weixin.qq.com/cgi-bin/license/get_active_info_by_code?provider_access_token=%s";
    private static final String URL_ORG_TRANSFORM_CORP_ID = "https://qyapi.weixin.qq.com/cgi-bin/service/corpid_to_opencorpid?provider_access_token=%s";
    private static final String URL_ORG_TRANSFORM_OPEN_ID = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=%s";


    public boolean isActiveUser(Long orgId, WechatWorkConfig config, String openId) {
        return true;
//        WeworkOrderAccount account = weworkOrderAccountRepository.findFirstByOrgIdAndCorpIdAndOpenId(orgId, config.getCompanyId(), openId);
//        return account != null && account.getStatus() != null && account.getStatus() == 1;
    }

    /**
     * 下单
     * 绑定企业
     * 1 绑定成功
     * 2 获取需要的账号数量
     * 3 下单
     */
    public WeworkOrder placeOrder(Long orgId, WechatWorkConfig config) {
        return null;
//        WeworkOrder order = weworkOrderRepository.findFirstByOrgIdAndCorpIdOrderByCreateTimeDesc(orgId, config.getCompanyId());
//        if (order != null) {
//            log.info("企业（{}）已购买订单，corpId={}", orgId, config.getCompanyId());
//            return order;
//        }
//        log.info("企业（{}）未购买订单，开始下单", orgId);
//        WechatWorkProperty property = requireProperty();
//        int count = countOrderAccount(orgId);
//        String weworkOrderId = placeOrder0(count, property, config);
//        order = new WeworkOrder(orgId, config.getCompanyId(), property.getOrderBuyerUserId(), count, property.getOrderMonth(), weworkOrderId);
//        weworkOrderRepository.save(order);
//        return order;
    }

    /**
     * 查询需要下单的人数，企业的版本人数
     */
    private int countOrderAccount(Long orgId) {
        Integer size = organizationService.parseOrgOptionalLimit(organizationService.requireById(orgId)).getChildUserLimit();
        log.info("企业（{}）需要的账号数量（{}）", orgId, size);
        return size;
    }

    private String placeOrder0(int count, WechatWorkProperty property, WechatWorkConfig config) {
        String accessToken = requireCropAccessToken();
        Map<String, Object> body = Map.of(
                "corpid", transformCorpId(config.getCompanyId()),
                "buyer_userid", property.getOrderBuyerUserId(),
                "account_count", Map.of("base_count", count, "external_contact_count", 0),
                "account_duration", Map.of("months", property.getOrderMonth()));
        WechatWorkPlaceOrderDto dto = httpPost(String.format(URL_PLACE_ORDER, accessToken), body, WechatWorkPlaceOrderDto.class);
        if (dto == null || dto.getErrcode() != 0) {
            log.error("下单购买帐号失败");
            throw new BadRequestException();
        }
        log.error("下单购买帐号成功");
        return dto.getOrderId();
    }

    public void bindMember(ThirdPartyUser thirdPartyUser) {
//        try {
//            WechatWorkConfig config = checkConfig(thirdPartyUser);
//            bindMember(thirdPartyUser.getOrgId(), thirdPartyUser.getOpenId(), config);
//        } catch (Throwable e) {
//            log.error("企业（{}）成员（{}）激活失败", thirdPartyUser.getOrgId(), thirdPartyUser.getOpenId());
//        }
    }

    public void bindMemberWhenLogin(Long orgId, String openId, WechatWorkConfig config) {
//        try {
//            bindMember(orgId, openId, config);
//        } catch (Throwable e) {
//            log.error("企业（{}）成员（{}）激活失败", orgId, openId);
//        }
    }

    /**
     * 绑定账号
     * 1 查询订单
     * 2 获取激活码
     * 3 激活
     */
    public boolean bindMember(Long orgId, String openId, WechatWorkConfig config) {
//        // 查询最新的订单
//        WeworkOrder order = weworkOrderRepository.findFirstByOrgIdAndCorpIdOrderByCreateTimeDesc(orgId, config.getCompanyId());
//        if (order == null) {
//            // 如果没有订单
//            log.error("企业（{}）未初始化订单", orgId);
//            throw new BadRequestException("");
//        } else if (WechatWorkOrderStatus.needQueryStatus(order.getPayStatus())) {
//            // 查询订单，
//            log.info("企业（{}）订单未支付", orgId);
//            applyOrderStatus(order);
//        }
//        if (!WechatWorkOrderStatus.isSuccess(order.getPayStatus())) {
//            throw ToastMessageHelper.badRequestException(AuthToastMessage.WECHAT_WORK_ORDER_NOT_SUCCESS);
//        }
//
//        WeworkOrderAccount account = weworkOrderAccountRepository.findFirstByOrgIdAndCorpIdAndOpenId(orgId, order.getCorpId(), openId);
//        if (account != null) {
//            // 已经激活了
//            return true;
//        }
//        // 使用激活码
//        if (!useAccount(orgId, order, openId, config)) {
//            // 检查是否由无效的激活码可以继承
//            if (!reuseAccount(orgId, openId, order, config)) {
//                throw ToastMessageHelper.badRequestException(AuthToastMessage.WECHAT_WORK_ORDER_NO_BALANCE);
//            }
//        }
        return true;
    }

    private boolean reuseAccount(Long orgId, String openId, WeworkOrder order, WechatWorkConfig config) {
        // 查询出无效的激活码
        WeworkOrderAccount reuse = weworkOrderAccountRepository.getReuseAccount(orgId, order.getCorpId());
        if (reuse == null) {
            log.warn("没有可以重新继承的激活码");
            return false;
        }
        reuseActiveAccount(reuse, openId, config);
        return true;
    }

    private void reuseActiveAccount(WeworkOrderAccount orderAccount, String openId, WechatWorkConfig config) {
        String accessToken = requireCropAccessToken();
        String handover = transformOpenId(config, orderAccount.getOpenId());
        String takeover = transformOpenId(config, openId);
        Map<String, Object> body = Map.of(
                "corpid", config.getCompanyId(),
                "transfer_list", List.of(Map.of("handover_userid", handover, "takeover_userid", takeover)));
        WechatWorkOrderAccountReuseDto dto = httpPost(String.format(URL_ACTIVE_REUSE_ACCOUNT, accessToken), body, WechatWorkOrderAccountReuseDto.class);
        if (dto == null || dto.getErrcode() != 0 || CollectionUtils.isNotEmpty(dto.getTransferResult())) {
            log.error("帐号继承失败");
            throw new BadRequestException();
        }
        WechatWorkOrderAccountReuseItemDto item = dto.getTransferResult().get(0);
        if (item.getErrcode() != 0 || !item.getTakeoverUserid().equals(takeover) || !item.getHandoverUserid().equals(handover)) {
            log.error("帐号继承失败");
            throw new BadRequestException();
        }
        WechatWorkOrderAccountUseStatusDto status = accountStatus(orderAccount, config);
        if (status.getStatus() == 2) {
            orderAccount.setOpenId(openId);
            orderAccount.setStatus(1);
            orderAccount.setActiveTime(new Date(status.getActiveTime() * 1000));
            orderAccount.setExpireTime(new Date(status.getExpireTime() * 1000));
            weworkOrderAccountRepository.save(orderAccount);
        } else {
            log.error("帐号继承失败");
            throw new BadRequestException();
        }
        log.info("帐号继承成功");
    }

    private boolean useAccount(Long orgId, WeworkOrder order, String openId, WechatWorkConfig config) {
        log.info("开始激活帐号，corpId={}, openId={}", order.getCorpId(), openId);
        WeworkOrderAccount notUse = weworkOrderAccountRepository.findFirstByOrgIdAndCorpIdAndStatus(orgId, order.getCorpId(), 0);
        if (notUse == null) {
            return false;
        }
        activeAccount(notUse, openId, config);
        return true;
    }

    private void activeAccount(WeworkOrderAccount orderAccount, String openId, WechatWorkConfig config) {
        String accessToken = requireCropAccessToken();
        String takeover = transformOpenId(config, openId);
        Map<String, Object> body = Map.of(
                "active_code", orderAccount.getActiveCode(),
                "corpid", config.getCompanyId(),
                "userid", takeover);
        WechatWorkAuthProvider.WechatWorkDto dto = httpPost(String.format(URL_ACTIVE_ACCOUNT, accessToken), body, WechatWorkAuthProvider.WechatWorkDto.class);
        if (dto == null || dto.getErrcode() != 0) {
            log.error("激活帐号失败，corpId={}, openId={}", orderAccount.getCorpId(), openId);
            throw new BadRequestException();
        }
        WechatWorkOrderAccountUseStatusDto status = accountStatus(orderAccount, config);
        if (status.getStatus() == 2) {
            orderAccount.setOpenId(openId);
            orderAccount.setStatus(1);
            orderAccount.setActiveTime(new Date(status.getActiveTime() * 1000));
            orderAccount.setExpireTime(new Date(status.getExpireTime() * 1000));
            weworkOrderAccountRepository.save(orderAccount);
        } else {
            log.error("激活帐号失败，corpId={}, openId={}", orderAccount.getCorpId(), openId);
            throw new BadRequestException();
        }
        log.info("激活帐号成功，corpId={}, openId={}", orderAccount.getCorpId(), openId);
    }

    private WechatWorkOrderAccountUseStatusDto accountStatus(WeworkOrderAccount orderAccount, WechatWorkConfig config) {
        String accessToken = requireCropAccessToken();
        Map<String, Object> body = Map.of(
                "active_code", orderAccount.getActiveCode(),
                "corpid", config.getCompanyId());
        WechatWorkOrderAccountUseDto dto = httpPost(String.format(URL_ACCOUNT_STATUS, accessToken), body, WechatWorkOrderAccountUseDto.class);
        if (dto == null || dto.getErrcode() != 0 || dto.getActiveInfo() == null) {
            log.error("获取激活码详情失败");
            throw new BadRequestException();
        }
        return dto.getActiveInfo();
    }


    private void applyOrderStatus(WeworkOrder order) {
        log.info("开始查询订单（{}）详情", order.getId());
        String accessToken = requireCropAccessToken();
        Map<String, Object> body = Map.of("order_id", order.getWeworkOrderId());
        WechatWorkQueryOrderDto dto = httpPost(String.format(URL_ORDER_DETAIL, accessToken), body, WechatWorkQueryOrderDto.class);
        if (dto == null || dto.getErrcode() != 0 || dto.getOrder() == null) {
            log.error("查询订单（{}）详情失败", order.getId());
            throw new BadRequestException();
        }
        order.setPayStatus(dto.getOrder().getOrderStatus());
        log.info("查询到订单（{}）的支付状态：{}", order.getId(), order.getPayStatus());
        weworkOrderRepository.save(order);
        if (WechatWorkOrderStatus.isSuccess(order.getPayStatus())) {
            // 保存激活码
            log.info("订单（{}）已支付，开始获取激活码", order.getId());
            saveAccount(order);
        } else {
            log.warn("订单（{}）未支付成功", order.getId());
        }
    }

    private void saveAccount(WeworkOrder order) {
        String accessToken = requireCropAccessToken();
        String next = null;
        boolean hasNext = true;
        while (hasNext) {
            Map<String, Object> body = new HashMap<>();
            body.put("order_id", order.getWeworkOrderId());
            if (next != null) {
                body.put("cursor", next);
            }
            WechatWorkOrderAccountDto dto = httpPost(String.format(URL_ORDER_ACTIVE_CODE, accessToken), body, WechatWorkOrderAccountDto.class);
            if (dto == null || dto.getErrcode() != 0) {
                log.error("获取订单中的帐号列表失败");
                throw new BadRequestException();
            }
            Optional.ofNullable(dto.getAccountList()).ifPresent(list -> {
                List<WeworkOrderAccount> accounts = list.stream().map(i -> new WeworkOrderAccount(order.getOrgId(), order.getId(), order.getCorpId(), i.getActiveCode())).collect(Collectors.toList());
                weworkOrderAccountRepository.saveAll(accounts);
                log.info("订单（{}）激活码已保存，（{}）个", order.getId(), accounts.size());
            });
            next = dto.getNextCursor();
            hasNext = dto.getHasMore() == 1;
        }
    }

    public WechatWorkConfig checkConfig(ThirdPartyUser thirdPartyUser) {
        ThirdPartyAuth auth = thirdPartyAuthService.getBySource(thirdPartyUser.getSource(), WECHAT_WORK, thirdPartyUser.getApp());
        return checkConfig(auth, true);
    }

    public WechatWorkConfig checkConfig(ThirdPartyAuth auth, boolean httpError) {
        WechatWorkConfig config = null;
        if (auth != null) {
            config = JsonHelper.toObject(auth.getConfig(), WechatWorkConfig.class);
        }
        if (config == null || StringUtils.isEmpty(config.getPermanentCode()) || StringUtils.isEmpty(config.getAgentId()) || StringUtils.isEmpty(config.getCompanyId())) {
            if (httpError) {
                throw ToastMessageHelper.badRequestException(AuthToastMessage.WECHAT_WORK_NO_CROP);
            } else {
                throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_NO_CROP);
            }
        }
        return config;
    }


    public <T> T httpPost(String url, Map<String, Object> body, Class<T> tClass) {
        try {
            String bodyString = JsonHelper.toJson(body);

            String response = Request.Post(url).connectTimeout(30000).socketTimeout(30000)
                    .bodyString(bodyString, ContentType.APPLICATION_JSON)
                    .execute().returnContent().toString();
            log.info("wechat work http post:\n" +
                    "url={}\n" +
                    "body={}\n" +
                    "response={}", url, bodyString, response);
            if (tClass.equals(String.class)) {
                return (T) response;
            } else {
                return JsonHelper.toObject(response, tClass);
            }
        } catch (IOException e) {
            log.error("wechat work post({}) http error ", url, e);
        }
        return null;
    }

    public <T> T httpGet(String url, Class<T> tClass) {
        try {
            String response = Request.Get(url).connectTimeout(30000).socketTimeout(30000)
                    .execute().returnContent().toString();
            log.info("wechat work http get:\n" +
                    "url={}\n" +
                    "response={}", url, response);
            return JsonHelper.toObject(response, tClass);
        } catch (IOException e) {
            log.error("wechat work get({}) http error ", url, e);
        }
        return null;
    }

    public WechatWorkProperty requireProperty() {
        return Optional.ofNullable(authProperties.getWechatWork()).orElseThrow(() -> ToastMessageHelper.badRequestException(AuthToastMessage.WECHAT_WORK_NOT_SUPPORT));
    }

    //***************************************************************************************************
    //**************************************** accessToken **********************************************
    //***************************************************************************************************

    private final Lock lockGetCorpAccessToken = new ReentrantLock();
    private final Lock lockGetSuiteAccessToken = new ReentrantLock();
    private final Lock lockGetOrgAccessToken = new ReentrantLock();

    public <X extends WechatWorkAuthProvider.WechatWorkAccessTokenDto> String getAccessToken0(String key, Lock lock, Supplier<X> getFromServer) {
        Function<String, String> getAccessTokenByCache = k -> stringRedisTemplate.opsForValue().get(key);
        String accessToken = getAccessTokenByCache.apply(key);
        if (StringUtils.isEmpty(accessToken)) {
            try {
                lock.lock();
                accessToken = getAccessTokenByCache.apply(key);
                if (StringUtils.isEmpty(accessToken)) {
                    X response = getFromServer.get();
                    if (response != null && response.getErrcode() == 0) {
                        accessToken = response.getAccessToken();
                        stringRedisTemplate.opsForValue().set(key, accessToken, Duration.ofSeconds(7000));
                    }
                }
            } catch (Throwable e) {
                log.error("企业微信服务器获取accessToken失败", e);
            } finally {
                lock.unlock();
            }
        }
        return accessToken;
    }

    /**
     * 应用的ticket
     */
    public String suiteTicketKey(String suiteId) {
        return String.format("wechat_open_suite_ticket:%s", suiteId);
    }

    /**
     * 应用的accessToken
     */
    private String suiteAccessTokenKey(String suiteId) {
        return String.format("wechat_open_suite_accesstoken:%s", suiteId);
    }

    /**
     * 服务商的accessToken
     */
    private String corpAccessTokenKey(String corpId) {
        return String.format("wechat_open_corp_accesstoken:%s", corpId);
    }

    /**
     * 企业的accessToken
     */
    public String orgAccessTokenKey(String orgCorpId) {
        return String.format("wechat_open_org_accesstoken:%s", orgCorpId);
    }

    /**
     * 服务商的token
     */
    public String getCropAccessToken() {
        WechatWorkProperty property = requireProperty();
        String key = corpAccessTokenKey(property.getCorpId());
        return getAccessToken0(key, lockGetCorpAccessToken, () -> {
            Map<String, Object> body = Map.of("corpid", property.getCorpId(), "provider_secret", property.getCorpSecret());
            return httpPost(URL_CORP_ACCESS_TOKEN, body, WechatWorkAuthProvider.WechatWorkCorpAccessTokenDto.class);
        });
    }

    public String requireCropAccessToken() {
        String accessToken = getCropAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            log.error("获取服务商的accessToken失败");
            throw new BadRequestException();
        }
        return accessToken;
    }

    public String requireSuiteAccessToken() {
        String accessToken = getSuitAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            log.error("获取应用的accessToken失败");
            throw new BadRequestException();
        }
        return accessToken;
    }

    /**
     * 第三方应用的token
     */
    public String getSuitAccessToken() {
        WechatWorkProperty property = requireProperty();
        String key = suiteAccessTokenKey(property.getSuiteId());
        return getAccessToken0(key, lockGetSuiteAccessToken, () -> {
            String suiteTicketKey = suiteTicketKey(property.getSuiteId());
            String suiteTicket = stringRedisTemplate.opsForValue().get(suiteTicketKey);
            if (StringUtils.isEmpty(suiteTicket)) {
                log.error("获取应用（{}）的ticket失败", property.getSuiteId());
                return null;
            }
            Map<String, Object> body = Map.of("suite_id", property.getSuiteId(), "suite_secret", property.getSuiteSecret(), "suite_ticket", suiteTicket);
            return httpPost(URL_SUITE_ACCESS_TOKEN, body, WechatWorkAuthProvider.WechatWorkSuiteAccessTokenDto.class);
        });
    }

    public String requireOrgAccessToken(WechatWorkConfig config) {
        String accessToken = getOrgAccessToken(config);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("获取企业的accessToken失败");
            throw new BadRequestException();
        }
        return accessToken;
    }


    /**
     * 第三方企业的token
     */
    public String getOrgAccessToken(WechatWorkConfig config) {
        WechatWorkProperty property = requireProperty();
        String key = orgAccessTokenKey(config.getCompanyId());
        return getAccessToken0(key, lockGetOrgAccessToken, () -> {
            String suiteAccessToken = getSuitAccessToken();
            if (StringUtils.isEmpty(suiteAccessToken)) {
                log.error("获取企业（{}）的accessToken失败", property.getSuiteId());
                throw new BadRequestException();
            }
            Map<String, Object> body = Map.of("auth_corpid", config.getCompanyId(), "permanent_code", config.getPermanentCode());
            String url = String.format(URL_ORG_ACCESS_TOKEN, suiteAccessToken);
            return httpPost(url, body, WechatWorkAuthProvider.WechatWorkOrgAccessTokenDto.class);
        });
    }

    public String transformCorpId(String corpId) {
        if (corpId.length() == 32) {
            return corpId;
        }
        String accessToken = getCropAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            return corpId;
        }
        Map<String, Object> body = Map.of("corpid", corpId);
        WechatWorkOpenCorpDto response = httpPost(String.format(URL_ORG_TRANSFORM_CORP_ID, accessToken), body, WechatWorkOpenCorpDto.class);
        if (response != null && response.getErrcode() == 0 && StringUtils.isNotEmpty(response.getOpenCorpId())) {
            return response.getOpenCorpId();
        }
        return corpId;
    }

    public String transformOpenId(WechatWorkConfig config, String openId) {
        if (openId.length() == 32) {
            return openId;
        }
        String accessToken = getOrgAccessToken(config);
        if (StringUtils.isEmpty(accessToken)) {
            return openId;
        }
        Map<String, Object> body = Map.of("userid_list", List.of(openId));
        WechatWorkOpenUserDto response = httpPost(String.format(URL_ORG_TRANSFORM_OPEN_ID, accessToken), body, WechatWorkOpenUserDto.class);
        if (response != null && response.getErrcode() == 0 && CollectionUtils.isNotEmpty(response.getOpenUserIdList())) {
            return response.getOpenUserIdList().get(0).getOpenUserId();
        }
        return openId;
    }

    @Getter
    @Setter
    public static class WechatWorkOpenCorpDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("open_corpid")
        private String openCorpId;
    }

    @Getter
    @Setter
    public static class WechatWorkOpenUserDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("open_userid_list")
        private List<WechatWorkOpenUserItemDto> openUserIdList;
        @JsonProperty("invalid_userid_list")
        private List<String> invalidUserIdList;
    }

    @Getter
    @Setter
    public static class WechatWorkOpenUserItemDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("userid")
        private String userId;
        @JsonProperty("open_userid")
        private String openUserId;
    }

    @Getter
    @Setter
    public static class WechatWorkAccountListDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("admin")
        private List<Map<String, Object>> admin;
    }

    @Getter
    @Setter
    public static class WechatWorkPlaceOrderDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("order_id")
        private String orderId;
    }

    @Getter
    @Setter
    public static class WechatWorkQueryOrderDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("order")
        private WechatWorkOrderDetailDto order;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderDetailDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("order_status")
        private int orderStatus;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderAccountDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("next_cursor")
        private String nextCursor;
        @JsonProperty("has_more")
        private int hasMore;
        @JsonProperty("account_list")
        private List<WechatWorkOrderAccountDetailDto> accountList;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderAccountDetailDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("active_code")
        private String activeCode;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderAccountUseDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("active_info")
        private WechatWorkOrderAccountUseStatusDto activeInfo;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderAccountUseStatusDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("status")
        private int status;
        @JsonProperty("active_time")
        private long activeTime;
        @JsonProperty("expire_time")
        private long expireTime;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderAccountReuseDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("transfer_result")
        private List<WechatWorkOrderAccountReuseItemDto> transferResult;
    }

    @Getter
    @Setter
    public static class WechatWorkOrderAccountReuseItemDto extends WechatWorkAuthProvider.WechatWorkDto {
        @JsonProperty("handover_userid")
        private String handoverUserid;
        @JsonProperty("takeover_userid")
        private String takeoverUserid;
        @JsonProperty("errcode")
        private int errcode;
    }

}
