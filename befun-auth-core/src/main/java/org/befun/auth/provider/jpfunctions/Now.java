package org.befun.auth.provider.jpfunctions;

import com.jayway.jsonpath.internal.EvaluationContext;
import com.jayway.jsonpath.internal.PathRef;
import com.jayway.jsonpath.internal.function.Parameter;
import com.jayway.jsonpath.internal.function.PathFunction;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class Now implements PathFunction {
    @Override
    public Object invoke(String currentPath, PathRef parent, Object model, EvaluationContext ctx, List<Parameter> parameters) {
        StringBuilder result = new StringBuilder();

        if (parameters != null) {
            for (Parameter param : parameters) {
                Object value = param.getValue();
                if (value != null) {
                    result.append(LocalDateTime.now().format(DateTimeFormatter.ofPattern(value.toString())));
                }

            }
        }


        return result.toString();
    }
}
