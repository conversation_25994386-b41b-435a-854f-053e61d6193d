package org.befun.auth.provider.saml;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.SamlProperty;
import org.befun.auth.service.auth.config.SamlConfig;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.converter.RsaKeyConverters;
import org.springframework.security.saml2.core.Saml2X509Credential;
import org.springframework.security.saml2.provider.service.authentication.*;
import org.springframework.security.saml2.provider.service.metadata.OpenSamlMetadataResolver;
import org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistration;
import org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrations;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import javax.net.ssl.*;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Service
@ConditionalOnProperty(name = "befun.auth.enable-provider", havingValue = "true")
public class SamlHelper {

    private static final Logger log = LoggerFactory.getLogger(SamlHelper.class);
    private final OpenSamlAuthenticationRequestFactory requestFactory = new OpenSamlAuthenticationRequestFactory();
    private final OpenSamlMetadataResolver metadataResolver = new OpenSamlMetadataResolver();
    private final OpenSamlAuthenticationProvider authenticationProvider = new OpenSamlAuthenticationProvider();
    private final SamlIdpMetadataParser idpMetadataParser = new SamlIdpMetadataParser();

    public String buildLoginRequest(String orgCode, SamlConfig config, SamlProperty property) {
        RelyingPartyRegistration relyingParty = registration(orgCode, config, property);
        Saml2AuthenticationRequestContext context = Saml2AuthenticationRequestContext.builder().issuer(relyingParty.getEntityId())
                .relyingPartyRegistration(relyingParty)
                .assertionConsumerServiceUrl(relyingParty.getAssertionConsumerServiceLocation())
                .relayState(UUID.randomUUID().toString()).build();
        Saml2RedirectAuthenticationRequest authenticationRequest = requestFactory.createRedirectAuthenticationRequest(context);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(authenticationRequest.getAuthenticationRequestUri());
        addParameter("SAMLRequest", authenticationRequest.getSamlRequest(), uriBuilder);
        addParameter("RelayState", authenticationRequest.getRelayState(), uriBuilder);
        addParameter("SigAlg", authenticationRequest.getSigAlg(), uriBuilder);
        addParameter("Signature", authenticationRequest.getSignature(), uriBuilder);
        String uriString = uriBuilder.build(true).toUriString();
        log.info("SAML login request: {}", uriString);
        return uriString;
    }

    private void addParameter(String name, String value, UriComponentsBuilder builder) {
        Assert.hasText(name, "name cannot be empty or null");
        if (StringUtils.isNotEmpty(value)) {
            builder.queryParam(UriUtils.encode(name, StandardCharsets.UTF_8),
                    UriUtils.encode(value, StandardCharsets.UTF_8));
        }
    }

    public DefaultSaml2AuthenticatedPrincipal loginPrincipal(String orgCode, SamlConfig config, SamlProperty property, String samlResponse) {
        try {
            RelyingPartyRegistration relyingParty = registration(orgCode, config, property);
            Saml2AuthenticationToken authenticationToken = new Saml2AuthenticationToken(relyingParty, samlResponse);
            log.info("SAML login  send token: {}", samlResponse);
            Saml2Authentication authentication = (Saml2Authentication) authenticationProvider.authenticate(authenticationToken);
            log.info("SAML login get principal: {}", JsonHelper.toJson(authentication.getPrincipal()));
        return (DefaultSaml2AuthenticatedPrincipal) authentication.getPrincipal();
        } catch (Exception e) {
            log.error("SAML login principal error: {}", e.getMessage());
            throw e;
        }

    }

    public String metadata(String orgCode, SamlConfig config, SamlProperty property) {
        RelyingPartyRegistration relyingParty = registration(orgCode, config, property);
        log.info("SAML metadata: {}", JsonHelper.toJson(relyingParty));
        return metadataResolver.resolve(relyingParty);
    }

    private void disableSSLVerification() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        // 安装全信任的信任管理器
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new java.security.SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

        // 忽略主机名验证
        HostnameVerifier allHostsValid = (hostname, session) -> true;
        HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
    }

    @SneakyThrows
    private void withSSL(String filePath) {
        // 加载证书文件
        FileInputStream fis = new FileInputStream(filePath);

        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) cf.generateCertificate(fis);

        // 创建 KeyStore 并将证书添加到 TrustManager
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);  // 初始化空的 keystore
        keyStore.setCertificateEntry("mycert", cert);

        // 初始化 TrustManager
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);

        // 设置 SSL 上下文
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), new java.security.SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
    }

    @SneakyThrows
    public RelyingPartyRegistration registration(String orgCode, SamlConfig config, SamlProperty property) {
        Map<String, Object> params = Map.of("orgCode", orgCode);
        RelyingPartyRegistration.Builder builder = buildFromIdp(config, property);
        builder.registrationId(orgCode);
        log.info("SAML orgCode: {}", orgCode);
        builder.entityId(TemplateEngine.renderTextTemplate2(property.getSpMetadataUrl(), params));
        log.info("SAML entityId: {}", TemplateEngine.renderTextTemplate2(property.getSpMetadataUrl(), params));
        builder.assertionConsumerServiceLocation(TemplateEngine.renderTextTemplate2(property.getSpUrl(), params));
        log.info("SAML assertionConsumerServiceLocation: {}", TemplateEngine.renderTextTemplate2(property.getSpUrl(), params));
        builder.assertionConsumerServiceBinding(config.getBinding());
        log.info("SAML assertionConsumerServiceBinding: {}", config.getBinding());
        builder.signingX509Credentials(c -> Optional.ofNullable(signing(property.getSpKey(), property.getSpCrt())).ifPresent(c::add));
        builder.decryptionX509Credentials(c -> Optional.ofNullable(decryption(property.getSpKey(), property.getSpCrt())).ifPresent(c::add));
        RelyingPartyRegistration registration = builder.build();
        return registration;
    }

    private Saml2X509Credential decryption(String key, String crt) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(crt)) {
            return null;
        }
        try {
            X509Certificate certificate = (X509Certificate) CertificateFactory.getInstance("X.509").generateCertificate(new ByteArrayInputStream(crt.getBytes(StandardCharsets.UTF_8)));
            RSAPrivateKey rsa = RsaKeyConverters.pkcs8().convert(new ByteArrayInputStream(key.getBytes(StandardCharsets.UTF_8)));
            return Saml2X509Credential.decryption(rsa, certificate);
        } catch (CertificateException e) {
            throw new RuntimeException(e);
        }
    }

    private Saml2X509Credential signing(String key, String crt) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(crt)) {
            return null;
        }
        try {
            X509Certificate certificate = (X509Certificate) CertificateFactory.getInstance("X.509").generateCertificate(new ByteArrayInputStream(crt.getBytes(StandardCharsets.UTF_8)));
            RSAPrivateKey rsa = RsaKeyConverters.pkcs8().convert(new ByteArrayInputStream(key.getBytes(StandardCharsets.UTF_8)));
            return Saml2X509Credential.signing(rsa, certificate);
        } catch (CertificateException e) {
            throw new RuntimeException(e);
        }
    }

    private RelyingPartyRegistration.Builder buildFromIdp(SamlConfig config, SamlProperty property) {
        if (StringUtils.isNotEmpty(config.getIdpMetadata())) {
            return idpMetadataParser.parse(config.getIdpMetadata());
        }
        try {
            if (StringUtils.isNotEmpty(property.getSslPath())) {
                withSSL(property.getSslPath());
            } else if (property.getIgnoreSSL()) {
                disableSSLVerification();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return RelyingPartyRegistrations.fromMetadataLocation(config.getIdpMetadataUrl());
    }
}
