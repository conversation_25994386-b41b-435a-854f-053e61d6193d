package org.befun.auth.provider.saml;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.SamlProperty;
import org.befun.auth.dto.LoginResponseDto;
import org.befun.auth.dto.LoginSSOVerifyResponseDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.service.*;
import org.befun.auth.service.auth.config.SamlConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantData;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.filter.RequestInfoContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.saml2.provider.service.authentication.DefaultSaml2AuthenticatedPrincipal;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.*;
import java.util.function.BiFunction;

import static org.befun.auth.constant.ThirdPartyAuthType.SAML;
import static org.befun.auth.service.AuthService.CACHE_LOGIN_RESPONSE_PREFIX;

@Slf4j
@Service("befun_auth_saml")
@ConditionalOnProperty(name = "befun.auth.enable-provider", havingValue = "true")
public class SamlProvider extends BaseAuthProvider {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private ThirdPartyAuthWhiteListService thirdPartyAuthWhiteListService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService userService;
    @Autowired
    private SamlHelper samlHelper;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;

    private SamlProperty property() {
        return authProperties.getSaml();
    }

    private SamlConfig requireConfig(ThirdPartyAuth auth) {
        return parseConfig(auth, true);
    }

    private SamlConfig parseConfig(ThirdPartyAuth auth, boolean throwable) {
        SamlConfig config = null;
        if (auth != null) {
            config = JsonHelper.toObject(auth.getConfig(), SamlConfig.class);
        }

        if (config != null) return config;

        if (throwable) {
            throw new BadRequestException("未设置saml登录方式");
        } else {
            return null;
        }
    }

    private String parseOrgCode(String source) {
        source = source.replaceFirst(SAML.getSourcePrefix(), "");
        if (SAML.getSourceSuffix() != null && source.endsWith(SAML.getSourceSuffix())) {
            return source.substring(0, source.length() - SAML.getSourceSuffix().length());
        }
        return source;
    }

    private Organization getOrg(String source) {
        String orgCode = parseOrgCode(source);
        return organizationService.getByOrgCode(orgCode);
    }

    private long requireOrg(String source) {
        Organization org = getOrg(source);
        if (org == null) {
            throw new BadRequestException("无效的企业编号");
        }
        return org.getId();
    }

    public String metadata(String source, String app) {
        return applyOrgCodeAndConfig(source, app,
                (orgCode, config) -> samlHelper.metadata(orgCode, config, property()));
    }

    @Override
    public Object verify(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        Organization org = getOrg(source);
        if (org == null) {
            return LoginSSOVerifyResponseDto.orgCodeError();
        }
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(org.getId(), SAML, app);
        SamlConfig config = parseConfig(auth, false);
        if (config == null || (StringUtils.isEmpty(config.getIdpMetadataUrl()) && StringUtils.isEmpty(config.getIdpMetadata()))) {
            return LoginSSOVerifyResponseDto.notConfigError();
        }
        return LoginSSOVerifyResponseDto.success();
    }


    @Override
    public String authorize(String source, String app) {
        String url = applyOrgCodeAndConfig(source, app,
                (orgCode, config) -> samlHelper.buildLoginRequest(orgCode, config, property()));
        log.info("saml authorize url: {}", url);
        return url;
    }

    @Override
    public Object login(String source, String app, Object callback) {
        String saml2Response = (String) callback;
        log.info("saml2Response: {}", saml2Response);
        try {
            saml2Response = new String(Base64.getDecoder().decode(saml2Response));
        } catch (Throwable ignore) {
            // ignore saml2Response is not base64
        }
        String orgCode = parseOrgCode(source);
        long orgId = requireOrg(orgCode);
        String finalSource = SAML.getSourcePrefix() + orgId;
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, SAML, app);
        SamlConfig config = requireConfig(auth);
        SamlProperty property = property();
        DefaultSaml2AuthenticatedPrincipal principal = samlHelper.loginPrincipal(orgCode, config, property, saml2Response);
        log.info("principal getAttributes: {}", JsonHelper.toJson(principal.getAttributes()));
        Map<String, Object> principalParams = JsonHelper.toMap(principal.getAttributes());
        String openId = (String) parseParams(principalParams, config.getParamId());
        if (StringUtils.isEmpty(openId)) {
            openId = principal.getName();
        }
        LoginResponseDto responseDto = null;
        List<String> roleCodes = (List<String>) parseParams(principalParams, config.getParamRoleCode());
        String employeeNo = (String) parseParams(principalParams, config.getParamEmployeeNo());
        String[] departmentArray = config.getParamDepartment().split("&");
        Set<String> departmentCode = new HashSet<>((List<String>) parseParams(principalParams, departmentArray[0]));

        if (departmentArray.length > 1) {
            List<String> secondCode = (List<String>) parseParams(principalParams, departmentArray[1]);
            Set<String> cartesianProduct = new HashSet<>();
            for (String first : departmentCode) {
                for (String second : secondCode) {
                    cartesianProduct.add(first + second);
                }
            }
            departmentCode = cartesianProduct;
        }

        log.info("openId: {}, roleCodes: {}, employeeNo: {}, departmentCode: {}", openId, roleCodes, employeeNo, departmentCode);

        if (ignoreWhiteList(auth) || thirdPartyAuthWhiteListService.isInWhiteList(auth.getId(), openId)) {
            ThirdPartyUser thirdPartyUser = thirdPartyUserService.getBySourceAppOpenId(finalSource, app, openId);
            if (thirdPartyUser != null) {
                responseDto = loginByThirdPartyUser(thirdPartyUser, roleCodes, app);
            } else {
                String name = (String) parseParams(principalParams, config.getParamName());
                String email = (String) parseParams(principalParams, config.getParamEmail());
                String mobile = (String) parseParams(principalParams, config.getParamMobile());
                User user = userService.addUserByThirdParty(auth, openId, name == null ? openId : name, mobile, email, departmentCode, employeeNo);
                responseDto = checkLoginInfo(user, roleCodes, app);
            }
        } else {
            log.warn("第三方cas登录失败，账号未加入白名单，source={}, app={}, openId={}", source, app, openId);
        }

        if (responseDto != null) {
            User user = userService.get(responseDto.getUserId());
            if (StringUtils.isEmpty(user.getEmployeeNo()) && StringUtils.isNotEmpty(employeeNo)) {
                user.setEmployeeNo(employeeNo);
                userService.save(user);
            }

            updateUserRole(user.getOrgId(), roleCodes, departmentCode, user.getId());
            RequestInfoContext.Data requestInfo = RequestInfoContext.get();
            if (requestInfo.getTenantData() == null) {
                TenantData tenantData = new TenantData();
                tenantData.setOrgId(orgId);
                tenantData.setUserId(user.getId());
                tenantData.setDepartmentIds(user.parseDepartmentIds2());
                requestInfo.setTenantData(tenantData);
            }
            if (user.parseDepartmentIds2().isEmpty()) {
                throw new BadRequestException(String.format("没有匹配到有效部门-%s", JsonHelper.toJson(departmentCode)));
            }
            log.info("saml login success, dto: {}", JsonHelper.toJson(responseDto));
            return cacheLoginResponse(property, responseDto);
        }
        throw new BadRequestException("saml登录失败");
    }

    /**
     * saml 响应会直接请求到后端的登录地址，这里把登录成功的信息，临时缓存起来，并把这个key返回给前端，前端收到这个key后，再请求登录成功的数据（请求完立即删除，只能请求一次）
     */
    private String cacheLoginResponse(SamlProperty property, LoginResponseDto responseDto) {
        String token = UUID.randomUUID().toString();
        String key = CACHE_LOGIN_RESPONSE_PREFIX + token;
        redisTemplate.opsForValue().set(key, JsonHelper.toJson(responseDto), Duration.ofMinutes(5));
        return TemplateEngine.renderTextTemplate2(property.getLoginUrl(), Map.of("token", token));
    }

    private boolean ignoreWhiteList(ThirdPartyAuth auth) {
        return auth == null || auth.getEnableWhiteList() == null || auth.getEnableWhiteList() != 1;
    }

    private List<String> getRoleCodes(Map<String, List<Object>> attributes, String key) {
        return parseRoleCodes(attributes.get(key));
    }

    private String getThirdPartyParams(Map<String, List<Object>> attributes, String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        List<Object> values = attributes.get(key);
        if (CollectionUtils.isNotEmpty(values)) {
            Object value = values.get(0);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    private <X> X applyOrgCodeAndConfig(String source, String app, BiFunction<String, SamlConfig, X> apply) {
        String orgCode = parseOrgCode(source);
        long orgId = requireOrg(orgCode);
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, SAML, app);
        SamlConfig config = requireConfig(auth);
        return apply.apply(orgCode, config);
    }

    /**
     * 以ums的角色为准，更新用户角色
     */
    private void updateUserRole(Long orgId, List<String> roleCodes, Set<String> departmentCode, Long userId) {
        try {
            Optional.ofNullable(roleService.getByOrgIdAndCodes(orgId, roleCodes)).ifPresent(roles -> {
                roleService.getByUserId(userId).forEach(r -> userRoleService.deleteUserRole(userId, r.getId()));
                roles.forEach(role -> userRoleService.addUserRole(userId, role.getId()));
            });

            User user = userService.require(userId);
            user.setFormatDepartmentIds(null);
            departmentCode.forEach(code -> {
                Optional.ofNullable(departmentService.getByEquivalentCode(orgId, code))
                        .ifPresent(departments -> departments.forEach(d -> {
                            user.appendDepartmentId(d.getId());
                        }));
            });
            departmentService.clearCache(orgId);
            userService.save(user);

        } catch (Exception e) {
            log.error("updateUserRole error", e);
        }
    }
}
