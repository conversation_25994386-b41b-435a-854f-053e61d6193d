package org.befun.auth.provider.local;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.VerifyCodeType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.*;
import org.befun.auth.dto.auth.authcode.AuthCodeCache;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.provider.ILoginStatus;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.AuthService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.auth.utils.PasswordLoginHelper;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.function.Function;
import java.util.function.Supplier;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.auth.constant.VerifyCodeUseFor.*;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

@Slf4j
public abstract class VerifyCodeProvider extends BaseAuthProvider implements ILoginStatus<LoginVerifyCodeRequestDto> {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PasswordLoginHelper passwordLoginHelper;
    @Lazy
    @Autowired
    private AuthService authService;

    public abstract VerifyCodeType getVerifyCodeType();

    public abstract User getUser(String account);

    public abstract boolean bind(Long currentUserId, String account);

    public abstract boolean hasUser(String account);

    public abstract boolean sendCode(VerifyCodeUseFor useFor, String account, String code);

    public abstract BadRequestException existException(VerifyCodeUseFor useFor);

    public abstract BadRequestException notExistException(VerifyCodeUseFor useFor);

    @Override
    public boolean hasActiveToken(LoginVerifyCodeRequestDto dto, String app) {
        User user = getUser(dto.getAccount());
        if (user == null) {
            throw notExistException(LOGIN);// 该手机号未注册，请注册 || 该邮箱未注册
        }
        String key = redisKey(LOGIN, getVerifyCodeType(), dto.getAccount());
        if (!verifyCodeFromRedis(key, dto.getCode())) {
            throw badRequestException(VERIFY_CODE_CHECK_ERROR);
        }
        return checkInfoAndHasActiveToken(dto.getPlatform(), user, app);
    }

    public <R> R verifyCodeAndApply(User user, VerifyCodeUseFor useFor, String account, String code,
                                    Supplier<R> successApply,
                                    Function<R, Boolean> applySuccess) {
        String key = redisKey(useFor, getVerifyCodeType(), account);
        if (!verifyCodeFromRedis(key, code)) {
            passwordLoginHelper.incrementFailTimes(user, LOGIN_CODE_ERROR_TIMES);
            throw badRequestException(VERIFY_CODE_CHECK_ERROR);
        }
        R r = successApply.get();
        if (applySuccess.apply(r)) {
            clearVerifyCode(key, code);
        }
        return r;
    }

    public <R> R verifyCodeAndApply(VerifyCodeUseFor useFor, String account, String code,
                                    Supplier<R> successApply,
                                    Function<R, Boolean> applySuccess) {
        String key = redisKey(useFor, getVerifyCodeType(), account);
        if (!verifyCodeFromRedis(key, code)) {
            throw badRequestException(VERIFY_CODE_CHECK_ERROR);
        }
        R r = successApply.get();
        if (applySuccess.apply(r)) {
            clearVerifyCode(key, code);
        }
        return r;
    }

    @Override
    public Object login(String source, String app, Object callback) {
        LoginVerifyCodeRequestDto dto = (LoginVerifyCodeRequestDto) callback;
        // v1.9.9 新增授权码
        AuthCodeCache cache = authService.checkAuthCodeByBind(dto.getAuthCode());
        User user = getUser(dto.getAccount());
        if (user == null) {
            throw notExistException(LOGIN);// 该手机号未注册，请注册 || 该邮箱未注册
        }
        // 登录次数限制
        int failTimes = passwordLoginHelper.checkFailTimes(user);
        // 图形验证码校验
        passwordLoginHelper.checkGraphCaptcha(dto.getCaptcha(), failTimes);
        return verifyCodeAndApply(user, LOGIN, dto.getAccount(), dto.getCode(),
                () -> {
                    if (cache != null && authService.countUserBinding(app, cache.getSource(), user) > 0) {
                        throw existException(BIND);
                    }
                    return checkLoginInfo(dto.getPlatform(), user, app);
                },
                i -> {
                    authService.authCodeBindUser(user, cache);
                    passwordLoginHelper.clearGraphCaptcha(dto.getCaptcha());
                    return true;
                });
    }

    @Override
    public boolean bind(String app, String source, Object accountInfo) {
        BindVerifyCodeRequestDto dto = (BindVerifyCodeRequestDto) accountInfo;
        if (dto.getOrgId() == null || dto.getUserId() == null) {
            log.error("没有登录信息");
            throw badRequestException(LOGIN_REQUIRE);
        }
        return verifyCodeAndApply(BIND, dto.getAccount(), dto.getCode(),
                () -> bind(dto.getUserId(), dto.getAccount()),
                i -> i);
    }

    @Override
    public boolean resetPasswordByVerifyCode(String app, String source, Object account) {
        ResetPasswordRequestDto dto = (ResetPasswordRequestDto) account;
        return verifyCodeAndApply(RESET_PASSWORD, dto.getAccount(), dto.getCode(),
                () -> {
                    User user = getUser(dto.getAccount());
                    if (user == null) {
                        throw badRequestException(ACCOUNT_NOT_EXISTS);
                    }
                    user.setPassword(PasswordHelper.encrypt(dto.getNewPassword()));
                    user.setPasswordStrength(PasswordHelper.passwordStrength(dto.getNewPassword()));
                    userRepository.save(user);
                    return true;
                },
                i -> i);
    }

    @Override
    public boolean sendVerifyCode(String source, String app, VerifyCodeUseFor useFor, Object account) {
        SendVerifyCodeRequestDto dto = (SendVerifyCodeRequestDto) account;
        String sendAccount = dto.getAccount();
        if (LOGIN == useFor || RESET_PASSWORD == useFor) {
            // 必须存在这个账号
            if (!hasUser(dto.getAccount())) {
                throw notExistException(useFor);// 该手机号未注册，请注册 || 该邮箱未注册
            }
        } else if (BIND == useFor || VerifyCodeUseFor.REGISTER == useFor) {
            // 此账号必须不存在
            if (hasUser(dto.getAccount())) {
                throw existException(useFor); // 手机号已存在 || 邮箱已存在
            }
        } else if (VerifyCodeUseFor.MFA == useFor) {
            //
            sendAccount = passwordLoginHelper.getMfaLoginAccount(dto.getAccount());
            if (StringUtils.isEmpty(sendAccount)) {
                throw new BadRequestException("验证已超时");
            }
        }
        String keySuffix = StringUtils.isEmpty(dto.getUniqueKey()) ? sendAccount : dto.getUniqueKey();
        String key = redisKey(useFor, getVerifyCodeType(), keySuffix);
        return sendVerifyCode(key, useFor, sendAccount);
    }

    @Override
    public boolean verifyCodeStatus(String source, String app, VerifyCodeUseFor useFor, Object account) {
        VerifyCodeStatusRequestDto dto = (VerifyCodeStatusRequestDto) account;
        String sendAccount = dto.getAccount();
        if (VerifyCodeUseFor.MFA == useFor) {
            sendAccount = passwordLoginHelper.getMfaLoginAccount(dto.getAccount());
            if (StringUtils.isEmpty(sendAccount)) {
                throw badRequestException(VERIFY_CODE_EXPIRED);
            }
        }
        String keySuffix = StringUtils.isEmpty(dto.getUniqueKey()) ? sendAccount : dto.getUniqueKey();
        String key = redisKey(useFor, getVerifyCodeType(), keySuffix);
        return verifyCodeFromRedis(key, dto.getCode());
    }

    public boolean sendVerifyCode(String key, VerifyCodeUseFor useFor, String account) {
        String freezeKey = redisFreezeKey(key);
        if (isFreeze(freezeKey)) {
            throw badRequestException(VERIFY_CODE_SEND_FREEZE);
        }
        String code = RandomStringUtils.randomNumeric(6);
        boolean send = sendCode(useFor, account, code);
        if (send) {
            cacheCodeToRedis(key, freezeKey, code);
            log.info("验证码发送成功：{}, {}", account, code);
        }
        return send;
    }

    public boolean mockSendVerifyCode(VerifyCodeUseFor useFor, String account, String uniqueKey) {
        String keySuffix = StringUtils.isEmpty(uniqueKey) ? account : uniqueKey;
        String key = redisKey(useFor, getVerifyCodeType(), keySuffix);
        String freezeKey = redisFreezeKey(key);
        if (isFreeze(freezeKey)) {
            throw badRequestException(VERIFY_CODE_SEND_FREEZE);
        }
        String code = "999999";
        cacheCodeToRedis(key, freezeKey, code);
        return true;
    }

    public String redisKey(VerifyCodeUseFor useFor, VerifyCodeType type, String account) {
        // verify_code:login:mobile:***********
        // verify_code:bind:mobile:***********
        // verify_code:bind:email:<EMAIL>
        return String.format("verify_code:%s:%s:%s", useFor.getValue(), type.name(), account);
    }

    private String redisFreezeKey(String key) {
        return String.format("%s-freeze", key);
    }

    private String redisCacheKey(String key, String code) {
        return String.format("%s-cache-%s", key, code);
    }

    private void cacheCodeToRedis(String key, String freezeKey, String code) {
        redisTemplate.opsForValue().set(key, code, Duration.ofMinutes(authProperties.getVerifyCode().getCodeExpireMinutes()));
        redisTemplate.opsForValue().set(freezeKey, "1", Duration.ofMinutes(authProperties.getVerifyCode().getCodeFreezeMinutes()));
        redisTemplate.opsForValue().set(redisCacheKey(key, code), "1", Duration.ofHours(authProperties.getVerifyCode().getCodeCacheHours()));
    }

    private boolean isFreeze(String freezeKey) {
        Boolean has = redisTemplate.hasKey(freezeKey);
        return has != null && has;
    }

    public boolean verifyCodeFromRedis(String key, String code) {
        Object serverCode = redisTemplate.opsForValue().get(key);
        boolean r = serverCode != null && serverCode.toString().equals(code);
        if (r) {
            return true;
        }
        Boolean hasKey = redisTemplate.hasKey(redisCacheKey(key, code));
        if (hasKey != null && hasKey) {
            throw badRequestException(VERIFY_CODE_EXPIRED);
        }
        return false;
    }

    public void clearVerifyCode(String key, String code) {
        redisTemplate.delete(key);
        redisTemplate.delete(redisCacheKey(key, code));
    }
}
