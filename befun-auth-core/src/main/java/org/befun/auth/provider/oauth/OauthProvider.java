package org.befun.auth.provider.oauth;

import cn.hutool.core.date.DateUtil;
import com.jayway.jsonpath.DocumentContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.fluent.Form;
import org.apache.http.client.fluent.Request;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.util.EntityUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.dto.LoginSSOVerifyResponseDto;
import org.befun.auth.dto.auth.oauth.OauthRequestDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.service.*;
import org.befun.auth.service.auth.config.OauthConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ThirdPartyAuthType.OAUTH;

@Slf4j
@Service("befun_auth_oauth")
@ConditionalOnProperty(name = "befun.auth.enable-provider", havingValue = "true")
public class OauthProvider extends BaseAuthProvider {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private ThirdPartyAuthWhiteListService thirdPartyAuthWhiteListService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService userService;


    private OauthConfig requireConfig(ThirdPartyAuth auth) {
        return parseConfig(auth, true);
    }


    private OauthConfig parseConfig(ThirdPartyAuth auth, boolean throwable) {
        OauthConfig config = null;
        if (auth != null) {
            config = JsonHelper.toObject(auth.getConfig(), OauthConfig.class);
        }

        if (config != null) return config;

        if (throwable) {
            throw new BadRequestException("未设置oauth登录方式");
        } else {
            return null;
        }
    }

    private String parseOrgCode(String source) {
        source = source.replaceFirst(OAUTH.getSourcePrefix(), "");
        if (OAUTH.getSourceSuffix() != null && source.endsWith(OAUTH.getSourceSuffix())) {
            return source.substring(0, source.length() - OAUTH.getSourceSuffix().length());
        }
        return source;
    }

    private Organization getOrg(String source) {
        String orgCode = parseOrgCode(source);
        return organizationService.getByOrgCode(orgCode);
    }

    private long requireOrg(String source) {
        Organization org = getOrg(source);
        if (org == null) {
            throw new BadRequestException("无效的企业编号");
        }
        return org.getId();
    }

    @Override
    public Object verify(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        Organization org = getOrg(source);
        if (org == null) {
            return LoginSSOVerifyResponseDto.orgCodeError();
        }
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(org.getId(), OAUTH, app);
        OauthConfig config = parseConfig(auth, false);
        if (config == null
                || StringUtils.isEmpty(config.getAuthorizeUrl())
                || StringUtils.isEmpty(config.getResourceConfig().getUrl())
        ) {
            return LoginSSOVerifyResponseDto.notConfigError();
        }
        return LoginSSOVerifyResponseDto.success();
    }

    @Override
    public String authorize(String source, String app) {
        String orgCode = parseOrgCode(source);
        long orgId = requireOrg(orgCode);
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, OAUTH, app);
        OauthConfig config = requireConfig(auth);
        return config.getAuthorizeUrl();
    }


    private Map<String, Object> paramsToParams(Map<String, Object> params, OauthRequestDto tokenConfig) {
        Map<String, Object> tokenInfo = new HashMap<>();
        if (StringUtils.isEmpty(tokenConfig.getUrl())) {
            tokenInfo = params;
        } else {
            HashMap<String, Object> body = new HashMap<>();
            params.putAll(buildDate());
            tokenConfig.getBody().forEach((k, v) -> {
                Object value = JP.parse(params).read(v.toString());
                if (value != null) {
                    v = value.toString();
                }
                body.put(k, v);
            });
            tokenConfig.setBody(body);
            tokenInfo.putAll(JP.parse(httpRequest(params, tokenConfig)).json());
        }
        log.info("oauth code to token response: {}", JsonHelper.toJson(tokenInfo));
        return tokenInfo;
    }

    private String httpRequest(Map<String, Object> context, OauthRequestDto requestDto) {
        try {

            Request request = null;
            String responseBody = null;

            if (HttpMethod.GET.equals(requestDto.getMethod())) {
                URIBuilder uriBuilder;
                uriBuilder = new URIBuilder(requestDto.getUrl());

                for (Map.Entry<String, Object> entry : requestDto.getBody().entrySet()) {
                    uriBuilder.addParameter(entry.getKey(), entry.getValue().toString());
                }
                log.info("oauth http request method:{}-url:{}", requestDto.getMethod(), uriBuilder.build().toString());
                request = Request.Get(uriBuilder.build().toString());
            } else {
                log.info("oauth http request method:{}->type:{}:->url:{}->body:{}",
                        requestDto.getMethod(),
                        requestDto.getContentType(),
                        requestDto.getUrl(),
                        JsonHelper.toJson(requestDto.getBody())
                );
                switch (requestDto.getContentType()) {
                    case FORM_DATA:
                        Form data = Form.form();
                        requestDto.getBody().forEach((k, v) -> {
                            data.add(k, v.toString());
                        });
                        request = Request.Post(requestDto.getUrl()).bodyForm(data.build());
                        break;
                    case XML:
                        request = Request.Post(requestDto.getUrl()).bodyString(JsonHelper.toJson(requestDto.getBody()), ContentType.APPLICATION_XML);
                        break;
                    default:
                        request = Request.Post(requestDto.getUrl()).bodyString(JsonHelper.toJson(requestDto.getBody()), ContentType.APPLICATION_JSON);
                }
            }

            log.info("oauth http request headers:{}", JsonHelper.toJson(requestDto.getHeaders()));
            for (Map.Entry<String, String> set : requestDto.getHeaders().entrySet()) {
                String v = set.getValue();
                Object value = JP.parse(context).read(v);
                if (value != null) {
                    v = value.toString();
                }
                request.addHeader(set.getKey(), v);
            }

            HttpResponse response = request.execute().returnResponse();
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == HttpStatus.SC_OK) {
                responseBody = EntityUtils.toString(response.getEntity());
            } else {
                responseBody = EntityUtils.toString(response.getEntity());
                throw new BadRequestException("oauth http request 失败，Status code: " + statusCode + ", Response body: " + responseBody);
            }

            return responseBody;
        } catch (Exception e) {
            log.error("oauth http request error", e);
            throw new BadRequestException("oauth http request 失败");
        }
    }

    @Override
    public Object login(String source, String app, Object callback) {
        Map<String, Object> params = ((Map<String, String[]>) callback).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().length > 0 ? entry.getValue()[0] : null
        ));
        log.info("oauth登录回调 source={}, app={}, params={}", source, app, JsonHelper.toJson(params));
        try {
            String orgCode = parseOrgCode(source);
            long orgId = requireOrg(orgCode);
            String finalSource = OAUTH.getSourcePrefix() + orgId;
            ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, OAUTH, app);
            OauthConfig config = requireConfig(auth);

            params = paramsToParams(params, config.getTokenConfig());
            Map<String, Object> assertion = paramsToParams(params, config.getResourceConfig());

            DocumentContext context = JP.parse(assertion);
            String openId = parseThirdPartyParams(context, config::getParamId);

            if (openId != null && assertion != null) {
                if (ignoreWhiteList(auth) || thirdPartyAuthWhiteListService.isInWhiteList(auth.getId(), openId)) {
                    ThirdPartyUser thirdPartyUser = thirdPartyUserService.getBySourceAppOpenId(finalSource, app, openId);
                    List<String> roleCodes = getRoleCodes(context, config::getParamRoleCode);
                    if (thirdPartyUser != null) {
                        return loginByThirdPartyUser(thirdPartyUser, roleCodes, app);
                    } else {
                        String name = parseThirdPartyParams(context, config::getParamName);
                        String email = parseThirdPartyParams(context, config::getParamEmail);
                        String mobile = parseThirdPartyParams(context, config::getParamMobile);
                        Set<String> departmentCode = Set.of(parseThirdPartyParams(context, config::getParamDepartment));
                        String employeeNo = parseThirdPartyParams(context, config::getParamEmployeeNo);
                        User user = userService.addUserByThirdParty(auth, openId, name == null ? openId.substring(0, 20) : name, mobile, email, departmentCode, employeeNo);
                        return checkLoginInfo(user, roleCodes, app);
                    }
                } else {
                    log.warn("第三方oauth登录失败，账号未加入白名单，source={}, app={}, openId={}", source, app, openId);
                }
            }
        } catch (Exception e) {
            log.error("第三方oauth登录失败 source={}, app={}", source, app, e);
            if (e instanceof BadRequestException) {
                throw (BadRequestException) e;
            }
        }
        throw new BadRequestException("oauth登录失败");
    }

    private boolean ignoreWhiteList(ThirdPartyAuth auth) {
        return auth == null || auth.getEnableWhiteList() == null || auth.getEnableWhiteList() != 1;
    }

    private List<String> getRoleCodes(DocumentContext context, Supplier<String> getKey) {
        return parseRoleCodes(parseThirdPartyParams0(context, getKey));
    }

    private String parseThirdPartyParams(DocumentContext context, Supplier<String> getKey) {
        Object param = parseThirdPartyParams0(context, getKey);
        if (param != null) {
            return param.toString();
        }
        return null;
    }

    private Object parseThirdPartyParams0(DocumentContext context, Supplier<String> getKey) {
        String key = getKey.get();
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return context.read(key);
    }

    private HashMap<String, Object> buildDate() {
        HashMap<String, Object> date = new HashMap<>();
        Date now = new Date();
        date.put("TIMESTAMP", now.getTime());
        date.put("DATE", DateUtil.format(now, "yyyy-MM-dd"));
        date.put("TIME", DateUtil.format(now, "HH:mm:ss"));
        date.put("DATETIME", DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        date.put("DATETIMET", DateUtil.format(now, "yyyy-MM-dd'T'HH:mm:ss"));
        return date;
    }

}
