package org.befun.auth.provider;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.AuthPingResponseDto;
import org.befun.auth.dto.ThirdPartyBindDto;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
public interface IAuthProvider {

    /* authorize by source */
    default String authorize(String source, String app) {
        throw new NotImplementedException("");
    }

    /* login by source */
    Object login(String source, String app, Object callback);


    /* send verify code */
    default boolean sendVerifyCode(String source, String app, VerifyCodeUseFor useFor, Object account) {
        throw new NotImplementedException("");
    }

    /* verify code status */
    default boolean verifyCodeStatus(String source, String app, VerifyCodeUseFor useFor, Object account) {
        throw new NotImplementedException("");
    }

    /* bind by info */
    default boolean bind(String app, String source, Object accountInfo) {
        throw new NotImplementedException("");
    }

    /* resetPassword by verifyCode */
    default boolean resetPasswordByVerifyCode(String app, String source, Object account) {
        throw new NotImplementedException("");
    }

    /* bind by qrcode */
    default ThirdPartyBindDto bindByQrCode(String app, String source, String bindId) {
        throw new NotImplementedException("");
    }

    /* system ping */
    default AuthPingResponseDto ping(String app, HttpServletRequest request, HttpServletResponse response) {
        throw new NotImplementedException("");
    }

    /* provider验证：比如微信的服务器验证类接口，默认实现是啥都不做 */
    default Object verify(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        throw new NotImplementedException("");
    }

    /* 响应provider消息 */
    default void onMessage(String app, HttpServletRequest request, String requestBody, HttpServletResponse response) {
        throw new NotImplementedException("");
    }

    /* 通知provider用户 */
    default void notifyUser(String app, String openId, String templateName, Map<String, Object> parameters) {
        throw new NotImplementedException("");
    }

    default String authCodeByQrCode(String app, String source, String code) {
        throw new NotImplementedException("");
    }
}
