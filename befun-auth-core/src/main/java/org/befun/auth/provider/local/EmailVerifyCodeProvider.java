package org.befun.auth.provider.local;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.VerifyCodeType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.entity.User;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.UserService;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.service.MailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.auth.constant.VerifyCodeUseFor.*;
import static org.befun.auth.constant.VerifyCodeUseFor.RESET_PASSWORD;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

@Slf4j
@Service("befun_auth_email")
public class EmailVerifyCodeProvider extends VerifyCodeProvider {

    @Autowired
    private MailService mailService;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserService userService;

    @Override
    public VerifyCodeType getVerifyCodeType() {
        return VerifyCodeType.EMAIL;
    }

    @Override
    @Transactional
    public boolean bind(Long currentUserId, String account) {
        if (userService.existsUser(currentUserId, getUser(account))) {
            throw existException(BIND);
        }
        User user = requireUserById(currentUserId);
        user.setEmail(account);
        userRepository.save(user);
        return true;
    }

    @Override
    public User getUser(String account) {
        return userRepository.findFirstByEmail(account);
    }

    @Override
    public boolean hasUser(String account) {
        return userRepository.existsByEmail(account);
    }

    @Override
    public boolean sendCode(VerifyCodeUseFor useFor, String email, String code) {
        boolean r = false;
        try {
            String templateName = authProperties.getVerifyCode().getEmailTemplateName().get(useFor.getValue());
            if (StringUtils.isNotEmpty(templateName)) {
                mailService.sendMessageByTemplate(templateName, email, Map.of("code", code));
                r = true;
            } else {
                log.error("邮箱模板不存在：useFor={}，email={}", useFor.getValue(), email);
            }
        } catch (Exception e) {
            log.error("发送邮件验证码失败：useFor={}，email={}", useFor.getValue(), email, e);
        }
        if (r) {
            return true;
        }
        throw badRequestException(VERIFY_CODE_SEND_FAIL);
    }


    @Override
    public BadRequestException existException(VerifyCodeUseFor useFor) {
        if (useFor == REGISTER) {
            return badRequestException(EMAIL_EXISTS_USE_REGISTER);
        } else if (useFor == BIND) {
            return badRequestException(EMAIL_EXISTS_USE_BIND);
        }
        return badRequestException(EMAIL_EXISTS);
    }

    @Override
    public BadRequestException notExistException(VerifyCodeUseFor useFor) {
        if (useFor == LOGIN) {
            return badRequestException(EMAIL_NOT_EXISTS_USE_LOGIN);
        } else if (useFor == RESET_PASSWORD) {
            return badRequestException(EMAIL_NOT_EXISTS_USE_RESET_PASSWORD);
        }
        return badRequestException(EMAIL_NOT_EXISTS);
    }
}
