package org.befun.auth.provider.local;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.VerifyCodeType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.entity.User;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.UserService;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.constant.AccountSafetyType;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.auth.constant.VerifyCodeUseFor.*;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

@Slf4j
@Service("befun_auth_mobile")
public class MobileVerifyCodeProvider extends VerifyCodeProvider {

    @Autowired
    private SmsService smsService;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserService userService;

    @Override
    public VerifyCodeType getVerifyCodeType() {
        return VerifyCodeType.MOBILE;
    }

    @Override
    @Transactional
    public boolean bind(Long currentUserId, String account) {
        if (userService.existsUser(currentUserId, getUser(account))) {
            throw notExistException(BIND);
        }
        User user = requireUserById(currentUserId);
        user.setMobile(account);
        userRepository.save(user);
        return true;
    }

    @Override
    public User getUser(String account) {
        return userRepository.findFirstByMobile(account);
    }

    @Override
    public boolean hasUser(String account) {
        return userRepository.existsByMobile(account);
    }

    @Override
    public boolean sendCode(VerifyCodeUseFor useFor, String mobile, String code) {
        boolean r = false;
        try {
            //if (useFor == REGISTER) {
            //     checkMobileBlacklist(mobile);
            // }
            String templateName = authProperties.getVerifyCode().getSmsTemplateName().get(useFor.getValue());
            if (StringUtils.isNotEmpty(templateName)) {
                r = smsService.sendMessageByTemplate(templateName, mobile, Map.of("code", code));
            } else {
                log.error("短信模板不存在：useFor={}，mobile={}", useFor.getValue(), mobile);
            }
        } catch (BadRequestException ee) {
            throw ee;
        } catch (Exception e) {
            log.error("发送短信验证码失败：useFor={}，mobile={}", useFor.getValue(), mobile, e);
        }
        if (r) {
            return true;
        }
        throw badRequestException(VERIFY_CODE_SEND_FAIL);
    }

    private void checkMobileBlacklist(String mobile) {
        String serverIp = authProperties.getServiceIp().parseServiceIp();
        if (StringUtils.isNotEmpty(serverIp)) {
            AccountSafetyType type = smsService.checkAccount(mobile, serverIp);
            if (type == AccountSafetyType.BLACKLIST) {
                throw badRequestException(VERIFY_CODE_MOBILE_BLACKLIST);
            }
        }
    }

    @Override
    public BadRequestException existException(VerifyCodeUseFor useFor) {
        if (useFor == REGISTER) {
            return badRequestException(MOBILE_EXISTS_USE_REGISTER);
        } else if (useFor == BIND) {
            return badRequestException(MOBILE_EXISTS_USE_BIND);
        }
        return badRequestException(MOBILE_EXISTS);
    }

    @Override
    public BadRequestException notExistException(VerifyCodeUseFor useFor) {
        if (useFor == LOGIN) {
            return badRequestException(MOBILE_NOT_EXISTS_USE_LOGIN);
        } else if (useFor == RESET_PASSWORD) {
            return badRequestException(MOBILE_NOT_EXISTS_USE_RESET_PASSWORD);
        }
        return badRequestException(MOBILE_NOT_EXISTS);
    }
}
