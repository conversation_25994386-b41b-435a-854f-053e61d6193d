package org.befun.auth.provider;

import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

public abstract class BaseOauthProvider extends BaseAuthProvider {

    protected abstract AuthRequest buildRequest(String source);

    @Override
    public String authorize(String source, String app) {
        AuthRequest request = buildRequest(source);
        String state = buildState();
        return request.authorize(state);
    }

    @Override
    public Object login(String source, String app, Object callback) {
        AuthCallback authCallback = (AuthCallback) callback;
        AuthRequest authRequest = buildRequest(source);
        AuthResponse<?> response = authRequest.login(authCallback);

        if (response.ok() && response.getData() instanceof AuthUser) {
            String openId = ((AuthUser) response.getData()).getUuid();
            if (StringUtils.isNotEmpty(openId)) {
                return loginByThirdPartyUser(app, source, openId);
            }
        }
        throw new BadRequestException("invalid login not auth");
    }


    protected String buildState() {
        return "default";
    }
}
