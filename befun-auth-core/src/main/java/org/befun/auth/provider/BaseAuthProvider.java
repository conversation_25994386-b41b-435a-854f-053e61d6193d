package org.befun.auth.provider;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ParseContext;
import com.jayway.jsonpath.internal.function.PathFunctionFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.*;
import org.befun.auth.dto.*;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Role;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.provider.jpfunctions.*;
import org.befun.auth.repository.ThirdPartyUserRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.context.TenantData;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.*;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.jayway.jsonpath.Option.SUPPRESS_EXCEPTIONS;
import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseAuthProvider implements IAuthProvider {

    @Autowired
    private RoleService roleService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    protected StringRedisTemplate redisTemplate;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ThirdPartyUserRepository thirdPartyUserRepository;
    @Autowired
    private IAuthEventTrigger authEventTrigger;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    protected ParseContext JP = null;

    @PostConstruct
    public void init() {
        log.info("BaseAuthProvider init");
        try {
            JP = JsonPath.using(Configuration.defaultConfiguration().addOptions(SUPPRESS_EXCEPTIONS));
            @SuppressWarnings({"force modification jsonpath private field"})
            Map<String, Class> m = (Map<String, Class>) FieldUtils.readField(PathFunctionFactory.FUNCTIONS, "m", true);
            m.put("lowerMd5Upper", LowerMD5Upper.class);
            m.put("dateFormat", DateFormat.class);
            m.put("timeStamp", TimeStamp.class);
            m.put("userAgent", UserAgent.class);
            m.put("substring", Substring.class);
            m.put("uuid", org.befun.auth.provider.jpfunctions.UUID.class);
            m.put("upper", Upper.class);
            m.put("lower", Lower.class);
            m.put("now", Now.class);
            m.put("md5", MD5.class);
            m.put("ip", Ip.class);
        } catch (Exception e) {
            log.error("BaseAuthProvider init error", e);
        }

    }


    protected Object parseParams(Map<String, Object> attributes, String pathKey) {
        if (StringUtils.isEmpty(pathKey)) {
            return null;
        }
        // 使用jsonPath解析
        Object value;
        try {
            value = JsonPath.read(attributes, pathKey);
        } catch (Exception e) {
            log.error("jsonPath parseParams error", e);
            return null;
        }
        return value;
    }

    protected boolean checkOrgAndHasActiveToken(LoginPlatform platform, User user, String app) {
        Organization org = requireOrgById(user.getOrgId());
        checkOrg(org, user, app);
        return hasActiveToken(user.getId(), platform);
    }

    protected boolean checkInfoAndHasActiveToken(LoginPlatform platform, User user, String app) {
        checkUser(user);
        Organization org = requireOrgById(user.getOrgId());
        checkOrg(org, user, app);
        return hasActiveToken(user.getId(), platform);
    }

    protected LoginResponseDto checkLoginInfo(User user, String app) {
        return checkLoginInfo(LoginPlatform.pc, user, app);
    }

    protected LoginResponseDto checkLoginInfo(User user, List<String> roleCodes, String app) {
        return checkLoginInfo(LoginPlatform.pc, user, roleCodes, app);
    }

    protected LoginResponseDto checkLoginInfo(LoginPlatform platform, User user, String app) {
        return checkLoginInfo(platform, user, null, app);
    }

    protected LoginResponseDto checkLoginInfo(LoginPlatform platform, User user, List<String> roleCodes, String app) {
        addCopiedTenantData(user);
        checkUser(user);
        Organization org = requireOrgById(user.getOrgId());
        AppVersionDto orgVersion = checkOrg(org, user, app);
        return loginSuccess(platform, org, user, orgVersion, roleCodes, app);
    }

    protected LoginResponseDto loginSuccess(LoginPlatform platform, Organization org, User user, AppVersionDto orgVersion, String app) {
        return loginSuccess(platform, org, user, orgVersion, null, app);
    }

    protected LoginResponseDto loginSuccess(LoginPlatform platform, Organization org, User user, AppVersionDto orgVersion, List<String> roleCodes, String app) {
        authEventTrigger.userLogin(org.getId(), user.getId());
        String token = UUID.randomUUID().toString();
        if (platform != null && platform != LoginPlatform.pc) {
            token = platform.name() + "." + token;
        } else {
            platform = LoginPlatform.pc;
        }
        boolean isOwner = false;
        boolean isAdmin = false;
        String roleIds;
        Map<String, Set<String>> permissions;
        if (roleCodes == null) {
            isOwner = user.getIsAdmin() != null && user.getIsAdmin();
            isAdmin = roleService.hasSuperAdminRole(user.getId());
            roleIds = roleService.getRoleIdsByUserId(user.getId());
            permissions = roleService.getMenusByUserId(user.getId());
        } else {
            List<Role> roles = roleService.getByOrgIdAndCodes(org.getId(), roleCodes);
            if (CollectionUtils.isEmpty(roles)) {
                roleIds = "";
                permissions = Map.of();
            } else {
                isAdmin = roles.stream().filter(i -> i.getType() != null).anyMatch(i -> RoleType.SUPER_ADMIN.getType() == i.getType());
                roleIds = roleService.getRoleIdsByRoles(roles);
                permissions = roleService.getMenusByRoles(roles);
            }
        }

        if (StringUtils.isEmpty(roleIds)) {
            throw new BadRequestException("未匹配到角色:" + roleCodes);
        }

        String cemVersion = orgVersion.getCem_version();
        String surveyPlusVersion = orgVersion.getSurveyplus_version();
        Set<Long> departmentIds = user.parseDepartmentIds();
        List<DepartmentTreeDto> departments = departmentService.childrenTreeByDepartments(org.getId(), departmentIds);
        List<Long> subDepartmentIds = departmentService.mergeDepartmentIds(departments);
        String subDepartmentIdsString = subDepartmentIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        boolean isTop = CollectionUtils.isNotEmpty(departments) && departments.stream().anyMatch(i -> i.getPid() == null || i.getPid() == 0);

        Map<String, String> userInfo = new HashMap<>();
        userInfo.put("orgId", org.getId().toString());
        userInfo.put("availableSystems", user.getAvailableSystems());
        userInfo.put("userId", user.getId().toString());
        userInfo.put("permissions", JsonHelper.toJson(permissions));
        userInfo.put("admin_username", getAdminInOrg(org));
        userInfo.put("departmentIds", departmentIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
        userInfo.put("subDepartmentIds", subDepartmentIdsString);
        userInfo.put("plan", cemVersion);
        userInfo.put("is_admin", isAdmin ? "1" : "0");
        userInfo.put("is_owner", isOwner ? "1" : "0");
        userInfo.put("is_top", isTop ? "1" : "0");
        userInfo.put("orgCode", org.getCode());
        userInfo.put("org_name", org.getName());
        userInfo.put("roleIds", roleIds);
        userInfo.put("username", user.getUsername());
        userInfo.put("truename", user.getTruename());
        userInfo.put("old_sp_user", user.getOldSurveyPlusUser() == null ? "0" : user.getOldSurveyPlusUser().toString());

        // redis 增加缓存信息
        cacheTokenToRedis(user.getId(), token, platform.name(), userInfo);
        if (user.getStatus() == null || user.getStatus() == UserStatus.INIT.getStatus()) {
            user.setStatus(UserStatus.ENABLE.getStatus());
        }
        user.setLatestLogin(Long.valueOf(System.currentTimeMillis() / 1000).intValue());
        userRepository.save(user);
        // 构建接口返回信息
        // 过期时间当天还能使用
        Date expireDate = org.getAvailableDateEnd();
        Date startDate = org.getAvailableDateBegin();

        // 从环境变量中获取CEM_APP_VERSION
        String cemAppVersion = System.getenv("CEM_APP_VERSION");

        return LoginResponseDto.builder()
                .token(token)
                .cemVersion(cemVersion)
                .surveyPlusVersion(surveyPlusVersion)
                .userId(user.getId())
                .truename(user.getTruename())
                .username(user.getUsername())
                .accountType(app)
                .avatar(user.getAvatar())
                .isAdmin(isAdmin ? 1 : 0)
                .isOwner(isOwner ? 1 : 0)
                .guideInfo(UserGuideIndoType.parse(user.getGuideInfo()))
                .createTime(user.getCreateTime())
                .expireDate(expireDate)
                .startDate(startDate)
                .orgCode(org.getCode())
                .permissions(permissions)
                .oldSurveyPlusUser(user.getOldSurveyPlusUser())
                .organization(organizationConfigService.getSimpleOrgInfo(org))
                .cemAppVersion(cemAppVersion)
                .build();
    }

    /**
     * 1 保存token和userInfo并设置时间
     * 2 将 token 加入到 user-session 的 集合中
     * 3 清除 user-session 集合中已超时的 token
     * 4 重置 user-session 的过期时间
     * 5 设置platform的有效token
     * token结构 hash
     * key                          hashKey             value
     * session:{token}              userId              {userId}
     * 有效的token结构 hash
     * key                                              value
     * session.active:{platform}:{userId}               session:{token}
     */
    private void cacheTokenToRedis(Long userId, String token, String platform, Map<String, String> userInfo) {
        Duration expire = Duration.ofHours(authProperties.getTokenExpireHours());
        long nowSecond = System.currentTimeMillis() / 1000;
        long endSecond = nowSecond + expire.getSeconds();
        // 缓存登录信息
        String sessionKey = LegacyAuthTokenFilter.getSessionKey(token);
        redisTemplate.opsForHash().putAll(sessionKey, userInfo);
        redisTemplate.expire(sessionKey, expire);
        // 记录用户的所有token
        String userSessionKey = "user.session:" + userId;
        redisTemplate.opsForZSet().add(userSessionKey, token, endSecond);                       // 将最新的token加入用户的所有列表中
        redisTemplate.opsForZSet().removeRangeByScore(userSessionKey, 0, nowSecond);       // 移除已超时的
        redisTemplate.expire(userSessionKey, expire);                                           // 重置用户token队列超时时间
        // 设置单一登录平台的唯一有效的token，支持白名单
        String whitelistUserKey = "user.session.whitelist";
        Boolean isWhiteList = redisTemplate.opsForSet().isMember(whitelistUserKey, userId.toString());
        String activeSessionKey = LegacyAuthTokenFilter.getActiveSessionKey(userId, platform);
        if (isWhiteList != null && isWhiteList) {
            redisTemplate.delete(activeSessionKey);
        } else {
            redisTemplate.opsForValue().set(activeSessionKey, sessionKey, expire);                  // 重新指定{platform}的有效token
        }
    }

    private boolean hasActiveToken(Long userId, LoginPlatform platform) {
        String activeSessionKey = LegacyAuthTokenFilter.getActiveSessionKey(userId, platform.name());
        String activeSession = redisTemplate.opsForValue().get(activeSessionKey);
        return StringUtils.isNotEmpty(activeSession);
    }

    private String getAdminInOrg(Organization org) {
        Long adminUserId = org.getOwnerId();
        if (adminUserId != null && adminUserId > 0) {
            User user = userRepository.findById(adminUserId).orElse(null);
            if (user != null) {
                return user.getUsername();
            }
        }
        return null;
    }

    protected User requireUserById(Long userId) {
        if (userId != null && userId > 0) {
            User user = userRepository.findById(userId).orElse(null);
            if (user != null) {
                return user;
            }
        }
        throw badRequestException(ACCOUNT_NOT_EXISTS);
    }


    protected Organization requireOrgById(Long orgId) {
        if (orgId != null && orgId > 0) {
            Organization org = organizationService.getById(orgId).orElse(null);
            if (org != null) {
                return org;
            }
        }
        throw badRequestException(ORG_NOT_EXISTS);
    }

    protected void checkUser(User user) {
        if (user == null) {
            log.error("账号未注册：user is null");
            throw badRequestException(ACCOUNT_NOT_EXISTS);
        } else {
            int status = UserStatus.mapStatus(user.getStatus());
            if (status == UserStatus.DISABLE.getStatus()) {
                log.info("用户已被禁用：userId={}", user.getId());
                if (user.getIsAdmin() != null && user.getIsAdmin()) {
                    throw badRequestException(ACCOUNT_ADMIN_DISABLED);
                } else {
                    throw badRequestException(ACCOUNT_MEMBER_DISABLED);
                }
            } else if (status == UserStatus.INVITE.getStatus()) {
                log.info("用户未激活：userId={}", user.getId());
                throw badRequestException(ACCOUNT_MEMBER_INACTIVE);
            }
        }
        String availableSystems = user.getAvailableSystems();
        if (StringUtils.isEmpty(availableSystems)) {
            log.info("无系统权限，请联系管理员：userId={}, availableSystems is null", user.getId());
        } else {
            AppUserDto appUser = JsonHelper.toObject(availableSystems, AppUserDto.class);
            if (appUser != null && appUser.getLogin_cem() == 1) {
                return;
            }
            log.info("无系统权限，请联系管理员：userId={}, login_cem != 1", user.getId());
            throw badRequestException(ORG_NOT_EXISTS);
        }
        throw badRequestException(ACCOUNT_NOT_EXISTS);
    }

    protected AppVersionDto checkOrg(Organization org, User user, String app) {
        if (org == null) {
            log.error("无系统权限，请联系管理员");
            throw badRequestException(ORG_NOT_EXISTS);
        } else if (org.getIsBlock() == null || org.getIsBlock() == 1) {
            log.error("该企业已被禁用：orgId={}", org.getId());
            throw badRequestException(ORG_DISABLED);
        }
//        List<AppType> activeAppTypes = organizationService.parseOrgAppTypes(org);
//        AppType loginAppType = getLoginAppType();
//        if (!activeAppTypes.contains(loginAppType)) {
//            throw badRequestException(ORG_NOT_EXISTS);
//        }
        AppVersionDto appVersionDto = organizationService.parseOrgVersion(org);
        AppVersion orgVersion = organizationService.parseOrgVersion(appVersionDto);
        if (orgVersion == AppVersion.EMPTY) {
            log.info("无系统权限，请联系管理员：orgId={}, version is null", org.getId());
            throw badRequestException(ORG_NOT_EXISTS);
        } else if (
                orgVersion != AppVersion.FREE && (//不是免费版,并且时间不在有效期
                        org.getAvailableDateBegin() == null
                                || org.getAvailableDateEnd() == null
                                || new Date().before(org.getAvailableDateBegin())
                                || new Date().after(DateUtils.addDays(org.getAvailableDateEnd(), 1)) // 当前过期是当天的00:00:00 需要增加一天
                )
        ) {
            log.info("账户已过期，请联系管理员：orgId={}, dateBegin={}, dateEnd={}", org.getId(), org.getAvailableDateBegin(), org.getAvailableDateEnd());
            throw badRequestException(ORG_EXPIRED);
        }
        checkOrgUserLimit(org, user);
        return appVersionDto;
    }

    /**
     * v1.10.4
     * 账号数量已经达到版本的限额，该账号无法登录，请联系管理员升级版本！
     */
    private void checkOrgUserLimit(Organization org, User user) {
        OrganizationOptionalLimitDto limitDto = organizationService.parseOrgOptionalLimit(org);
        Integer limit = limitDto.getChildUserLimit();
        if (limit == null || limit <= 0) {
            limit = 1;
        }
        List<SimpleUser> users = userRepository.findByOrgIdAndStatusInOrderByCreateTimeAsc(
                org.getId(),
                UserStatus.enableStatus(),
                PageRequest.of(0, limit));
        if (CollectionUtils.isEmpty(users) || users.stream().noneMatch(i -> i.getId().equals(user.getId()))) {
            throw badRequestException(LOGIN_USER_LIMIT);
        }
    }

    private AppType getLoginAppType() {
        String loginAppType = request.getHeader("App-Type");
        if (StringUtils.isEmpty(loginAppType)) {
            return AppType.cem;
        } else {
            return EnumHelper.parse(AppType.values(), loginAppType, AppType.cem);
        }
    }

    protected LoginResponseDto loginByThirdPartyUser(ThirdPartyUser thirdPartyUser, List<String> roleCodes, String app) {
        if (thirdPartyUser == null) {
            throw new BadRequestException("第三方用户未注册");
        }
        User user = requireUserById(thirdPartyUser.getUserId());
        return checkLoginInfo(user, roleCodes, app);
    }

    protected LoginResponseDto loginByThirdPartyUser(ThirdPartyUser thirdPartyUser, String app) {
        return loginByThirdPartyUser(thirdPartyUser, null, app);
    }

    protected LoginResponseDto loginByThirdPartyUser(String app, String source, String openId) {
        return loginByThirdPartyUser(requireThirdPartyUser(app, source, openId), app);
    }

    private ThirdPartyUser requireThirdPartyUser(String app, String source, String openId) {
        List<ThirdPartyUser> users = thirdPartyUserRepository.findAllByAppAndSourceAndOpenId(app, source, openId);
        if (CollectionUtils.isNotEmpty(users)) {
            return users.get(0);
        }
        throw new BadRequestException("invalid login not bind");
    }

    /**
     * 登录接口是没有使用鉴权的 所有不会有TenantContext
     */
    protected void addCopiedTenantData(User user) {
        if (user == null) {
            return;
        }
        try {
            Long orgId = user.getOrgId();
            Long userId = user.getId();
            List<Long> departmentIds = new ArrayList<>(user.parseDepartmentIds());
            TenantContext.copiedTenantData.set(new TenantData(orgId, userId, departmentIds, null, null));
        } catch (Exception e) {
            log.error("addTenantContext error", e);
        }
    }

    protected List<String> parseRoleCodes(Object param){
        if (param == null) {
            return null;
        }
        List<String> roleCodes = new ArrayList<>();
        if (param instanceof List<?>) {
            List<?> values = (List<?>) param;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(values)) {
                values.forEach(i -> {
                    if (i != null) {
                        roleCodes.add(i.toString());
                    }
                });
            }
        } else {
            roleCodes.add(param.toString());
        }
        return roleCodes;
    }
}
