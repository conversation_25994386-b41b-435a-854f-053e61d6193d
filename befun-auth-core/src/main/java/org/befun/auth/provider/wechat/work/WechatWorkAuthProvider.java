package org.befun.auth.provider.wechat.work;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.WechatWorkProperty;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.LoginPlatform;
import org.befun.auth.constant.UserStatus;
import org.befun.auth.dto.UserInviteResponseDto;
import org.befun.auth.dto.WechatWorkBindDto;
import org.befun.auth.dto.WechatWorkLoginCallbackDto;
import org.befun.auth.dto.WechatWorkPreBindDto;
import org.befun.auth.dto.auth.WechatWorkAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.service.*;
import org.befun.auth.service.auth.AuthWechatWorkService;
import org.befun.auth.service.auth.config.WechatWorkConfig;
import org.befun.auth.utils.StringHelper;
import org.befun.auth.utils.wx.WXBizMsgCrypt;
import org.befun.auth.utils.wx.XMLParse;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static org.befun.auth.constant.AuthToastMessage.WECHAT_WORK_NOT_MATCH;
import static org.befun.auth.constant.AuthToastMessage.WECHAT_WORK_NO_AGENT;
import static org.befun.auth.constant.ThirdPartyAuthType.WECHAT_WORK;

@Slf4j
@Service("befun_auth_wechat_work")
public class WechatWorkAuthProvider extends BaseAuthProvider {

    @Autowired
    private AuthProperties authProperties;

    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private UserService userService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private UserInviteService userInviteService;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private AuthWechatWorkService authWechatWorkService;
    @Autowired
    private WeChatWorkAccountHelper weChatWorkAccountHelper;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    private static final String URL_BIND_PRE_AUTH_CODE = "https://qyapi.weixin.qq.com/cgi-bin/service/get_pre_auth_code?suite_access_token=%s";
    private static final String URL_BIND_SESSION = "https://qyapi.weixin.qq.com/cgi-bin/service/set_session_info?suite_access_token=%s";
    private static final String URL_BIND_AUTHORIZE = "https://open.work.weixin.qq.com/3rdapp/install?suite_id=%s&pre_auth_code=%s&redirect_uri=%s&state=%s";
    private static final String URL_BIND_PERMANENT = "https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=%s";
    private static final String URL_LOGIN_AUTHORIZE = "https://open.work.weixin.qq.com/wwopen/sso/3rd_qrConnect?appid=%s&redirect_uri=%s&state=%s&usertype=%s";
    private static final String URL_ORG_AUTHORIZE_INFO = "https://qyapi.weixin.qq.com/cgi-bin/service/get_login_info?access_token=%s";
    private static final String URL_ORG_APP_INFO = "https://qyapi.weixin.qq.com/cgi-bin/agent/get?access_token=%s&agentid=%s";
    private static final String URL_ORG_DEPARTMENT_USERS = "https://qyapi.weixin.qq.com/cgi-bin/user/simplelist?access_token=%s&department_id=%s&fetch_child=1";
    private static final String URL_ORG_TAG_USERS = "https://qyapi.weixin.qq.com/cgi-bin/tag/get?access_token=%s&tagid=%s";
    private static final String URL_SEND_MESSAGE = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";

    private static final String URL_MOBILE_LOGIN_AUTHORIZE = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%s#wechat_redirect";
    private static final String URL_MOBILE_ORG_AUTHORIZE_INFO = "https://qyapi.weixin.qq.com/cgi-bin/service/getuserinfo3rd?suite_access_token=%s&code=%s";


    private WechatWorkProperty requireProperty() {
        return Optional.ofNullable(authProperties.getWechatWork()).orElseThrow(() -> ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_NOT_SUPPORT));
    }

    //***************************************************************************************************
    //****************************************** redisKey ***********************************************
    //***************************************************************************************************

    /**
     * 企业微信扫码登录后的，绑定和注册
     */
    private String bindOrCreateMemberKey(String state) {
        return String.format("wechat_open_bind_or_create_member:%s", state);
    }

    /**
     * 管理后台成员绑定企业微信
     */
    private String bindMemberKey(String state) {
        return String.format("wechat_open_bind_member:%s", state);
    }


    /**
     * 管理后台企业绑定企业微信
     */
    private String bindOrgKey(String state) {
        return String.format("wechat_open_bind_org:%s", state);
    }


    //***************************************************************************************************
    //**************************************** send message *********************************************
    //***************************************************************************************************

    public boolean sendTemplateMessage(Long orgId, String app, Long userId, String templateName, Map<String, Object> params) {
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, WECHAT_WORK, app);
        if (auth == null || auth.getConfig() == null) {
            log.warn("发送企业微信模板失败，未配置企业微信");
            return false;
        }
        WechatWorkConfig config = JsonHelper.toObject(auth.getConfig(), WechatWorkConfig.class);
        if (config == null) {
            log.warn("发送企业微信模板失败，未配置企业微信");
            return false;
        }
        ThirdPartyUser user = thirdPartyUserService.getByUserSourceApp(userId, auth.getSource(), app);
        if (user == null) {
            log.warn("发送企业微信模板失败，未绑定成员");
            return false;
        }
        return sendTemplateMessage(orgId, user.getOpenId(), config, templateName, params);
    }

    public boolean sendTemplateMessage(Long orgId, String openId, WechatWorkConfig config, String templateName, Map<String, Object> params) {
        try {
            if (!weChatWorkAccountHelper.isActiveUser(orgId, config, openId)) {
                log.error("发送模板消息失败，用户未激活 openId={}, templateName={}", openId, templateName);
                return false;
            }
            Integer agentId = Integer.parseInt(config.getAgentId());
            WechatWorkProperty property = requireProperty();
            WechatWorkProperty.WechatWorkTemplate template = property.getTemplates().stream().filter(i -> i.getName().equals(templateName)).findFirst().orElse(null);
            if (template == null) {
                log.error("发送模板消息失败, 模板不存在。 openId={}, templateName={}", openId, templateName);
                return false;
            }
            String accessToken = weChatWorkAccountHelper.getOrgAccessToken(config);
            if (StringUtils.isEmpty(accessToken)) {
                log.error("发送模板消息失败, 获取企业accessToken失败。 openId={}, templateName={}", openId, templateName);
                return false;
            }
            String url = String.format(URL_SEND_MESSAGE, accessToken);
            String templateUrl = StringUtils.isEmpty(template.getUrl()) ? "" : TemplateEngine.renderTextTemplateSimple(template.getUrl(), params);
            List<Map<String, String>> content = template.getParameters().stream().map(i -> {
                String value = TemplateEngine.renderTextTemplateSimple(i.getValue(), params);
                return Map.of("key", i.getName(), "value", value);
            }).collect(Collectors.toList());
            Map<String, Object> msg = Map.of("template_id", template.getId(), "url", templateUrl == null ? "" : templateUrl, "content_item", content);
            Map<String, Object> body = Map.of("touser", openId, "msgtype", "template_msg", "agentid", agentId, "template_msg", msg);
            WechatWorkDto response = weChatWorkAccountHelper.httpPost(url, body, WechatWorkDto.class);
            if (response == null || response.getErrcode() != 0) {
                log.error("发送模板消息失败 openId={}, templateName={}", openId, templateName);
                return false;
            }
            log.info("发送模板消息成功 openId={}, templateName={}", openId, templateName);
            return true;
        } catch (Exception e) {
            log.error("发送模板消息失败 openId={}, templateName={}", openId, templateName, e);
        }
        return false;
    }

    //***************************************************************************************************
    //*************************************** 企业微信通知回调 *********************************************
    //***************************************************************************************************

//    public static void main(String[] args) {
//        try {
//            String sign="968fda65526088378a6877cfba99bbefef4844da";
//            String timestamp="1652426794";
//            String nonce="1651568319";
//            String echostr="ZFEeLdSG6EPVXlzs8avtDw0jjMrriGN8LlEoqT5bjwaaZcZW2L3V8hZW/0l0zQK4ufBYVfDqvwBP+tp5fsm28A==";
//            String getToken="SChlsiT5D0";
//            String getAesKey="Aw2bynwMwrKnltnINlLWnldrnx953WBFmLF4GRU8fAP";
//            String getSuiteId="ww951769b8e8579644";
//            WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(getToken,getAesKey,getSuiteId);
//            String verify = msgCrypt.VerifyURL(sign, timestamp, nonce, echostr);
//            log.info("校验企业微信消息回调地址成功，sign={}, timestamp={}, nonce={}, echostr={}, verify={}", sign, timestamp, nonce, echostr, verify);
//        } catch (Exception e) {
//
//        }
//    }
//    您好，在服务商侧即第三方应用/小程序，配置的url对应的
//    get请求都需以服务商本身的corpid来解密
//    post请求，数据回调url，以授权企业的corpid进行解密，对应的url可以加上对应的$CORPID$来进行解密；
//    指令回调url以应用本身的suiteid进行解密
//    自建应用全部以自身企业的corpid进行加解密
//    自建应用的xml格式与第三方应用有差异

    public String verifyMessageUrl(String app, String sign, String timestamp, String nonce, String echostr) {
        try {
            WechatWorkProperty property = requireProperty();
            //    get请求都需以服务商本身的corpid来解密
            WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(property.getToken(), property.getAesKey(), property.getCorpId());
            String verify = msgCrypt.VerifyURL(sign, timestamp, nonce, echostr);
            log.info("校验企业微信消息回调地址成功，sign={}, timestamp={}, nonce={}, echostr={}, verify={}", sign, timestamp, nonce, echostr, verify);
            return verify;
        } catch (Exception e) {
            log.error("校验企业微信消息回调地址失败，sign={}, timestamp={}, nonce={}, echostr={}", sign, timestamp, nonce, echostr);
        }
        return "success";
    }

    public String verifyTicketUrl(String app, String sign, String timestamp, String nonce, String echostr) {
        try {
            WechatWorkProperty property = requireProperty();
            //    get请求都需以服务商本身的corpid来解密
            WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(property.getToken(), property.getAesKey(), property.getCorpId());
            String verify = msgCrypt.VerifyURL(sign, timestamp, nonce, echostr);
            log.info("校验企业微信ticket回调地址成功，sign={}, timestamp={}, nonce={}, echostr={}, verify={}", sign, timestamp, nonce, echostr, verify);
            return verify;
        } catch (Exception e) {
            log.error("校验企业微信ticket回调地址失败，sign={}, timestamp={}, nonce={}, echostr={}", sign, timestamp, nonce, echostr);
        }
        return "success";
    }

    public String parseMessage(String app, String corpId, // orgCorpId
                               String sign, String timestamp, String nonce, String body) {
        try {
            WechatWorkProperty property = requireProperty();
            // post请求，数据回调url，以授权企业的corpid进行解密，对应的url可以加上对应的$CORPID$来进行解密；
            WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(property.getToken(), property.getAesKey(), corpId);
            String xml = msgCrypt.DecryptMsg(sign, timestamp, nonce, body);
            log.info("接收到企业微信推送的消息：{}", xml);
        } catch (Exception e) {
            log.error("解密企业微信消息失败，sign={}, timestamp={}, nonce={}, data={}", sign, timestamp, nonce, body);
        }
        return "success";
    }

    public String parseTicket(String app, String sign, String timestamp, String nonce, String body) {
        try {
            WechatWorkProperty property = requireProperty();
            //    指令回调url以应用本身的suiteid进行解密
            WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(property.getToken(), property.getAesKey(), property.getSuiteId());
            String xml = msgCrypt.DecryptMsg(sign, timestamp, nonce, body);
            log.info("接收到企业微信指令数据：{}", xml);
            Document document = XMLParse.readAsDocument(xml);
            Element root = document.getDocumentElement();
            NodeList dataType = root.getElementsByTagName("InfoType");
            String infoType = dataType.item(0).getTextContent();
            if (infoType.equals("suite_ticket")) {
                NodeList suiteIdNode = root.getElementsByTagName("SuiteId");
                NodeList suiteTicketNode = root.getElementsByTagName("SuiteTicket");
                String suiteId = suiteIdNode.item(0).getTextContent();
                String suiteTicket = suiteTicketNode.item(0).getTextContent();
                String suiteTicketKey = weChatWorkAccountHelper.suiteTicketKey(suiteId);
                log.info("接收到企业微信ticket, suiteId={}, ticket={}", suiteId, suiteTicket);
                stringRedisTemplate.opsForValue().set(suiteTicketKey, suiteTicket);
            } else if (infoType.equals("cancel_auth")) {
                NodeList suiteIdNode = root.getElementsByTagName("SuiteId");
                NodeList companyIdNode = root.getElementsByTagName("AuthCorpId");
                String suiteId = suiteIdNode.item(0).getTextContent();
                String companyId = companyIdNode.item(0).getTextContent();
                log.info("接收到企业微信取消授权的通知, suiteId={}, companyId={}", suiteId, companyId);
                if (property.getSuiteId().equals(suiteId)) {
                    Long orgId = authWechatWorkService.delete(app, companyId);
                    if (orgId != null) {
                        log.info("已解绑企业（{}）与企业微信（{}）的关系", orgId, companyId);
                    }
                } else {
                    log.warn("忽略企业微信取消授权的通知, 应用id不匹配（{} != {}）", property.getSuiteId(), suiteId);
                }
            } else if (infoType.equals("change_auth")) {
                NodeList suiteIdNode = root.getElementsByTagName("SuiteId");
                NodeList companyIdNode = root.getElementsByTagName("AuthCorpId");
                String suiteId = suiteIdNode.item(0).getTextContent();
                String companyId = companyIdNode.item(0).getTextContent();
                log.info("接收到企业微信修改授权的通知, suiteId={}, companyId={}", suiteId, companyId);
                if (property.getSuiteId().equals(suiteId)) {
                    ThirdPartyAuth entity = thirdPartyAuthService.getBySource(WECHAT_WORK.getSourcePrefix() + companyId, WECHAT_WORK, app);
                    if (entity != null) {
                        WechatWorkConfig config = weChatWorkAccountHelper.checkConfig(entity, false);
                        refreshAppInfo(entity, config);
                        log.info("已更新企业微信授权信息, orgId={}, companyId={}", entity.getOrgId(), companyId);
                    }
                } else {
                    log.warn("忽略企业微信修改授权的通知, 应用id不匹配（{} != {}）", property.getSuiteId(), suiteId);
                }
            }
        } catch (Exception e) {
            log.error("解析企业微信指令失败，sign={}, timestamp={}, nonce={}, data={}", sign, timestamp, nonce, body);
        }
        return "success";
    }

    //***************************************************************************************************
    //**************************************** 企业微信绑定用户 ********************************************
    //***************************************************************************************************
    public WechatWorkPreBindDto preBindMemberState(Long orgId, Long userId, String app) {
        requireNotBindMember(orgId, userId, app);
        String state = UUID.randomUUID().toString();
        String key = bindMemberKey(state);
        String value = String.format("%d:%d", orgId, userId);
        stringRedisTemplate.opsForValue().set(key, value, Duration.ofMinutes(5));
        return new WechatWorkPreBindDto(state);
    }

    private ThirdPartyAuth requireNotBindMember(Long orgId, Long userId, String app) {
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, WECHAT_WORK, app);
        if (auth == null) {
            log.error("企业微信未设置");
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_NO_CROP);
        }
        ThirdPartyUser thirdPartyUser = thirdPartyUserService.getByUserSourceApp(userId, auth.getSource(), app);
        if (thirdPartyUser != null) {
            log.error("该企业微信已被其他账号绑定！1");
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_MEMBER_EXISTS);
        }
        return auth;
    }

    private ThirdPartyAuth requireNotBindMember(ThirdPartyAuth auth, String openId, String app) {
        ThirdPartyUser thirdPartyUser = thirdPartyUserService.getBySourceAppOpenId(auth.getSource(), app, openId);
        if (thirdPartyUser != null) {
            log.error("该企业微信已被其他账号绑定！2");
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_MEMBER_EXISTS);
        }
        return auth;
    }

    private Pair<Long, Long> checkBindMemberState(String state) {
        String key = bindMemberKey(state);
        Object value = stringRedisTemplate.opsForValue().get(key);
        String info;
        if (value != null && StringUtils.isNotEmpty(info = value.toString())) {
            String[] infos = info.split(":");
            if (infos.length == 2 && NumberUtils.isDigits(infos[0]) && NumberUtils.isDigits(infos[1])) {
                return Pair.of(Long.parseLong(infos[0]), Long.parseLong(infos[1]));
            }
        }
        log.error("企业微信绑定成员的state（{}）无效", state);
        throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_CODE_EXPIRE);
    }

    public String bindMemberAuthorize(String state) {
        checkBindMemberState(state);
        WechatWorkProperty property = requireProperty();
        String callback = URLEncoder.encode(property.getMemberCallbackUrl(), StandardCharsets.UTF_8);
        return String.format(URL_LOGIN_AUTHORIZE, property.getCorpId(), callback, state, property.getMemberUserType());
    }

    @Transactional
    public boolean bindMemberAuthorizeCallback(WechatWorkLoginCallbackDto callback, String app) {
        Pair<Long, Long> pair = checkBindMemberState(callback.getState());
        Long orgId = pair.getLeft();
        Long userId = pair.getRight();
        ThirdPartyAuth auth = requireNotBindMember(orgId, userId, app);
        LoginUser loginUser = login(callback);
        if (loginUser != null) {
            WechatWorkConfig config = checkConfig(auth, loginUser, app, false);
            requireNotBindMember(auth, loginUser.userId, app);
            bindMember(auth, config, userId, loginUser.userId, app);
            return true;
        }
        throw new BusinessException("绑定失败");
    }

    private void bindMember(ThirdPartyAuth auth, WechatWorkConfig config, Long userId, String openId, String app) {
        thirdPartyUserService.add(auth.getOrgId(), userId, auth.getSource(), app, openId, null, true);
        try {
            weChatWorkAccountHelper.bindMember(auth.getOrgId(), openId, config);
        } catch (Throwable e) {
            log.error("", e);
        }
    }

    //***************************************************************************************************
    //**************************************** 企业微信绑定企业 ********************************************
    //***************************************************************************************************

    /**
     * 构建绑定企业的授权地址
     */
    public WechatWorkPreBindDto preBindOrgState(Long orgId, Long userId, String app) {
        requireNotBindOrg(orgId, app);
        String state = UUID.randomUUID().toString();
        String key = bindOrgKey(state);
        String value = String.format("%d:%d", orgId, userId);
        stringRedisTemplate.opsForValue().set(key, value, Duration.ofMinutes(5));
        return new WechatWorkPreBindDto(state);
    }

    private Pair<Long, Long> checkBindOrgState(String state) {
        String key = bindOrgKey(state);
        Object value = stringRedisTemplate.opsForValue().get(key);
        String info;
        if (value != null && StringUtils.isNotEmpty(info = value.toString())) {
            String[] infos = info.split(":");
            if (infos.length == 2 && NumberUtils.isDigits(infos[0]) && NumberUtils.isDigits(infos[1])) {
                return Pair.of(Long.parseLong(infos[0]), Long.parseLong(infos[1]));
            }
        }
        log.error("企业微信绑定企业的state（{}）无效", state);
        throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_CODE_EXPIRE);
    }

    /**
     * 构建绑定企业的授权地址
     */
    public String bindOrgAuthorize(String state) {
        checkBindOrgState(state);
        WechatWorkProperty property = requireProperty();
        String accessToken = weChatWorkAccountHelper.requireSuiteAccessToken();
        WechatWorkPreAuthCodeDto preAuthCode = weChatWorkAccountHelper.httpGet(String.format(URL_BIND_PRE_AUTH_CODE, accessToken), WechatWorkPreAuthCodeDto.class);
        if (preAuthCode == null || preAuthCode.getErrcode() != 0) {
            log.error("获取预授权码失败");
            throw new BusinessException();
        }
        Map<String, Object> sessionBody = Map.of("pre_auth_code", preAuthCode.getPreAuthCode(), "session_info", Map.of("auth_type", property.getBindAuthType()));
        WechatWorkDto session = weChatWorkAccountHelper.httpPost(String.format(URL_BIND_SESSION, accessToken), sessionBody, WechatWorkDto.class);
        if (session == null || session.getErrcode() != 0) {
            log.error("设置授权配置失败");
            throw new BusinessException();
        }
        String callback = URLEncoder.encode(property.getBindCallbackUrl(), StandardCharsets.UTF_8);
        return String.format(URL_BIND_AUTHORIZE, property.getSuiteId(), preAuthCode.getPreAuthCode(), callback, state);
    }

    /**
     * 检验平台企业未绑定企业微信
     */
    private void requireNotBindOrg(Long orgId, String app) {
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, WECHAT_WORK, app);
        if (auth != null) {
            throw new BusinessException("企业微信已绑定");
        }
    }

    /**
     * 检验企业微信未被绑定平台企业
     */
    private void requireNotBindOrg(String source, String app) {
        ThirdPartyAuth auth = thirdPartyAuthService.getBySource(source, WECHAT_WORK, app);
        if (auth != null) {
            throw new BusinessException("企业微信已绑定");
        }
    }

    @Transactional
    public WechatWorkAuthDto bindOrgCallback(String code, String state, String app) {
        Pair<Long, Long> pair = checkBindOrgState(state);
        Long orgId = pair.getLeft();
        Long userId = pair.getRight();
        requireNotBindOrg(orgId, app);
        String accessToken = weChatWorkAccountHelper.requireSuiteAccessToken();
        Map<String, Object> permanentBody = Map.of("auth_code", code);
        String authInfo = weChatWorkAccountHelper.httpPost(String.format(URL_BIND_PERMANENT, accessToken), permanentBody, String.class);
        BindOrgCorp orgCorp = parseOrgCorp(authInfo);
        if (orgCorp == null) {
            log.error("获取企业永久授权码失败");
            throw new BusinessException();
        }
        requireNotBindOrg(WECHAT_WORK.getSourcePrefix() + orgCorp.corpId, app);
        if (StringUtils.isNotEmpty(orgCorp.accessToken)) {
            stringRedisTemplate.opsForValue().set(weChatWorkAccountHelper.orgAccessTokenKey(orgCorp.corpId), orgCorp.accessToken, Duration.ofSeconds(7000));
        }
        // 增加 ThirdPartyAuth 绑定关系
        WechatWorkConfig config = new WechatWorkConfig();
        config.setName(orgCorp.name);
        config.setLogo(orgCorp.logo);
        config.setCompanyId(orgCorp.corpId);
        config.setAgentId(orgCorp.agentId);
        config.setPermanentCode(orgCorp.permanentCode);

        ThirdPartyAuth auth = authWechatWorkService.add(orgId, app, config, authInfo);
        refreshAppInfo(auth, config);

        // 下单
        weChatWorkAccountHelper.placeOrder(orgId, config);

        // 自动绑定扫码用户
        if (StringUtils.isNotEmpty(orgCorp.userId)) {
            bindMember(auth, config, userId, orgCorp.userId, app);
        }

        // 返回企业绑定企业微信信息
        return authWechatWorkService.getSingle(app);
    }

    private BindOrgCorp parseOrgCorp(String authInfo) {
        Map<String, Object> data = JsonHelper.toMap(authInfo);
        if (data != null) {
            String permanentCode = (String) data.get("permanent_code");
            String accessToken = (String) data.get("access_token");
            String corpId = null, name = null, logo = null, agentId = null, userId = null;
            Object corpData = data.get("auth_corp_info");
            if (corpData instanceof Map) {
                Map<?, ?> corp = (Map<?, ?>) corpData;
                corpId = (String) corp.get("corpid");
                name = (String) corp.get("corp_name");
                logo = (String) corp.get("corp_square_logo_url");
            }
            Object agentData = data.get("auth_info");
            if (agentData instanceof Map) {
                Map<?, ?> agent = (Map<?, ?>) agentData;
                List<?> agent0 = (List<?>) agent.get("agent");
                if (agent0 != null && agent0.size() > 0) {
                    Map<?, ?> agentInfo = (Map<?, ?>) agent0.get(0);
                    agentId = ((Integer) agentInfo.get("agentid")).toString();
                }
            }
            Object userData = data.get("auth_user_info");
            if (userData instanceof Map) {
                Map<?, ?> user = (Map<?, ?>) userData;
                userId = (String) user.get("userid");
            }
            if (StringUtils.isNotEmpty(permanentCode) && StringUtils.isNotEmpty(corpId) && StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(logo) && StringUtils.isNotEmpty(agentId)) {
                return new BindOrgCorp(corpId, agentId, permanentCode, accessToken, name, logo, userId);
            }
        }
        return null;
    }

    //***************************************************************************************************
    //************************************** 企业微信网页授权登录 ******************************************
    //***************************************************************************************************

    public String mobileAuthorize(String source, String app, String redirectPath) {
        WechatWorkProperty property = requireProperty();
        String redirectUrl;
        if (redirectPath.startsWith("http")) {
            redirectUrl = redirectPath;
        } else {
            redirectUrl = StringHelper.concatUrl(property.getRedirectDomain(), redirectPath);
        }
        String callback = URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8);
        String state = UUID.randomUUID().toString();
        return String.format(URL_MOBILE_LOGIN_AUTHORIZE, property.getSuiteId(), callback, state);
    }


    //***************************************************************************************************
    //************************************** 企业微信扫码授权登录 ******************************************
    //***************************************************************************************************


    @Override
    public String authorize(String source, String app) {
        WechatWorkProperty property = requireProperty();
        String callback = URLEncoder.encode(property.getLoginCallbackUrl(), StandardCharsets.UTF_8);
        String state = UUID.randomUUID().toString();
        return String.format(URL_LOGIN_AUTHORIZE, property.getCorpId(), callback, state, property.getLoginUserType());
    }

    private LoginUser login(WechatWorkLoginCallbackDto callback) {
        if (callback.getType().equals("mobile")) {
            String accessToken = weChatWorkAccountHelper.getSuitAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                return null;
            }
            WechatWorkMobileLoginDto result = weChatWorkAccountHelper.httpGet(String.format(URL_MOBILE_ORG_AUTHORIZE_INFO, accessToken, callback.getCode()), WechatWorkMobileLoginDto.class);
            if (result != null && result.getErrcode() == 0 && StringUtils.isNotEmpty(result.getCorpId()) && StringUtils.isNotEmpty(result.getUserId())) {
                return new LoginUser(result.getCorpId(), result.getUserId());
            }
            return null;
        } else {
            String accessToken = weChatWorkAccountHelper.getCropAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                return null;
            }
            Map<String, Object> body = Map.of("auth_code", callback.getCode());
            return parseUserInfo(weChatWorkAccountHelper.httpPost(String.format(URL_ORG_AUTHORIZE_INFO, accessToken), body, WechatWorkLoginDto.class));
        }
    }

    @Override
    public Object login(String source, String app, Object callback) {
        WechatWorkLoginCallbackDto authCallback = (WechatWorkLoginCallbackDto) callback;
        LoginUser loginUser = login(authCallback);
        if (loginUser != null) {
            String dbSource = WECHAT_WORK.getSourcePrefix() + loginUser.corpId;
            ThirdPartyAuth auth = thirdPartyAuthService.getBySource(dbSource, WECHAT_WORK, app);
            WechatWorkConfig config = checkConfig(auth, loginUser, app, true);
            ThirdPartyUser thirdPartyUser = thirdPartyUserService.getByOrgSourceAppOpenId(auth.getOrgId(), dbSource, app, loginUser.userId);
            User user = getUserByThirdPartyUser(thirdPartyUser);
            if (user == null) {
                // bind 这里抛出业务异常200，其他地方都抛出400
                String bindCode = UUID.randomUUID().toString();
                stringRedisTemplate.opsForValue().set(bindOrCreateMemberKey(bindCode), auth.getOrgId() + ":" + loginUser.userId, Duration.ofMinutes(5));
                throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_NOT_BIND, new BindCode(bindCode));
            } else {
                // login
                // 每次使用企业微信登录都需要尝试激活绑定
                weChatWorkAccountHelper.bindMemberWhenLogin(thirdPartyUser.getOrgId(), thirdPartyUser.getOpenId(), config);
                return checkLoginInfo(LoginPlatform.wechatWork, user, app);
            }
        }
        throw ToastMessageHelper.badRequestException(AuthToastMessage.WECHAT_WORK_FAILURE);
    }


    @Transactional
    public boolean loginAfterBindOrCreate(String app, WechatWorkBindDto param) {
        Pair<Long, String> cache = checkLoginAfterBindCode(param.getBindToken());
        long orgId = cache.getLeft();
        String openId = cache.getRight();
        boolean r;
        if (param.typeBind()) {
            r = loginAfterBindMember(orgId, openId, param.getEmail(), app);
        } else if (param.typeCreate()) {
            r = loginAfterCreateMember(orgId, openId, param.getEmail(), app);
        } else {
            throw new BadRequestException("type 参数错误");
        }
        if (r) {
            stringRedisTemplate.delete(bindOrCreateMemberKey(param.getBindToken()));
        }
        return r;
    }

    /**
     * 1、该邮箱是否注册（如果未注册，说明还不是体验家系统账号，进行toast提示：您的邮箱尚未注册，请联系所在企业的体验家系统管理员邀请加入！）【已邀请未激活的邮箱为已注册】
     * 2、该邮箱已注册，是否属于该企业（比如扫码的个人企微账号为企业A，但邮箱所属的体验家账户为企业B。如果不属于该企业，进行toast提示：该邮箱不属于本企业的体验家账号！）
     * 3、该邮箱是否已关联了其它个人企微（如果已关联过其它个人企微，进行toast提示：该邮箱已被其他账号绑定！）
     * 4、该邮箱是否已在邮件中验证成功，若是则toast提示：该邮箱已验证，请重新扫码登录！
     */
    private boolean loginAfterBindMember(long orgId, String openId, String email, String app) {
        User user = userService.getByEmail(email);
        if (user == null) {
            // 邮箱未注册
            log.info("绑定失败，邮箱未注册：orgId={}, email={}, openId={}", orgId, email, openId);
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_EMAIL_NOT_REGISTER);
        }
        // 用户需要是 UserStatus.isActiveStatus(user.getStatus())
        if (!UserStatus.isActiveStatus(user.getStatus())) {
            log.info("绑定失败，邮箱已被禁用：orgId={}, email={}, openId={}", orgId, email, openId);
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_EMAIL_DISABLE);
        }
        if (user.getOrgId() == null || user.getOrgId() != orgId) {
            // 已注册，但是企业不匹配
            log.info("绑定失败，企业不匹配：orgId={}, userId={}, openId={}", orgId, user.getId(), openId);
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_EMAIL_NOT_MATCH_ORG);
        }
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, WECHAT_WORK, app);
        if (auth == null) {
            // 企业微信登录未启用
            log.info("绑定失败，企业微信登录未启用：orgId={}, userId={}, openId={}", orgId, user.getId(), openId);
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_NO_CROP);
        }
        ThirdPartyUser thirdPartyUser = thirdPartyUserService.getByUserSourceApp(user.getId(), auth.getSource(), app);
        if (thirdPartyUser != null) {
            if (!openId.equals(thirdPartyUser.getOpenId())) {
                // 邮箱已被别人绑定
                log.info("绑定失败，邮箱已被别人绑定：orgId={}, userId={}, openId={}", orgId, user.getId(), openId);
                throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_EMAIL_EXISTS);
            } else {
                if (thirdPartyUser.isValid()) {
                    // 是我自己绑定的
                    log.info("已绑定：orgId={}, userId={}, openId={}", orgId, user.getId(), openId);
                    throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_EMAIL_SUCCESS);
                } else {
                    // 是我自己绑定的，重新发送邮件
                    log.info("已绑定，但是没有激活：orgId={}, userId={}, openId={}", orgId, user.getId(), openId);
                }
            }
        } else {
            thirdPartyUser = thirdPartyUserService.add(orgId, user.getId(), auth.getSource(), app, openId, null, false);
            log.info("绑定成功：orgId={}, userId={}, openId={}", orgId, user.getId(), openId);
        }
        sendActiveEmail(email, thirdPartyUser, app);
        return true;
    }

    private void sendActiveEmail(String email, ThirdPartyUser thirdPartyUser, String app) {
        try {
            // 发送邮件
            String code = StringHelper.generatorVerifyLinkCode("wechat-work");
            log.info("激活邮件code={}", code);
            String key = String.format("verify-link:%s", code);
            stringRedisTemplate.opsForValue().set(key, thirdPartyUser.getId() + "", Duration.ofHours(72));
            // 双向绑定，从 thirdPartyUserId 查 code
            String reverseKey = String.format("verify-link-reverse:%d", thirdPartyUser.getId());
            stringRedisTemplate.opsForValue().set(reverseKey, key, Duration.ofHours(72));
            asyncService.bindNotify(email, code, app);
        } catch (Exception ee) {
            log.error("发送激活邮件失败：", ee);
        }
    }

    /**
     * 1、该企业成员数量是否达到上限（如果达到上限，进行toast提示：成员数量已达到满额，请联系管理员购买可使用名额！）
     * 2、该邮箱是否已注册（如果已经注册，进行toast提示：该邮箱已注册！）【已邀请未激活的邮箱为已注册】
     */
    private boolean loginAfterCreateMember(long orgId, String openId, String email, String app) {
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, WECHAT_WORK, app);
        if (auth == null) {
            // 企业微信登录未启用
            log.info("绑定失败，企业微信登录未启用：orgId={}, email={}, openId={}", orgId, email, openId);
            throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_NO_CROP);
        }
        UserInviteResponseDto invite = userInviteService.inviteMembers(orgId, email, app, userId -> {
            thirdPartyUserService.add(orgId, userId, auth.getSource(), app, openId, null, false);
        });
        if (!invite.isSuccess()) {
            if (CollectionUtils.isNotEmpty(invite.getExistsEmail())) {
                throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_CREATE_EMAIL_EXISTS);
            } else if (invite.getOverSize() > 0) {
                throw ToastMessageHelper.businessException(AuthToastMessage.ORG_MEMBER_OVERSIZE);
            }
        }
        return true;
    }

    private Pair<Long, String> checkLoginAfterBindCode(String bindCode) {
        String value = stringRedisTemplate.opsForValue().get(bindOrCreateMemberKey(bindCode));
        if (StringUtils.isNotEmpty(value)) {
            String[] values = value.split(":");
            if (values.length == 2 && NumberUtils.isDigits(values[0])) {
                return Pair.of(Long.parseLong(values[0]), values[1]);
            }
        }
        throw ToastMessageHelper.businessException(AuthToastMessage.WECHAT_WORK_BIND_CODE_EXPIRE);
    }

    private User getUserByThirdPartyUser(ThirdPartyUser thirdPartyUser) {
        if (thirdPartyUser == null || thirdPartyUser.getUserId() == null) {
            return null;
        }
        if (!thirdPartyUser.isValid()) {
            // 检查一下，如果上次激活的连接已经失效了，这里重新发送一封邮件
            thirdPartyUserNotValid(thirdPartyUser);
            throw ToastMessageHelper.badRequestException(AuthToastMessage.WECHAT_WORK_EMAIL_NOT_VALID);
        }
        return userService.get(thirdPartyUser.getUserId());
    }

    /**
     * 绑定关系未激活，也许需要重新发送邮件
     */
    private void thirdPartyUserNotValid(ThirdPartyUser thirdPartyUser) {
        String reverseKey = String.format("verify-link-reverse:%d", thirdPartyUser.getId());
        if (Optional.ofNullable(stringRedisTemplate.hasKey(reverseKey)).orElse(false)) {
            // 激活链接未过期
            return;
        }
        User user = userService.get(thirdPartyUser.getUserId());
        if (user == null) {
            // 用户被删除了
            return;
        }
        sendActiveEmail(user.getEmail(), thirdPartyUser, thirdPartyUser.getApp());
    }

    private WechatWorkConfig checkConfig(ThirdPartyAuth auth, LoginUser loginUser, String app, boolean httpError) {
        WechatWorkConfig config = weChatWorkAccountHelper.checkConfig(auth, httpError);
        // 1.8.0新增 兼容旧版本（之前的版本没有保存授权的可见范围信息，这里增加一些判断，去重新拉取信息保存下来）
        if (config.getVisibleUsers() == null || config.getVisibleDepartments() == null || config.getVisibleTags() == null) {
            refreshAppInfo(auth, config);
        }
        // 检查绑定的企业是否匹配
        if (!config.getCompanyId().equals(loginUser.corpId)) {
            if (httpError) {
                throw ToastMessageHelper.badRequestException(WECHAT_WORK_NOT_MATCH);
            } else {
                throw ToastMessageHelper.businessException(WECHAT_WORK_NOT_MATCH);
            }
        }
        try {
            // 检查用户是否有应用的权限
            checkUserHasApp(config, loginUser.userId);
        } catch (Throwable e) {
            if (httpError) {
                throw ToastMessageHelper.badRequestException(WECHAT_WORK_NO_AGENT);
            } else {
                throw ToastMessageHelper.businessException(WECHAT_WORK_NO_AGENT);
            }
        }
        return config;
    }

    /**
     * 刷新app信息
     */
    private void refreshAppInfo(ThirdPartyAuth entity, WechatWorkConfig config) {
        String accessToken = weChatWorkAccountHelper.requireOrgAccessToken(config);
        WechatWorkAppInfoDto dto = weChatWorkAccountHelper.httpGet(String.format(URL_ORG_APP_INFO, accessToken, config.getAgentId()), WechatWorkAppInfoDto.class);
        if (dto != null && dto.getErrcode() == 0) {
            if (dto.getUsers() != null && CollectionUtils.isNotEmpty(dto.getUsers().getUser())) {
                config.setVisibleUsers(dto.getUsers().getUser().stream().map(WechatWorkAppInfoUserInfoDto::getUserId).filter(Objects::nonNull).collect(Collectors.joining(",")));
            }
            if (dto.getDepartments() != null && CollectionUtils.isNotEmpty(dto.getDepartments().getDepartmentIds())) {
                config.setVisibleDepartments(dto.getDepartments().getDepartmentIds().stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(",")));
            }
            if (dto.getTags() != null && CollectionUtils.isNotEmpty(dto.getTags().getTagIds())) {
                config.setVisibleTags(dto.getTags().getTagIds().stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(",")));
            }
            authWechatWorkService.updateConfig(entity, config);
        }
    }

    private void checkUserHasApp(WechatWorkConfig config, String openId) {
        // 先检查是否在 WechatWorkConfig#visibleUsers
        if (StringUtils.isNotEmpty(config.getVisibleUsers())) {
            if (Arrays.asList(config.getVisibleUsers().split(",")).contains(openId)) {
                // 是授权用户，直接返回
                return;
            }
        }
        String accessToken = null;
        // 然后检查是否在 WechatWorkConfig#visibleDepartments
        if (StringUtils.isNotEmpty(config.getVisibleDepartments())) {
            String[] ids = config.getVisibleDepartments().split(",");
            for (String id : ids) {
                if (NumberUtils.isDigits(id)) {
                    if (accessToken == null) {
                        accessToken = weChatWorkAccountHelper.getOrgAccessToken(config);
                    }
                    if (getUserByDepartmentId(accessToken, Integer.parseInt(id)).contains(openId)) {
                        // 授权部门包含此用户，是授权用户，直接返回
                        return;
                    }
                }
            }
        }
        // 然后检查是否在 WechatWorkConfig#visibleTags
        if (StringUtils.isNotEmpty(config.getVisibleTags())) {
            String[] ids = config.getVisibleTags().split(",");
            for (String id : ids) {
                if (NumberUtils.isDigits(id)) {
                    if (accessToken == null) {
                        accessToken = weChatWorkAccountHelper.getOrgAccessToken(config);
                    }
                    if (getUserByTagId(accessToken, Integer.parseInt(id)).contains(openId)) {
                        // 授权标签包含此用户，是授权用户，直接返回
                        return;
                    }
                }
            }
        }
        log.error("用户（{}）不在指定的应用（{}的{}）的可见返回", openId, config.getCompanyId(), config.getAgentId());
        throw ToastMessageHelper.badRequestException(WECHAT_WORK_NO_AGENT);
    }

    private List<String> getUserByDepartmentId(String accessToken, int departmentId) {
        WechatWorkUsersDto dto = weChatWorkAccountHelper.httpGet(String.format(URL_ORG_DEPARTMENT_USERS, accessToken, departmentId), WechatWorkUsersDto.class);
        if (dto != null && dto.getErrcode() == 0 && dto.getUsers() != null) {
            return dto.getUsers().stream().map(WechatWorkAppInfoUserInfoDto::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    private List<String> getUserByTagId(String accessToken, int tagId) {
        WechatWorkUsersDto dto = weChatWorkAccountHelper.httpGet(String.format(URL_ORG_TAG_USERS, accessToken, tagId), WechatWorkUsersDto.class);
        if (dto != null && dto.getErrcode() == 0 && dto.getUsers() != null) {
            return dto.getUsers().stream().map(WechatWorkAppInfoUserInfoDto::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    private LoginUser parseUserInfo(WechatWorkLoginDto data) {
        if (data == null) {
            return null;
        }
        LoginUser loginUser = new LoginUser();
        Optional.ofNullable(data.getUserInfo()).ifPresent(i -> {
            loginUser.userId = i.getUserId();
        });
        Optional.ofNullable(data.getCorpInfo()).ifPresent(i -> {
            loginUser.corpId = i.getCorpId();
        });
        if (StringUtils.isEmpty(loginUser.userId) || StringUtils.isEmpty(loginUser.corpId)) {
            return null;
        }
        return loginUser;
    }


    @NoArgsConstructor
    @AllArgsConstructor
    private static class LoginUser {
        String corpId;
        String userId;
    }

    private static class BindOrgCorp {
        String corpId;
        String agentId;
        String permanentCode;

        String accessToken;
        String name;
        String logo;

        String userId;

        public BindOrgCorp(String corpId, String agentId, String permanentCode, String accessToken, String name, String logo, String userId) {
            this.corpId = corpId;
            this.agentId = agentId;
            this.permanentCode = permanentCode;
            this.accessToken = accessToken;
            this.name = name;
            this.logo = logo;
            this.userId = userId;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BindCode {
        private String bindCode;
    }

    @Getter
    @Setter
    public static class WechatWorkDto {
        private int errcode;
        private String errmsg;
    }

    @Getter
    @Setter
    public static class WechatWorkQrCodeDto extends WechatWorkDto {
        @JsonProperty("qrcode")
        private String qrcode;
    }

    @Getter
    @Setter
    public static class WechatWorkPreAuthCodeDto extends WechatWorkDto {
        @JsonProperty("pre_auth_code")
        private String preAuthCode;
    }

    public abstract static class WechatWorkAccessTokenDto extends WechatWorkDto {
        public abstract String getAccessToken();
    }

    @Getter
    @Setter
    public static class WechatWorkCorpAccessTokenDto extends WechatWorkAccessTokenDto {
        @JsonProperty("provider_access_token")
        private String accessToken;
    }

    @Getter
    @Setter
    public static class WechatWorkSuiteAccessTokenDto extends WechatWorkAccessTokenDto {
        @JsonProperty("suite_access_token")
        private String accessToken;
    }

    @Getter
    @Setter
    public static class WechatWorkOrgAccessTokenDto extends WechatWorkAccessTokenDto {
        @JsonProperty("access_token")
        private String accessToken;
    }

    @Getter
    @Setter
    public static class WechatWorkLoginDto extends WechatWorkDto {
        @JsonProperty("usertype")
        private int userType;
        @JsonProperty("user_info")
        private WechatWorkUserDto userInfo;
        @JsonProperty("corp_info")
        private WechatWorkCropDto corpInfo;
    }

    @Getter
    @Setter
    public static class WechatWorkMobileLoginDto extends WechatWorkDto {
        @JsonProperty("CorpId")
        private String corpId;
        @JsonProperty("UserId")
        private String userId;
    }

    @Getter
    @Setter
    public static class WechatWorkUserDto {
        @JsonProperty("userid")
        private String userId;
    }

    @Getter
    @Setter
    public static class WechatWorkCropDto {
        @JsonProperty("corpid")
        private String corpId;
    }

    @Getter
    @Setter
    public static class WechatWorkAppInfoDto extends WechatWorkDto {
        @JsonProperty("allow_userinfos")
        private WechatWorkAppInfoUserDto users;
        @JsonProperty("allow_partys")
        private WechatWorkAppInfoDepartmentDto departments;
        @JsonProperty("allow_tags")
        private WechatWorkAppInfoTagDto tags;
    }

    @Getter
    @Setter
    public static class WechatWorkAppInfoUserDto {
        @JsonProperty("user")
        private List<WechatWorkAppInfoUserInfoDto> user;
    }

    @Getter
    @Setter
    public static class WechatWorkAppInfoDepartmentDto {
        @JsonProperty("partyid")
        private List<Integer> departmentIds;
    }

    @Getter
    @Setter
    public static class WechatWorkAppInfoTagDto {
        @JsonProperty("tagid")
        private List<Integer> tagIds;
    }

    @Getter
    @Setter
    public static class WechatWorkAppInfoUserInfoDto {
        @JsonProperty("userid")
        private String userId;
    }

    // 用户列表，（部门用户列表，标签用户列表）
    @Getter
    @Setter
    public static class WechatWorkUsersDto extends WechatWorkDto {
        @JsonProperty("userlist")
        private List<WechatWorkAppInfoUserInfoDto> users;
    }


}
