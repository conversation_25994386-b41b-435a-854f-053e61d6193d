package org.befun.auth.provider.local;

import org.befun.auth.constant.LoginPlatform;
import org.befun.auth.constant.OrganizationConfigMfaType;
import org.befun.auth.dto.AppVersionDto;
import org.befun.auth.dto.LoginPasswordMfaDto;
import org.befun.auth.dto.LoginPasswordRequestDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.provider.BaseAuthProvider;
import org.befun.auth.provider.ILoginStatus;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.utils.PasswordHelper;
import org.befun.auth.utils.PasswordLoginHelper;
import org.befun.auth.utils.StringHelper;
import org.befun.core.utils.RegHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.function.Consumer;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;
import static org.befun.extension.toast.ToastMessageHelper.businessException;

@Service("befun_auth_password")
@ConditionalOnProperty(name = "befun.auth.enable-provider", havingValue = "true")
public class PasswordProvider extends BaseAuthProvider implements ILoginStatus<LoginPasswordRequestDto> {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PasswordLoginHelper passwordLoginHelper;

    @Override
    public boolean hasActiveToken(LoginPasswordRequestDto dto, String app) {
        User user = getUser(dto.getUsername(), dto.getPassword(), i -> {
        });
        return checkOrgAndHasActiveToken(dto.getPlatform(), user, app);
    }

    @Override
    public Object login(String source, String app, Object callback) {
        LoginPasswordRequestDto dto = (LoginPasswordRequestDto) callback;
        User user = getUser(dto.getUsername(), dto.getPassword(), i -> passwordLoginHelper.checkGraphCaptcha(dto.getCaptcha(), i));
        Organization org = requireOrgById(user.getOrgId());
        AppVersionDto orgVersion = checkOrg(org, user, app);
        // mfa
        checkMfa(dto.getPlatform(), user);
        Object response = loginSuccess(dto.getPlatform(), org, user, orgVersion, app);
        passwordLoginHelper.clearFailTimes(user.getId());
        passwordLoginHelper.clearGraphCaptcha(dto.getCaptcha());
        return response;
    }

    private User getUser(String account, String password, Consumer<Integer> checkCaptcha) {
        User user = getUser(account);

        // 登录次数限制
        int failTimes = passwordLoginHelper.checkFailTimes(user);

        // 图形验证码校验
        checkCaptcha.accept(failTimes);

        if (user == null || !PasswordHelper.verify(password, user.getPassword())) {
            // 增加登录错误次数
            passwordLoginHelper.incrementFailTimes(user, LOGIN_PASSWORD_ERROR_TIMES);
            throw badRequestException(LOGIN_PASSWORD_ERROR);
        }

        //先校验状态，然后校验密码是否正确
        checkUser(user);
        return user;
    }

    private User getUser(String account) {
        User user = null;
        if (RegHelper.isMobile(account)) {
            user = userRepository.findFirstByMobile(account);
            if (user == null) {
                throw badRequestException(LOGIN_PASSWORD_ERROR);
            }
        } else if (RegHelper.isEmail(account)) {
            user = userRepository.findFirstByEmail(account);
            if (user == null) {
                throw badRequestException(LOGIN_PASSWORD_ERROR);
            }
        }
        addCopiedTenantData(user);
        return user;
    }

    private void checkMfa(LoginPlatform platform, User user) {
        if (user != null) {
            Long orgId = user.getOrgId();
            Long userId = user.getId();
            OrganizationConfigMfaType mfaType = passwordLoginHelper.getOrgMfaType(orgId);
            boolean needMfa = false;
            if (mfaType == OrganizationConfigMfaType.EVERY_TIME) {
                needMfa = true;
            } else if (mfaType == OrganizationConfigMfaType.SKIP_DAY_15) {
                LocalDateTime lastTime = passwordLoginHelper.getUserMfaInfo(orgId, userId);
                if (lastTime == null || lastTime.plusDays(15).isBefore(LocalDateTime.now())) {
                    needMfa = true;
                }
            }
            if (needMfa) {
                throw businessException(LOGIN_PASSWORD_MFA, mfaParam(platform, user)).codeOk();
            }
        }
    }

    private LoginPasswordMfaDto mfaParam(LoginPlatform platform, User user) {
        LoginPasswordMfaDto dto = new LoginPasswordMfaDto();
        dto.setMfaToken(platform.name() + ".mfa" + UUID.randomUUID());
        if (RegHelper.isMobile(user.getMobile())) {
            dto.setMfaVerifyType("mobile");
            dto.setMobile(StringHelper.maskMobile(user.getMobile()));
        } else if (RegHelper.isEmail(user.getEmail())) {
            dto.setMfaVerifyType("email");
            dto.setEmail(StringHelper.maskEmail(user.getEmail()));
        } else {
            throw badRequestException(LOGIN_PASSWORD_ERROR);
        }
        passwordLoginHelper.cacheMfaParams(dto, platform, user);
        return dto;
    }
}
