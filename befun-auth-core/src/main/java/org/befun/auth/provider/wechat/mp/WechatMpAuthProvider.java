package org.befun.auth.provider.wechat.mp;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpMessageHandler;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpUserService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import me.zhyd.oauth.request.AuthRequest;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.WechatMPSourceProperty;
import org.befun.auth.configuration.WechatMPTemplateParameterProperty;
import org.befun.auth.configuration.WechatMPTemplateProperty;
import org.befun.auth.constant.AuthBindType;
import org.befun.auth.dto.AuthBindStateDto;
import org.befun.auth.dto.AuthPingResponseDto;
import org.befun.auth.dto.ThirdPartyBindDto;
import org.befun.auth.dto.ThirdPartyUserInfoDto;
import org.befun.auth.event.AuthBindEvent;
import org.befun.auth.provider.BaseOauthProvider;
import org.befun.core.exception.BadRequestException;
import org.befun.core.security.UserPrincipal;
import org.befun.core.service.SpringContextHolder;
import org.befun.core.template.TemplateEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Component("befun_auth_wechat_mp")
public class WechatMpAuthProvider extends BaseOauthProvider {
    public static final String SOURCE = "wechat_mp";

    @Autowired
    private AuthProperties authProperties;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private Map<String, WechatMPSourceProperty> wechatMPSourcePropertyMap = new HashMap<>();
    private Map<String, WxMpDefaultConfigImpl> configs = new HashMap<>();
    private Map<String, Map<String, WechatTemplateDto>> templatesMap = new HashMap<>();
    private Map<String, WxMpService> wxMpServices = new HashMap<>();
    private Map<String, WxMpMessageRouter> messageRouters = new HashMap<>();

    @PostConstruct
    private void setup() {
        String prefix = authProperties.getRedisPrefix();
        List<WechatMPSourceProperty> wechatMpSources = authProperties.getWechatMpSources();

        wechatMPSourcePropertyMap = wechatMpSources.stream()
                .collect(Collectors.toMap(WechatMPSourceProperty::getName, Function.identity()));

        wechatMpSources.forEach(wechatMpSource -> {
            String name = wechatMpSource.getName();

            // 初始化所有微信模版消息
            // app->templateName => WechatTemplateDto
            Map<String, WechatTemplateDto> templates = new HashMap<>();
            for (WechatMPTemplateProperty templateProp : wechatMpSource.getTemplates()) {
                String templateName = templateProp.getName();
                Map<String, Object> contentTemplate = templateProp.getParameters().stream()
                        .collect(
                                Collectors.toMap(
                                        WechatMPTemplateParameterProperty::getName,
                                        WechatMPTemplateParameterProperty::getValue
                                ));
                WechatTemplateDto template = WechatTemplateDto.builder()
                        .id(templateProp.getId())
                        .name(templateName)
                        .url(templateProp.getUrl())
                        .contentTemplate(contentTemplate)
                        .build();
                templates.put(templateName, template);
            }
            templatesMap.put(name, templates);

            // config default mp
            WxMpDefaultConfigImpl config = new WxMpRedisConfigImpl(new RedisTemplateWxRedisOps(redisTemplate), prefix);
            config.setAppId(wechatMpSource.getAppId());   // 设置微信公众号的appid
            config.setSecret(wechatMpSource.getAppSecret()); // 设置微信公众号的app corpSecret
            config.setToken(wechatMpSource.getToken());   // 设置微信公众号的token
            config.setAesKey(wechatMpSource.getAesKey()); // 设置微信公众号的EncodingAESKey
            configs.put(name, config);

            // init mp service
            WxMpServiceImpl wxMpService = new WxMpServiceImpl();
            wxMpService.setWxMpConfigStorage(config);
            wxMpServices.put(name, wxMpService);

            WxMpMessageRouter messageRouter = new WxMpMessageRouter(wxMpService)
                    .rule()
                    .msgType(WxConsts.XmlMsgType.EVENT)
                    .handler(new WechatMpEventHandler(name))
                    .end();
            messageRouters.put(name, messageRouter);
        });
    }

    @Override
    protected AuthRequest buildRequest(String source) {
        return null;
    }

    @SneakyThrows
    @Override
    public ThirdPartyBindDto bindByQrCode(String app, String source, String bindId) {
        WxMpService mpService = wxMpServices.get(app);
        if (mpService == null) {
            throw new BadRequestException("invalid app");
        }
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        UserPrincipal principal = (UserPrincipal) authentication.getPrincipal();
        AuthBindStateDto metaDto = new AuthBindStateDto().builder()
                .scene("bind")
                .userId(principal.getUser().getId())
                .app(app)
                .source(source)
                .build();
        String state = metaDto.toString();
        log.info("wechat mp generate bind qrcode for state: {}", state);
        WxMpQrCodeTicket ticket = mpService.getQrcodeService().qrCodeCreateTmpTicket(state, authProperties.getBindExpirationInSeconds());
        ThirdPartyBindDto tbd = ThirdPartyBindDto.builder()
                .state(state)
                .qrCode(ticket.getUrl())
                .build();
        return tbd;
    }

    @Override
    public String authCodeByQrCode(String app, String source, String code) {
        WxMpService mpService = wxMpServices.get(app);
        if (mpService == null) {
            throw new BadRequestException("invalid app");
        }
        try {
            WxMpQrCodeTicket ticket = mpService.getQrcodeService().qrCodeCreateTmpTicket(code, authProperties.getBindExpirationInSeconds());
            return ticket.getUrl();
        } catch (WxErrorException e) {
            log.error("authCodeByQrCode error app={}, source={}, code={}", app, source, code);
        }
        return null;
    }

    /**
     * ping
     *
     * @param request
     * @param response
     * @return
     */
    @SneakyThrows
    @Override
    public AuthPingResponseDto ping(String app, HttpServletRequest request, HttpServletResponse response) {
        WxMpService mpService = wxMpServices.get(app);
        WxMpConfigStorage config = configs.get(app);
        if (mpService == null || config == null) {
            throw new BadRequestException("invalid app");
        }

        String accessToken = config.getAccessToken();
        AuthPingResponseDto responseDto = AuthPingResponseDto.builder()
                .appId(config.getAppId())
                .token(accessToken.substring(accessToken.length() - 4))
                .build();
        return responseDto;
    }

    /**
     * 响应微信公众号服务验证
     *
     * @param request
     * @param response
     */
    @SneakyThrows
    @Override
    public Object verify(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        WxMpService mpService = wxMpServices.get(app);
        if (mpService == null) {
            throw new BadRequestException("invalid app");
        }
        response.setContentType("text/html;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_OK);

        String signature = request.getParameter("signature");
        String nonce = request.getParameter("nonce");
        String timestamp = request.getParameter("timestamp");
        String echoStr = request.getParameter("echostr");

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echoStr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        log.info("received message from wechat mp timestamp:{} nonce:{} echoStr:{}", timestamp, nonce, echoStr);

        if (StringUtils.isNotBlank(echoStr)) {
            if (!mpService.checkSignature(timestamp, nonce, signature)) {
                // 消息签名不正确，说明不是公众平台发过来的消息
                response.getWriter().println("非法请求");
                return null;
            }
            response.getWriter().print(echoStr);
            response.getWriter().flush();
        }
        return null;
    }

    /**
     *
     */
    @SneakyThrows
    @Override
    public void notifyUser(String app, String openId, String templateName, Map<String, Object> parameters) {
        WxMpService mpService = wxMpServices.get(app);
        if (mpService == null) {
            throw new BadRequestException("invalid app");
        }

        if (templatesMap.get(app) == null || templatesMap.get(app).get(templateName) == null) {
            throw new BadRequestException("invalid template");
        }
        log.info("send wechat_mp template message to user: {} template: {}", openId, templateName);

        WechatTemplateDto template = templatesMap.get(app).get(templateName);
        String url = TemplateEngine.renderTextTemplate(
                template.getUrl(),
                parameters
        );
        List<WxMpTemplateData> params = buildMessage(template.getContentTemplate(), parameters);
        WxMpTemplateMessage message = WxMpTemplateMessage.builder()
                .templateId(template.getId())
                .url(url)
                .toUser(openId)
                .data(params)
                .build();
        if (!url.isEmpty()) {
            message.setUrl(url);
        }
        mpService.getTemplateMsgService().sendTemplateMsg(message);
    }

    /**
     * message handler
     *
     * @param request
     * @param response
     */
    @SneakyThrows
    @Override
    public void onMessage(String app, HttpServletRequest request, String requestBody, HttpServletResponse response) {
        WxMpService mpService = wxMpServices.get(app);
        WxMpMessageRouter messageRouter = messageRouters.get(app);
        if (mpService == null || messageRouter == null) {
            throw new BadRequestException("invalid app");
        }

        response.setContentType("text/html;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_OK);

        String signature = request.getParameter("signature");
        String nonce = request.getParameter("nonce");
        String timestamp = request.getParameter("timestamp");
        String encrypt_type = request.getParameter("encrypt_type");

        WxMpXmlMessage inMessage;
        log.info("received message from wechat mp timestamp:{} nonce:{}", timestamp, nonce);
        if (StringUtils.isBlank(encrypt_type)) {
            inMessage = WxMpXmlMessage.fromXml(requestBody);
        } else {
            inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, mpService.getWxMpConfigStorage(),
                    timestamp, nonce, signature);
            log.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
        }
        messageRouter.route(inMessage);
        // 认证服务不做消息任何回复处理
        response.getWriter().print("");
        response.getWriter().flush();
    }

    /**
     * wechat mp event handler
     */
    private class WechatMpEventHandler implements WxMpMessageHandler {
        private String app;

        public WechatMpEventHandler(String app) {
            this.app = app;
        }

        @Override
        public WxMpXmlOutMessage handle(WxMpXmlMessage wxMpXmlMessage, Map<String, Object> map, WxMpService wxMpService, WxSessionManager wxSessionManager) throws WxErrorException {
            if (!wxMpXmlMessage.getMsgType().equals(WxConsts.XmlMsgType.EVENT)) {
                return null;
            }

            String event = wxMpXmlMessage.getEvent();
            String eventKey = wxMpXmlMessage.getEventKey().replace("qrscene_", "");
            String openId = wxMpXmlMessage.getFromUser();

            WxMpUserService userService = wxMpService.getUserService();
            WxMpUser userInfo = userService.userInfo(openId);

            ApplicationEventPublisher eventPublisher = SpringContextHolder.getApplicationContext();
            AuthBindEvent bindEvent = null;

            switch (event) {
                case WxConsts.EventType.SCAN:
                case WxConsts.EventType.SUBSCRIBE:
                    if (eventKey.startsWith("authCode:")) {
                        bindEvent = AuthBindEvent.builder()
                                .app(this.app)
                                .bindType(AuthBindType.AUTH_CODE)
                                .openId(openId)
                                .source(SOURCE)
                                .eventKey(eventKey)
                                .build();
                        break;
                    }
                    AuthBindStateDto eventMetaDto = new AuthBindStateDto(eventKey);
                    log.info("handle wechat mp event user: {} event: {} eventKey: {}", wxMpXmlMessage.getFromUser(), event, eventKey);
                    Long userId = eventMetaDto.getUserId();
                    // 订阅消息
                    ThirdPartyUserInfoDto userInfoDto = ThirdPartyUserInfoDto.builder()
//                            .province(userInfo.getProvince())
//                            .city(userInfo.getProvince())
//                            .country(userInfo.getCountry())
                            .nickname(userInfo.getNickname())
                            .remark(userInfo.getRemark())
                            .build();
                    bindEvent = AuthBindEvent.builder()
                            .app(this.app)
                            .bindType(AuthBindType.BINDING)
                            .openId(openId)
                            .userId(userId)
                            .source(SOURCE)
                            .userInfoDto(userInfoDto)
                            .build();
                    break;
                case WxConsts.EventType.UNSUBSCRIBE:
                    // 取消订阅消息
                    bindEvent = AuthBindEvent.builder()
                            .app(this.app)
                            .bindType(AuthBindType.UNBINDING)
                            .openId(openId)
                            .source(SOURCE)
                            .build();
                    break;
            }

            if (bindEvent != null) {
                log.debug("publish auth binding event for openId:{} app: {}", openId, app);
                eventPublisher.publishEvent(bindEvent);
            }
            return null;
        }
    }

    /**
     * @param template
     * @param parameters
     * @return
     */
    private List<WxMpTemplateData> buildMessage(Map<String, Object> template, Map<String, Object> parameters) {
        List<WxMpTemplateData> params = new ArrayList<>();

        Map<String, Object> result = TemplateEngine.renderJsonTemplate(
                template,
                parameters);

        for (Map.Entry<String, Object> entry : result.entrySet()) {
            params.add(new WxMpTemplateData(entry.getKey(), entry.getValue().toString()));
        }

        return params;
    }


    public WxMpService getWxMpService(String app) {
        return (WxMpService) this.wxMpServices.get(app);
    }


}
