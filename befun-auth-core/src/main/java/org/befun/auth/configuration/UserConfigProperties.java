package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.QueryType;
import org.befun.auth.constant.QueryValueType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration()
@ConfigurationProperties(prefix = "befun.auth.user-config")
@Getter
@Setter
public class UserConfigProperties {

    private List<CustomerQuery> customerQuery = new ArrayList<>();
    private Map<String, List<CustomerQueryType>> customerQueryType = new HashMap<>();
    private Map<String, String> customerQueryExtendField = new HashMap<>();

    @Getter
    @Setter
    public static class CustomerQuery {
        private String propertyName;
        private String propertyLabel;
        private String propertyType = "string"; // string date datetime long arrayString
        private String propertySource;
        private String propertyColumn;
        private String queryItemType;
        private boolean enableBatchUpdate;
        private String inputType;
    }

    @Getter
    @Setter
    public static class CustomerQueryType {
        private String queryTypeLabel;
        private QueryType queryType;
        private QueryValueType queryValueType;
    }
}
