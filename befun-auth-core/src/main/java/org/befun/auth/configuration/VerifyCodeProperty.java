package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;


@Getter
@Setter
public class VerifyCodeProperty {

    private int codeExpireMinutes = 5; // 验证码有效时长（分钟）
    private int codeFreezeMinutes = 1; // 发送验证码间隔时长（分钟）
    private int codeCacheHours = 24;   // 验证码要保留一段时间，这期间判断验证码过期
    private Map<String, String> smsTemplateName = new HashMap<>();
    private Map<String, String> emailTemplateName = new HashMap<>();

}
