package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Getter
@Setter
public class YouzanProperty {

    private String clientId;              // 服务商id
    private String clientSecret;          // 服务商secret
    private boolean enableMock;
    private Map<String, String> supportEvents = new LinkedHashMap<>();
}
