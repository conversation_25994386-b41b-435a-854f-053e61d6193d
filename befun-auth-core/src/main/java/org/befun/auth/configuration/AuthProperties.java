package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = "befun.auth")
@Getter
@Setter
public class AuthProperties {
    private String redisPrefix = "befun";

    /* 默认绑定时效(秒) */
    private int bindExpirationInSeconds = 600;
    /* auth 服务域名 */
    private String domain;
    /* 登录有效时长（小时） 2*24 */
    private int tokenExpireHours = 48;
    /* 是否启用认证 */
    private boolean enableProvider = false;
    /* 图形验证码出现条件 */
    private int graphCaptchaInFailTimes = -1;
    /* 验证码 */
    private VerifyCodeProperty verifyCode = new VerifyCodeProperty();
    /* 成员邀请 */
    private InvitationProperty invitation = new InvitationProperty();
    /* 内容审核 */
    private ContentAuditProperty contentAudit = new ContentAuditProperty();
    /* 查询当前公网 ip */
    private ServiceIpProperty serviceIp = new ServiceIpProperty();

    /* 支持的app */
    @NestedConfigurationProperty
    private List<String> apps = new ArrayList<>();

    @NestedConfigurationProperty
    private List<WechatMPSourceProperty> wechatMpSources = new ArrayList<>();

    private CasProperty cas = new CasProperty();
    private WechatWorkProperty wechatWork = new WechatWorkProperty();
    private YouzanProperty youzan = new YouzanProperty();
    private OAuthProperty oauth = new OAuthProperty();
    private SamlProperty saml = new SamlProperty();

    private Map<String, String> orgDefaultConfig = new HashMap<>();

    private String rsaPrivateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJdc2rxxZWm5j4Qchn8gxrlODa1S60LORSaPKREpOiq0ZQ5Yssr1XY2IEVZiROKYunk5Ia+8/dLI4EypeoYVE2MPBcq0x0dgAgkFFudyTcNWm4XmOUEmjQrm4KsC2uo+5xwKXADLVX8bBPaezUDwuBIuOH0DuInStxCqLFej620vAgMBAAECgYEAlmw3DVN8P2ej0t0uZKTEGWASg4+GMiwf2MQpQd7A6bPBQlKvzLevs3noEPs0DiVOHC7E5YtijNoI3ucG1r2XNy62qZTF+siBzBBzY82i28aITxqXBgTiauooXew3zfh6YS4QL0rG7vT5gmIw/1L3ZYCDI9e/zHclZ02ev/6BUkECQQDdjIc+0FYPwbJ/vBg5a8xX/Nk6mj6gmjW0hs1v39qKyYwLpRzvpyF2T5JEN8G8LX42MBGI6OUhlhm4NZAxAZPfAkEAruZWJirUC1h6O76dQJuN6pTPY8j0b7McVpoTU6Ti2LE4g4gqZphzJulrgjr+J+nUkJGw0+dL/YI6wHo+VDHQsQJAMcZUzGP6MegOtmwH9T6m1k3UcDofP+3liv2OQkhEzVGiySr7aUxfVDlkXzc3q5N8+6epCN1qPTV/6cb+8kOseQJBAIkveJFVdJ81liL4n4XGvakrcgSV4k0fE3JGRjSMl7ZaQ7a//moc1+3ElyQ+O455FdcTv7xmpy9Aj+7bs7n8nPECQGIcYoFyHvbHLEwcFqDiOoQuP9kJhbfnXLDpWeIa0lMN+jskPVtLmsgCaD1aJCxXxbJX12rtGXmEQE0OdcJYKTY=";
    private String rsaPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCXXNq8cWVpuY+EHIZ/IMa5Tg2tUutCzkUmjykRKToqtGUOWLLK9V2NiBFWYkTimLp5OSGvvP3SyOBMqXqGFRNjDwXKtMdHYAIJBRbnck3DVpuF5jlBJo0K5uCrAtrqPuccClwAy1V/GwT2ns1A8LgSLjh9A7iJ0rcQqixXo+ttLwIDAQAB";
    private String rsaModPadding = "RSA/ECB/PKCS1Padding";
    private String aesModPadding = "AES/CBC/PKCS7Padding";
    private String sm2PrivateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg+0gZJl1UDOPTgKIeFfGh786AsMn6tOmuQjegJDluDBugCgYIKoEcz1UBgi2hRANCAARqq30YZg24ex1iVQYowMQsFaQLbsDY1bj+Hw+yhxPKdSbK9MfwHeVaPWfmXro2m2v48OlAvrn2TKhGhWW9HcXG";
    private String sm2PublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEaqt9GGYNuHsdYlUGKMDELBWkC27A2NW4/h8PsocTynUmyvTH8B3lWj1n5l66Nptr+PDpQL659kyoRoVlvR3Fxg==";
}
