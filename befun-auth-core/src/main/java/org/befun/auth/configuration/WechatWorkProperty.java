package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class WechatWorkProperty {

    private String corpId;              // 服务商id
    private String corpSecret;          // 服务商secret
    private String suiteId;             // 应用id
    private String suiteSecret;         // 应用secret
    private String aesKey;              // aes解密key
    private String token;               // 服务器验证token
    private String loginCallbackUrl;    // 登录回调地址
    private String loginUserType = "member";

    private String redirectDomain;      // 回调域名

    private int bindAuthType = 0;       // 绑定类型 授权类型：0 正式授权， 1 测试授权。 默认值为0。注意，请确保应用在正式发布后的授权类型为“正式授权”
    private String bindCallbackUrl;     // 绑定回调地址

    private String memberCallbackUrl;   // 绑定成员回调地址
    private String memberUserType = "member";

    private String orderBuyerUserId;
    private int orderMonth = 12;

    @NestedConfigurationProperty
    private List<WechatWorkTemplate> templates = new ArrayList<>();

    @Getter
    @Setter
    public static class WechatWorkTemplate{
        private String id;
        private String name;
        private String url;
        @NestedConfigurationProperty
        private List<WechatWorkTemplateParameter> parameters = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class WechatWorkTemplateParameter{
        private String name;
        private String value;
    }

}
