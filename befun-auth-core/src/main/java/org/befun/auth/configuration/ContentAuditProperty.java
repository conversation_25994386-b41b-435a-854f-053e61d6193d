package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class ContentAuditProperty {

    private boolean enabled = true;
    private boolean enableAuditText = true;
    private boolean enableAuditImage = true;
    private int textSplitSize = 6000;   // 文本内容分片长度
    private int textRetainDays = 15;    // 文本内容检测结果的有效时间
    private int timeoutMs = 30000;
    private int parallelism = 10;
    private String appId;
    private String apiKey;
    private String secretKey;
    private String imageUrlPattern = "https?://[-0-9a-zA-Z.:]+[-\\u4e00-\\u9fa5_0-9a-zA-Z./@#=?&%]*";
    private String imageSuffix = ".png,.jpg,.jpeg,.bmp,.gif,.webp,.tiff";
    private int accessTokenWaitMs = 100;
    private int accessTokenWaitTimes = 10;
    private int accessTokenExpireDay = 29;

}
