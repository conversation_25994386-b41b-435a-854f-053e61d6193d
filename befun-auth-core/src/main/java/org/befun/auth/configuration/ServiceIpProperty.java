package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Getter
@Setter
public class ServiceIpProperty {

    private boolean enabled = true;
    private String url = "https://ifconfig.me/ip";
    private String parsePattern;

    public String parseServiceIp() {
        if (enabled) {
            try {
                String s = Request.Get(url).connectTimeout(3000).socketTimeout(3000).execute().returnContent().toString();
                if (StringUtils.isNotEmpty(parsePattern)) {
                    Matcher matcher = Pattern.compile(parsePattern).matcher(s);
                    if (matcher.find()) {
                        s = matcher.group(1);
                    }
                }
                Pattern pattern = Pattern.compile("^\\d{1,3}.\\d{1,3}.\\d{1,3}.\\d{1,3}$");
                if (pattern.matcher(s).matches()) {
                    return s;
                }
            } catch (Throwable e) {
                // ignore
            }
        }
        return null;
    }
}
