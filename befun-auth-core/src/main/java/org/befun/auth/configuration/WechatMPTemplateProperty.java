package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class WechatMPTemplateProperty {
    /* template name */
    private String name;

    /* template id */
    private String id;

    /* template url */
    private String url;

    @NestedConfigurationProperty
    private List<WechatMPTemplateParameterProperty> parameters = new ArrayList<>();
}

