package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class WechatMPSourceProperty {
    /* auth source name */
    private String name;

    /* auth source appId */
    private String appId;

    /* client secret */
    private String appSecret;

    /* client token */
    private String token;

    /* redirect aesKey */
    private String aesKey;

    @NestedConfigurationProperty
    private List<WechatMPTemplateProperty> templates = new ArrayList<>();
}

