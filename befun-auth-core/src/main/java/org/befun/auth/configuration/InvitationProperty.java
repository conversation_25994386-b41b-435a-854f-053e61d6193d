package org.befun.auth.configuration;

import lombok.Getter;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
public class InvitationProperty {
    private Duration expireTime = Duration.ofHours(72);
    private String inviteUrl;
    private String emailTemplateName;
    private String emailTemplateNameWechatWork;
    private String emailTemplateNameByUserInfo;
    private String copyText;
}
