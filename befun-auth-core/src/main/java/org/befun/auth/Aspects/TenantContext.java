package org.befun.auth.Aspects;

import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.befun.auth.annotations.AddTenantContext;
import org.befun.auth.constant.TenantContext.TenantFields;
import org.befun.auth.service.OpenTenantContextService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Aspect
public class TenantContext {
    @Autowired
    private OpenTenantContextService openTenantContextService;

    @Pointcut("@annotation(org.befun.auth.annotations.AddTenantContext)")
    public void addTenant() {
    }

    @Before("addTenant()")
    public void before(JoinPoint point) throws Throwable {
        try {
            MethodSignature sig = (MethodSignature) point.getSignature();
            Method method = sig.getMethod();
            AddTenantContext annotation = method.getAnnotation(AddTenantContext.class);
            for (TenantFields tenantFields : annotation.value()) {
                switch (tenantFields) {
                    case ISAdmin:
                        openTenantContextService.addIsAdmin();
                        break;
                    case SubDepartmentId:
                        openTenantContextService.addSubDepartmentIdList();
                        break;
                    case Permissions:
                        openTenantContextService.addPermissions();
                        break;
                    case OrgId:
                        openTenantContextService.addTenant();
                        break;
                    case RoleIds:
                        openTenantContextService.addRoleIds();
                        break;
                    case IsTop:
                        openTenantContextService.addIsTop();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("addTenantContext error", e);
        } finally {
            log.info("addTenantContext success");
        }
    }
}
