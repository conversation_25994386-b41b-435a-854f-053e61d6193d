package org.befun.auth.dto.auth;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.service.auth.config.SamlConfig;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class SamlAuthDto extends ThirdPartyAuthDto<SamlConfig> {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否开启白名单：1 是 0 否")
    private Integer enableWhiteList;
}
