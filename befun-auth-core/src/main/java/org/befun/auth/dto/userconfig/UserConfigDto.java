package org.befun.auth.dto.userconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

@Getter
@Setter
public class UserConfigDto {

    @Valid
    @Schema(description = "用户配置-客户搜索条件")
    private List<UserConfigCustomerQueryDto> customerQuery;

    @Schema(description = "mfa安全验证时间")
    private String mfa;
}
