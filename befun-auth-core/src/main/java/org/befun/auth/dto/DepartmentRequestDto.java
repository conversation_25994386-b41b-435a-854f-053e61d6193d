package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class DepartmentRequestDto {

    @Schema(description = "部门id，新增时无参数，修改时必须")
    private Long id;

    @NotEmpty
    @Size(max = 20, message = "部门名称不超过20个字符")
    @Schema(description = "部门名称")
    private String title;

    @Schema(description = "部门父id")
    private Long pid;

    @Size(max = 20, message = "部门编号不超过20个字符")
    @Pattern(regexp = "[^\\u4e00-\\u9fa5]*", message = "部门编号只能输入字母、数字或字符")
    @Schema(description = "部门编号")
    private List<String> code;

    @Schema(description = "等效部门部门")
    private List<String> equivalentCode;

}
