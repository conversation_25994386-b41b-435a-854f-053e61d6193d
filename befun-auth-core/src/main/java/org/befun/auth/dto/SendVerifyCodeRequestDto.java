package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SendVerifyCodeRequestDto {

    @NotEmpty
    @Schema(description = "账号： 手机号或者邮箱或者mfaToken")
    private String account;

    @Schema(hidden = true, description = "本次发送的唯一标识，用作发送成功时的缓存key的后缀，如果没有此参数，则使用 account")
    private String uniqueKey;

}
