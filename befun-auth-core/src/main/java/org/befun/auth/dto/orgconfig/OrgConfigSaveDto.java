package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.OrganizationConfigType;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigSaveDto {

    @Schema(description = "需要保存的企业配置类型")
    private OrganizationConfigType type;

    @Schema(description = "v1.9.2 需要保存的企业配置类型, 支持同时保存多个配置项, 最终保存的类型会把type和types合并")
    private List<OrganizationConfigType> types;

    @Valid
    @NotNull
    @Schema(description = "企业配置内容")
    private OrgConfigDto config;


    public Set<OrganizationConfigType> allTypes() {
        Set<OrganizationConfigType> set = new HashSet<>();
        if (type != null) {
            set.add(type);
        }
        if (CollectionUtils.isNotEmpty(types)) {
            set.addAll(types);
        }
        return set;
    }
}
