package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import javax.validation.constraints.NotEmpty;
import java.util.function.Function;

@Getter
@Setter
public class UserChangePasswordRequestDto {

    @Schema(description = "明文旧密码，（明文旧密码和加密旧密码最少需要一个，优先使用明文旧密码）")
    private String oldPassword;

    @Schema(description = "加密旧密码，（明文旧密码和加密旧密码最少需要一个，优先使用明文旧密码）")
    private String  encryptedOldPassword;

    @Schema(description = "明文新密码，（明文新密码和加密新密码最少需要一个，优先使用明文新密码）")
    private String newPassword;

    @Schema(description = "加密新密码，（明文新密码和加密新密码最少需要一个，优先使用明文新密码）")
    private String encryptedNewPassword;

    public void confirmPassword(Function<String, String> decryptPassword) {
        if (StringUtils.isEmpty(oldPassword) && StringUtils.isEmpty(encryptedOldPassword)) {
            throw new BadRequestException("旧密码不能为空");
        }
        if (StringUtils.isEmpty(oldPassword) && StringUtils.isNotEmpty(encryptedOldPassword)) {
            oldPassword = decryptPassword.apply(encryptedOldPassword);
        }
        if (StringUtils.isEmpty(oldPassword)) {
            throw new BadRequestException("加密公钥不正确");
        }
        if (StringUtils.isEmpty(newPassword) && StringUtils.isEmpty(encryptedNewPassword)) {
            throw new BadRequestException("新密码不能为空");
        }
        if (StringUtils.isEmpty(newPassword) && StringUtils.isNotEmpty(encryptedNewPassword)) {
            newPassword = decryptPassword.apply(encryptedNewPassword);
        }
        if (StringUtils.isEmpty(newPassword)) {
            throw new BadRequestException("加密公钥不正确");
        }
    }
}
