package org.befun.auth.dto.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.ResourcePermissionRoleDto;
import org.befun.core.dto.ResourcePermissionUserDto;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ResourcePermissionWrapDto {

    @Schema(description = "用户列表")
    private List<ResourcePermissionUserDto> permissionUsers;

    @Schema(description = "角色列表")
    private List<ResourcePermissionRoleDto> permissionRoles;
}
