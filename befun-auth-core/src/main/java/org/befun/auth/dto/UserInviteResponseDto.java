package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class UserInviteResponseDto {

    @Schema(description = "邀请结果：true 邀请成功；false 邀请失败")
    private boolean success;

    @Schema(description = "已经存在的邮箱列表")
    private List<String> existsEmail = new ArrayList<>();

    @Schema(description = "已经存在的员工号列表")
    private List<String> existsEmployeeNo = new ArrayList<>();

    @Schema(description = "超额数量")
    private int overSize;

    @Schema(description = "总数量")
    private int limit = 0;

    public UserInviteResponseDto isFail() {
        if (overSize > 0 || CollectionUtils.isNotEmpty(existsEmail) || CollectionUtils.isNotEmpty(existsEmployeeNo)) {
            success = false;
            return this;
        }
        return null;
    }

    public static UserInviteResponseDto fail(int overSize, int limit) {
        UserInviteResponseDto dto = new UserInviteResponseDto();
        dto.setOverSize(overSize);
        dto.setExistsEmail(List.of());
        dto.setLimit(limit);
        return dto;
    }

    public static UserInviteResponseDto fail(List<String> existsEmail, int limit) {
        UserInviteResponseDto dto = new UserInviteResponseDto();
        dto.setSuccess(false);
        dto.setExistsEmail(existsEmail);
        dto.setLimit(limit);
        return dto;
    }

    public static UserInviteResponseDto success() {
        UserInviteResponseDto dto = new UserInviteResponseDto();
        dto.setSuccess(true);
        return dto;
    }

}
