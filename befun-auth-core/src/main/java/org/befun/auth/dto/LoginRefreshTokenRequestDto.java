package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.LoginRefreshTokenType;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class LoginRefreshTokenRequestDto {

    @NotEmpty
    @Schema(description = "刷新token")
    private String refreshToken;

    @Schema(description = "刷新token的类型")
    private LoginRefreshTokenType refreshTokenType = LoginRefreshTokenType.NONE;

    @Schema(description = "refreshTokenType==MFA时，需要传入验证码")
    private String code;

}
