package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class WechatWorkBindDto {
    @NotEmpty
    private String bindToken;
    @NotEmpty
    private String email;
    @NotEmpty
    @Schema(description = "bind 绑定账号，create 新增账号")
    private String type;

    public boolean typeBind(){
        return "bind".equals(type);
    }

    public boolean typeCreate(){
        return "create".equals(type);
    }
}
