package org.befun.auth.dto.auth.authcode;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@NoArgsConstructor
public class AuthCodeCache {
    private String app;
    private String source;
    private String code;
    private String status = "init"; // expire active
    private String openId;
    private Long userId;

    public AuthCodeCache(String app, String source, String code) {
        this.app = app;
        this.source = source;
        this.code = code;
    }

    public boolean isActive() {
        return "active".equals(status) && StringUtils.isNotEmpty(openId);
    }
}
