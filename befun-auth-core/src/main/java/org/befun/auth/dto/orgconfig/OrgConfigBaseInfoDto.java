package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Max;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigBaseInfoDto {

    @Schema(description = "企业logo")
    public String  logo;

    @Valid
    @Max(value = 45, message = "企业名称不能超过45个字符")
    @Schema(description = "企业domain")
    public String  domain;
    @Valid
    @Max(value = 45, message = "企业名称不能超过45个字符")
    @Schema(description = "企业地址")
    public String  address;
    @Valid
    @Max(value = 30, message = "企业联系方式不能超过30个字符")
    @Schema(description = "企业联系方式")
    public String  contact;

}
