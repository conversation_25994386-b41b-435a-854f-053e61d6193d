package org.befun.auth.dto.linker;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.toast.ToastMessageHelper;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class LinkerShenCeParamDto extends LinkerParamDto {

    private LocalDate startDate;

    private LocalDate endDate;

    @Schema(description = "分析类型路径：事件分析(events/report)")
    private String reportPath;

    @Schema(description = "分析内容: urlDecode 编码一次")
    private String requestBody;

    @Schema(hidden = true)
    private Map<String, Object> eventsReport;

    public LinkerShenCeParamDto(LocalDate startDate, LocalDate endDate, String reportPath, String requestBody) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.reportPath = reportPath;
        this.requestBody = requestBody;
    }

    public String buildParams() {
        if ("events/report".equals(reportPath)) {
            String body = URLDecoder.decode(requestBody, StandardCharsets.UTF_8);
            eventsReport = JsonHelper.toMap(body);
            if (!checkEventsReport()) {
                throw ToastMessageHelper.badRequestException(AuthToastMessage.SHEN_CE_PARAM_ERROR);
            }
            // 替换 时间
            if (startDate != null && endDate != null) {
                eventsReport.put("from_date", startDate.toString());
                eventsReport.put("to_date", endDate.toString());
            }
            requestBody = JsonHelper.toJson(eventsReport);
            return null;
        }
        throw ToastMessageHelper.badRequestException(AuthToastMessage.SHEN_CE_PARAM_ERROR);
    }

    private boolean checkEventsReport() {
        Object measures;
        return eventsReport != null
                && (measures = eventsReport.get("measures")) != null && measures instanceof List && !(((List<?>) measures).isEmpty());
    }

    public List<String> getEventsReportMeasures() {
        List<String> result = new ArrayList<>();
        Object measures = eventsReport.get("measures");
        if (measures instanceof List) {
            List<?> list = (List<?>) measures;
            list.forEach(i -> {
                if (i instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) i;
                    result.add(map.getOrDefault("name", "") + "(" + map.getOrDefault("event_name", "") + ")");
                }
            });
        }
        return result;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Measure {
        @JsonProperty("event_name")
        private String eventName;
        @JsonProperty("aggregator")
        private String aggregator;
        @JsonProperty("name")
        private String name;
    }
}
