package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.ExtendCustomerFieldFormat;
import org.befun.auth.constant.ExtendCustomerFieldType;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigExtendEventFieldDto {

    @Valid
    @NotNull
    @Schema(description = "自定义字段")
    public List<OrgConfigExtendEventFieldDto.ExtendField> extendFields = new ArrayList<>();

    @Getter
    @Setter
    public static class ExtendField {

        @Valid
        @NotNull
        @Schema(description = "预警规则id")
        public List<Long> eventRuleIds = new ArrayList<>();
        @NotEmpty
        @Schema(description = "属性（API Name）")
        private String prop;
        @NotEmpty
        @Schema(description = "描述（字段名称）")
        private String label;
        @NotNull
        @Schema(description = "字段类型：TEXT，NUMBER，DATE，SINGLE_CHOICE，MULTIPLE_CHOICE")
        private ExtendCustomerFieldType type;
        @NotNull
        @Schema(description = "字段格式：" +
                "TEXT [FORMAT_TEXT]，" +
                "NUMBER [FORMAT_INTEGER，FORMAT_DECIMAL，FORMAT_PERCENT]，" +
                "DATE [FORMAT_YEAR，FORMAT_YEAR_MONTH，FORMAT_DATE，FORMAT_DATE_TIME]，" +
                "SINGLE_CHOICE [FORMAT_CHOICE]，" +
                "MULTIPLE_CHOICE [FORMAT_CHOICE]")
        private ExtendCustomerFieldFormat format;
        @Schema(description = "保留小数位")
        private Integer decimalPlaces;
        @Valid
        @Schema(description = "字段选项：SINGLE_CHOICE，MULTIPLE_CHOICE 包含此项")
        private List<OrgConfigExtendEventFieldDto.FieldOption> options = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class FieldOption {
        @NotEmpty
        @Schema(description = "选项值")
        private String value;
        @NotEmpty
        @Schema(description = "选项描述")
        private String label;
    }
}
