package org.befun.auth.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RegisterCodeDto extends BaseDTO {
    @NotEmpty(message = "手机号不能为空")
    @Length(min = 11, max = 11, message = "手机号只能为11位")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$", message = "手机号格式有误")
    private String mobile;
}
