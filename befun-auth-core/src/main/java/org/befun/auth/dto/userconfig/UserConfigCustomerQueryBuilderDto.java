package org.befun.auth.dto.userconfig;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.QueryType;
import org.befun.auth.constant.QueryValueType;
import org.befun.auth.dto.orgconfig.OrgConfigExtendCustomerFieldDto;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserConfigCustomerQueryBuilderDto {

    @Schema(description = "查询属性描述")
    private String propertyLabel;

    @Schema(description = "查询属性名称")
    private String propertyName;

    @JsonIgnore
    @Schema(description = "查询属性字段名")
    private String propertyColumn;

    @JsonIgnore
    @Schema(description = "查询属性来源：customer stat group extend")
    private String propertySource;

    @JsonIgnore
    @Schema(description = "查询属性类型：string date datetime long arrayString")
    private String propertyType;

    @Schema(description = "是否可以批量修改此属性")
    private boolean enableBatchUpdate;

    @Schema(description = "输入类型：TEXT，ARRAY_TEXT，NUMBER，DATE，SINGLE_CHOICE，MULTIPLE_CHOICE NONE(自定义属性) ")
    private String inputType;

    @Schema(description = "查询属性可选选项")
    private List<UserConfigCustomerQueryBuilderValueOptionDto> propertyValueOptions;

    @Schema(description = "该属性支持的查询条件")
    private List<UserConfigCustomerQueryBuilderItemDto> queryItems;

    @Schema(description = "自定义属性的定义")
    private OrgConfigExtendCustomerFieldDto.ExtendField extendField;

    public UserConfigCustomerQueryBuilderDto(String propertyLabel, String propertyName, boolean enableBatchUpdate, List<UserConfigCustomerQueryBuilderItemDto> queryItems) {
        this.propertyLabel = propertyLabel;
        this.propertyName = propertyName;
        this.enableBatchUpdate = enableBatchUpdate;
        this.queryItems = queryItems;
        this.propertyValueOptions = new ArrayList<>();
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserConfigCustomerQueryBuilderItemDto {

        @Schema(description = "查询条件类型描述")
        private String queryTypeLabel;

        @Schema(description = "查询条件类型")
        private QueryType queryType;

        @Schema(description = "查询值类型")
        private QueryValueType queryValueType;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserConfigCustomerQueryBuilderValueOptionDto {

        @Schema(description = "选项值")
        private String value;

        @Schema(description = "选项描述")
        private String label;

    }

}
