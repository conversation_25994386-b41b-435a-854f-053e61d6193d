package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class BindVerifyCodeRequestDto {

    @Hidden
    private Long orgId;

    @Hidden
    private Long userId;

    @NotEmpty
    @Schema(description = "账号：手机号或者邮箱")
    private String account;

    @NotEmpty
    @Schema(description = "验证码")
    private String code;
}
