package org.befun.auth.dto;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationConfig;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
public class SimpleOrganization extends BaseDTO {
    @JsonView(ResourceViews.Detail.class)
    private Long id;
    @Schema(description = "公司名称")
    @JsonView(ResourceViews.Basic.class)
    @Size(max = 45, message = "公司名称不能超过45个字符")
    private String name;

    @Schema(description = "公司编号")
    @JsonView(ResourceViews.Basic.class)
    @Size(max = 45, message = "公司编号不能超过45个字符")
    private String code;

    @Schema(description = "公司logo")
    @JsonView(ResourceViews.Basic.class)
    private String logo;

    @Schema(description = "账号权限限制")
    @Column(name = "optional_limit")
    private String optionalLimit;

    public SimpleOrganization(Organization organization) {
        this.id = organization.getId();
        this.name = organization.getName();
        this.code = organization.getCode();
    }

    public SimpleOrganization(Organization organization, OrganizationConfig config) {
        this.id = organization.getId();
        this.name = organization.getName();
        this.code = organization.getCode();
    }
}
