package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.Organization;

@Getter
@Setter
public class OrganizationDto {

    @Schema(description = "企业名称")
    private String name;

    @Schema(description = "企业编号")
    private String code;

    public static OrganizationDto mapFromEntity(Organization org) {
        OrganizationDto dto = new OrganizationDto();
        if (org != null) {
            dto.setName(org.getName());
            dto.setCode(org.getCode());
        }
        return dto;
    }
}
