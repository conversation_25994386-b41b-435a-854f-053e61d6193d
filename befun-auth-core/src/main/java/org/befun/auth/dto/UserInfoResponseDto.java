package org.befun.auth.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.User;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class UserInfoResponseDto {

    private String avatar;
    private String username;
    private String truename;
    private String mobile;
    private String email;
    private String departmentName;
    private String roleNames;
    private String availableDateStart;
    private String availableDateEnd;
    private Integer isAdmin;
    private Integer passwordStrength;
    private Integer walletSms;
    private Integer walletMoney;
    private Integer walletAiPoint;
    private String cemVersion;
    private int maxMembers;
    private SimpleOrganization organization = new SimpleOrganization();

}
