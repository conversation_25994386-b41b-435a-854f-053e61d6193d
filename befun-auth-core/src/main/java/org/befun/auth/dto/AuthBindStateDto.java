package org.befun.auth.dto;

import lombok.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AuthBindStateDto {
    private final String SPLITTER = "__";

    private String scene;
    private String app;
    private String source;
    private Long userId;

    /**
     * parse event: format scene:parameter
     * @param event
     */
    public AuthBindStateDto(String event) {
        String[] keys = event.split(SPLITTER);
        if (keys.length == 4) {
            this.scene = keys[0];
            this.app = keys[1];
            this.source = keys[2];
            this.userId = Long.valueOf(keys[3]);
        }
    }

    @Override
    public String toString() {
        return String.format("%s__%s__%s__%s", scene, app, source, userId);
    }
}
