package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Max;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigClientInfoDto {

    @Schema(description = "答题地址")
    public String  surveyPrefixUrl;

}
