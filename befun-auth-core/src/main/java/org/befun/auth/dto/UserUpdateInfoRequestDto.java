package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class UserUpdateInfoRequestDto {


    @NotEmpty
    @Schema(description = "姓名")
    private String truename;

    @Schema(description = "员工号")
    private String employeeNo;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "层级id列表")
    @NotNull
    @Size(min = 1)
    private List<@NotNull Long> departmentIds;

    @Schema(description = "角色id列表")
    @NotNull
    @Size(min = 1)
    private List<@NotNull Long> roleIds;

}
