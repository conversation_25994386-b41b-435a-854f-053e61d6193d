package org.befun.auth.dto.userconfig;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.QueryType;
import org.befun.auth.constant.QueryValueType;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class UserConfigCustomerQueryItemDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "查询属性名称")
    private String propertyName;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "查询条件类型")
    private QueryType queryType;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "查询值类型")
    private QueryValueType queryValueType;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "查询值")
    private String queryValue;


}
