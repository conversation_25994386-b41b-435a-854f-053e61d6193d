package org.befun.auth.dto;

import org.befun.auth.constant.UserStatus;

import java.util.List;
import java.util.stream.Collectors;

public interface IUserInviteInfo {

    default String getTruename() {
        return null;
    }

    default String getEmployeeNo() {
        return null;
    }

    default String getPassword() {
        return null;
    }

    default int userStatus() {
        return UserStatus.ENABLE.getStatus();
    }

    String getEmail();

    String getApp();

    static IUserInviteInfo ofEmail(String email, String app) {
        return new IUserInviteInfo() {
            @Override
            public String getEmail() {
                return email;
            }

            @Override
            public String getApp() {
                return app;
            }

            @Override
            public int userStatus() {
                return UserStatus.INVITE.getStatus();
            }
        };
    }

    static List<IUserInviteInfo> ofEmails(List<String> emails, String app) {
        return emails.stream().map(email -> ofEmail(email, app)).collect(Collectors.toList());
    }
}
