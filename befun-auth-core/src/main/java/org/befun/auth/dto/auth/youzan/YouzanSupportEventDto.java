package org.befun.auth.dto.auth.youzan;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class YouzanSupportEventDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "事件类型")
    private String event;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "事件名称")
    private String name;

}
