package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
public class UserInviteRequestDto {

    @NotEmpty
    @Schema(description = "应用类型：cem, surveyplus")
    private String app = "cem";

    @Schema(description = "部门id列表")
    private List<Long> departmentIds;

    @Schema(description = "角色id列表")
    private List<Long> roleIds;

    @NotEmpty
    @Schema(description = "邮箱")
    private List<@Email @NotEmpty String> emails;

}
