package org.befun.auth.dto.linker;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class LinkerShenCeResponseDataDto {

    private String linkerType;

    private List<Map<String, String>> columns = new ArrayList<>();

    private List<Map<String, Object>> rows = new ArrayList<>();

    public LinkerShenCeResponseDataDto(String linkerType) {
        this.linkerType = linkerType;
    }
}
