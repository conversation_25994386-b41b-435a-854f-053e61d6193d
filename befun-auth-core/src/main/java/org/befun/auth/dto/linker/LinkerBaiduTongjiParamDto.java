package org.befun.auth.dto.linker;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
public class LinkerBaiduTongjiParamDto extends LinkerParamDto {

    private LocalDate startDate;
    private LocalDate endDate;
    private String method;
    private String metrics;
    private String gran;
    private String visitor;

    public LinkerBaiduTongjiParamDto(LocalDate startDate, LocalDate endDate, String method, String metrics, String gran, String visitor) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.method = method;
        this.metrics = metrics;
        this.gran = gran;
        this.visitor = visitor;
    }

    public String buildParams() {
        return "&method=" + method +
                "&metrics=" + metrics +
                "&gran=" + gran +
                "&visitor=" + visitor +
                "&start_date=" + getStartDate() +
                "&end_date=" + getEndDate();
    }
}
