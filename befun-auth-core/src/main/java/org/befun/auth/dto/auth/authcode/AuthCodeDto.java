package org.befun.auth.dto.auth.authcode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AuthCodeDto {
    @Schema(description = "应用类型：cem | surveyplus")
    private String app;
    @Schema(description = "来源：wechat_mp")
    private String source;
    @Schema(description = "授权码")
    private String code;
    @Schema(description = "二维码")
    private String qrCode;
    @Schema(description = "有效时长：秒")
    private int expire;

    public AuthCodeDto(String app, String source, String code, String qrCode, int expire) {
        this.app = app;
        this.source = source;
        this.code = code;
        this.qrCode = qrCode;
        this.expire = expire;
    }
}
