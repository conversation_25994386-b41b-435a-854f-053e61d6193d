package org.befun.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.constant.UserStatus;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.validation.ValidSearchText;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
public class UserQueryDto extends ResourceCustomQueryDto {

    @JsonProperty("_q")
    @Schema(description = "搜索关键字")
    @ValidSearchText
    private String q;

    @Schema(description = "状态(多个逗号分隔): 1 启用 2 禁用 3 邀请待激活")
    private String status;

    @Schema(description = "部门id(多个逗号分隔): 111,222")
    private String departmentIds;

    @Schema(description = "角色id(多个逗号分隔): 888,999")
    private String roleIds;

    @Schema(description = "是否过滤本部门以及子部门")
    private Boolean withSubDepartment = false;

    public Set<UserStatus> parseStatus() {
        List<UserStatus> statuses = UserStatus.parseStatues(status);
        if (statuses == null || statuses.isEmpty()) {
            return null;
        }
        Set<UserStatus> statusSet = new HashSet<>();
        statuses.forEach(i -> {
            if (i.getStatus() == 1) {
                statusSet.add(UserStatus.INIT);
            }
            statusSet.add(i);
        });
        return statusSet;
    }

    public Set<Long> parseRoleIds() {
        if (StringUtils.isEmpty(roleIds)) {
            return null;
        }
        return Arrays.stream(roleIds.split(",")).filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toSet());
    }

    public Set<Long> parseDepartmentIds() {
        if (StringUtils.isEmpty(departmentIds)) {
            return new HashSet<>();
        }
        return Arrays.stream(departmentIds.split(",")).filter(NumberUtils::isDigits).map(Long::valueOf).collect(Collectors.toSet());
    }

}
