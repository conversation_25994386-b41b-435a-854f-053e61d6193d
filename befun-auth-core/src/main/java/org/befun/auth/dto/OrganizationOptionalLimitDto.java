package org.befun.auth.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.AppVersion;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationOptionalLimitDto {

    @JsonAlias("child_user_limit")
    @JsonProperty("child_user_limit")
    @Schema(description = "子账户数目")
    private Integer childUserLimit;

    @JsonAlias("customer_lifecycle_limit")
    @JsonProperty("customer_lifecycle_limit")
    @Schema(description = "客户旅程数")
    private Integer customerLifecycleLimit;

    @JsonAlias("surveys_limit")
    @JsonProperty("surveys_limit")
    @Schema(description = "问卷数")
    private Integer surveysLimit;

    @JsonAlias("event_rules_limit")
    @JsonProperty("event_rules_limit")
    @Schema(description = "事件预警数")
    private Integer eventRulesLimit;

    @JsonAlias("customer_person_limit")
    @JsonProperty("customer_person_limit")
    @Schema(description = "客户画像数")
    private Integer customerPersonLimit;

    @JsonAlias("bi_dashboard_limit")
    @JsonProperty("bi_dashboard_limit")
    @Schema(description = "BI仪表盘数")
    private Integer biDashboardLimit;

    @JsonAlias("logo_change_limit")
    @JsonProperty("logo_change_limit")
    @Schema(description = "是否允许修改logo")
    private Boolean  logoChangeLimit;

    @JsonAlias("bi_data_field_edit")
    @JsonProperty("bi_data_field_edit")
    @Schema(description = "BI字段/指标配置")
    private Boolean  biDataFieldEdit;
    
    @JsonAlias("sys_manage_api_edit")
    @JsonProperty("sys_manage_api_edit")
    @Schema(description = "管理后台API基础配置")
    private Boolean  sysManageApiEdit;

    public void defaultIfNull(Supplier<AppVersion> appVersion) {
        if (childUserLimit == null
                || customerLifecycleLimit == null
                || surveysLimit == null
                || eventRulesLimit == null
                || customerPersonLimit == null
                || biDashboardLimit == null
                || logoChangeLimit == null
                || biDataFieldEdit == null
                || sysManageApiEdit == null
        ) {
            OrganizationOptionalLimitDto defaultDto = appVersion.get().getOptionalLimit();
            defaultIfNull(childUserLimit, defaultDto.childUserLimit, this::setChildUserLimit);
            defaultIfNull(customerLifecycleLimit, defaultDto.customerLifecycleLimit, this::setCustomerLifecycleLimit);
            defaultIfNull(surveysLimit, defaultDto.surveysLimit, this::setSurveysLimit);
            defaultIfNull(eventRulesLimit, defaultDto.eventRulesLimit, this::setEventRulesLimit);
            defaultIfNull(customerPersonLimit, defaultDto.customerPersonLimit, this::setCustomerPersonLimit);
            defaultIfNull(biDashboardLimit, defaultDto.biDashboardLimit, this::setBiDashboardLimit);
            defaultIfNull(logoChangeLimit, defaultDto.logoChangeLimit, this::setLogoChangeLimit);
            defaultIfNull(biDataFieldEdit, defaultDto.biDataFieldEdit, this::setBiDataFieldEdit);
            defaultIfNull(sysManageApiEdit, defaultDto.sysManageApiEdit, this::setSysManageApiEdit);
        }
    }

    private void defaultIfNull(Integer value, Integer defaultValue, Consumer<Integer> setDefault) {
        if (value == null) {
            setDefault.accept(defaultValue);
        }
    }

    private void defaultIfNull(Boolean value, Boolean defaultValue, Consumer<Boolean> setDefault) {
        if (value == null) {
            setDefault.accept(defaultValue);
        }
    }

}
