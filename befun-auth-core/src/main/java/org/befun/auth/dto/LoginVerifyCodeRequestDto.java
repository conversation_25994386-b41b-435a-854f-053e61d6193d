package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.LoginPlatform;
import org.befun.extension.dto.GraphCaptchaVerifyDto;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class LoginVerifyCodeRequestDto {

    @Schema(description = "v1.9.9新增，授权码，用来在登录的时候绑定第三方账号")
    private String authCode;

    @NotEmpty
    @Schema(description = "账号：手机号或者邮箱")
    private String account;

    @NotEmpty
    @Schema(description = "验证码")
    private String code;

    @Schema(description = "图形验证码校验")
    private GraphCaptchaVerifyDto captcha;

    @Hidden
    @Schema(description = "登录平台：pc/wechatWork/mobile", hidden = true)
    private LoginPlatform platform;

    public LoginPlatform getPlatform() {
        return platform == null ? LoginPlatform.pc : platform;
    }
}
