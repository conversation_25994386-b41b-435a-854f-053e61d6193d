package org.befun.auth.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.OrganizationSmsRecord;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class OrganizationSmsRecordExtDto extends BaseEntityDTO<OrganizationSmsRecord> {

    public OrganizationSmsRecordExtDto(OrganizationSmsRecord entity) {
        super(entity);
    }

    public OrganizationSmsRecordExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    private SimpleUser createUser;
}
