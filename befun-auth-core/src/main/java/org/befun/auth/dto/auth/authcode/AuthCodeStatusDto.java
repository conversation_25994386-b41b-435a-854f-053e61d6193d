package org.befun.auth.dto.auth.authcode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AuthCodeStatusDto {
    @Schema(description = "应用类型：cem surveyplus")
    private String app;
    @Schema(description = "来源：wechat_mp")
    private String source;
    @Schema(description = "授权码")
    private String code;
    @Schema(description = "状态：init, active, expire")
    private String status;
    @Schema(description = "是否有绑定用户：true 只能登录; false 可以注册，可以绑定用户")
    private boolean hasUser;

    public static AuthCodeStatusDto expire(String app, String source, String code) {
        return new AuthCodeStatusDto(app, source, code, "expire", false);
    }

    public static AuthCodeStatusDto fromCache(AuthCodeCache cache) {
        return new AuthCodeStatusDto(cache.getApp(), cache.getSource(), cache.getCode(), cache.getStatus(), cache.getUserId() != null);
    }
}
