//package org.befun.auth.dto.message;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//import lombok.Setter;
//import org.befun.auth.dto.RegisterCustomerInfoDto;
//import org.befun.core.dto.BaseDTO;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@Getter
//@Setter
//@AllArgsConstructor
//public class UserRegisterDto extends BaseDTO {
//
//    private String userName;
//    private String companyName;
//    private String trueName;
//    private String password;
//    private String email;
//    private Long orgId;
//    private List<Long> roleId;
//    private Long departmentId;
//    private String mobile;
//    private int status;
//    private String availableSystems;
//    private RegisterCustomerInfoDto customer;
//
//
//}
