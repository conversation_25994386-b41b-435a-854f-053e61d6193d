package org.befun.auth.dto.linker;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class LinkerResponseDataDto {

    private String linkerType;

    @Schema(description = "实际值")
    private Double currentValue;

    @Schema(description = "统计数据列类型：第一列为x轴，第二列开始为数据列")
    private List<String> recentLabel = new ArrayList<>();

    @Schema(description = "统计数量")
    private List<List<Object>> recentValue = new ArrayList<>();

    public LinkerResponseDataDto(String linkerType) {
        this.linkerType = linkerType;
    }

    public void addRecentValue(String label, Double value) {
        recentValue.add(Lists.newArrayList(label, value));
    }
}
