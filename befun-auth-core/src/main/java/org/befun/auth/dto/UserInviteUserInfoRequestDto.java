package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.function.Function;

@Getter
@Setter
public class UserInviteUserInfoRequestDto implements IUserInviteInfo {

    @NotEmpty
    @Schema(description = "应用类型：cem, surveyplus")
    private String app = "cem";

    @NotEmpty
    @Schema(description = "姓名")
    private String truename;

    @Schema(description = "员工号")
    private String employeeNo;

    @NotEmpty
    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "明文密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String password;

    @Schema(description = "加密密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String encryptedPassword;

    @Schema(description = "部门id列表")
    private List<Long> departmentIds;

    @Schema(description = "角色id列表")
    private List<Long> roleIds;

    @Schema(description = "是否发送邮件通知")
    private boolean notifyEmail;

    public void confirmPassword(Function<String, String> decryptPassword) {
        if (StringUtils.isEmpty(password) && StringUtils.isEmpty(encryptedPassword)) {
            throw new BadRequestException("密码不能为空");
        }
        if (StringUtils.isEmpty(password) && StringUtils.isNotEmpty(encryptedPassword)) {
            password = decryptPassword.apply(encryptedPassword);
        }
        if (StringUtils.isEmpty(password)) {
            throw new BadRequestException("加密公钥不正确");
        }
    }
}
