package org.befun.auth.dto.auth.oauth;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.http.client.methods.HttpGet;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.http.HttpMethod;

import java.net.http.HttpClient;
import java.util.HashMap;

@Setter
@Getter
public class OauthRequestDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "请求路径")
    private String url;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "请求方式: GET/POST")
    private HttpMethod method;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "header: map")
    private HashMap<String, String> headers = new HashMap<>();


    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "请求体类型: json/form-data/xml")
    private OauthReqContentType contentType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "body: String")
    private HashMap<String, Object> body = new HashMap<>();

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "响应体体类型: json/form-data/xml")
    private OauthReqContentType responseType;

}
