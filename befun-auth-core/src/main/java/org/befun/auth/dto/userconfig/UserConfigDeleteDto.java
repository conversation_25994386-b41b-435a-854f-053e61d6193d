package org.befun.auth.dto.userconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserConfigType;

import javax.validation.constraints.NotNull;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserConfigDeleteDto {

    @NotNull
    @Schema(description = "需要删除的用户配置类型")
    private UserConfigType type;

    @NotNull
    @Schema(description = "用户配置Id")
    private Long configId;


}
