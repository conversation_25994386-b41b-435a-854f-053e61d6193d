package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserInviteUrlResponseDto {

    @Schema(description = "邀请链接")
    private String url;
    @Schema(description = "复制文案")
    private String copyText;

    public UserInviteUrlResponseDto() {
    }

    public UserInviteUrlResponseDto(String url, String copyText) {
        this.url = url;
        this.copyText = copyText;
    }
}
