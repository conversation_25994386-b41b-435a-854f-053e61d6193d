package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.auth.constant.UserGuideIndoType;

import java.util.Date;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponseDto {

    private String token;
    private String cemVersion;
    private String surveyPlusVersion;
    private Long userId;
    private String truename;
    private String username;
    private String avatar;
    private String accountType = "cem";
    private Integer isAdmin;
    private Integer isOwner;
    private Map<String, Set<String>> permissions;
    @Schema(description = "CEM应用版本")
    private String cemAppVersion;
    @Schema(description = "用户指导完成的类型")
    private Set<UserGuideIndoType> guideInfo;
    @Schema(description = "账户注册时间")
    private Date createTime;
    @Schema(description = "账户过期时间")
    private Date expireDate;
    @Schema(description = "账户开始时间")
    private Date startDate;
    @Schema(description = "企业代号")
    private String orgCode;
    private Integer oldSurveyPlusUser;
    @Schema(description = "企业代号")
    private SimpleOrganization organization;

}
