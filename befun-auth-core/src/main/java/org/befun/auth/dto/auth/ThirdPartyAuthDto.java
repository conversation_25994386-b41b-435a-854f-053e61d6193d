package org.befun.auth.dto.auth;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.core.dto.CustomParamDto;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class ThirdPartyAuthDto<CONFIG> extends CustomParamDto<ThirdPartyAuth> {

    @NotEmpty
    @Schema(hidden = true, description = "默认cem")
    @JsonIgnore
    private String app = "cem";

    @NotNull
    @JsonView(ResourceViews.Basic.class)
    @Schema(required = true, description = "授权类型")
    private ThirdPartyAuthType authType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "配置项")
    private CONFIG config;

    @Schema(hidden = true)
    @JsonIgnore
    private String remark;

}
