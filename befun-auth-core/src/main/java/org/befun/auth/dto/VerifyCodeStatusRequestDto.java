package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class VerifyCodeStatusRequestDto {

    @NotEmpty
    @Schema(description = "账号： 手机号或者邮箱")
    private String account;

    @NotEmpty
    @Schema(description = "验证码")
    private String code;

    @Schema(hidden = true, description = "本次发送的唯一标识，用作发送成功时的缓存key的后缀，如果没有此参数，则使用 account")
    private String uniqueKey;

    public VerifyCodeStatusRequestDto() {
    }

    public VerifyCodeStatusRequestDto(String account, String code) {
        this.account = account;
        this.code = code;
    }

    public VerifyCodeStatusRequestDto(String account, String code, String uniqueKey) {
        this.account = account;
        this.code = code;
        this.uniqueKey = uniqueKey;
    }
}
