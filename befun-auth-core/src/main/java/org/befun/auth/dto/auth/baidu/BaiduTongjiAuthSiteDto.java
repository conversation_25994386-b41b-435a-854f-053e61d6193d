package org.befun.auth.dto.auth.baidu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaiduTongjiAuthSiteDto {

    @NotNull
    @Schema(description = "站点id", required = true)
    private Integer siteId;

    @NotEmpty
    @Schema(description = "站点域名", required = true)
    private String siteDomain;

}
