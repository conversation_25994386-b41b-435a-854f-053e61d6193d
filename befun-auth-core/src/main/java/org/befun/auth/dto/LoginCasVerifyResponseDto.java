package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LoginCasVerifyResponseDto {

    @NotEmpty
    @Schema(description = "0 成功 1 企业编号错误 2 企业未配置cas登录方式")
    private int success;

    public static LoginCasVerifyResponseDto success() {
        return new LoginCasVerifyResponseDto(0);
    }

    public static LoginCasVerifyResponseDto orgCodeError() {
        return new LoginCasVerifyResponseDto(1);
    }

    public static LoginCasVerifyResponseDto notConfigCasError() {
        return new LoginCasVerifyResponseDto(2);
    }
}
