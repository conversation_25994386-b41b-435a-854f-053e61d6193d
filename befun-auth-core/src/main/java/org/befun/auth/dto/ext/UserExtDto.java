package org.befun.auth.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.DepartmentDto;
import org.befun.auth.entity.RoleDto;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserInvitationDto;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public class UserExtDto extends BaseEntityDTO<User> {

    public UserExtDto() {
    }

    public UserExtDto(User entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "部门")
    private List<DepartmentDto> departments;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "部门名称")
    private String departmentNames;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "部门id")
    private List<Long> departmentIds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "角色")
    private List<RoleDto> roles;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "角色名称")
    private String roleNames;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "角色id")
    private List<Long> roleIds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "邀请信息")
    private UserInvitationDto invitation;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否可以编辑用户")
    private Boolean enableEditUser = false;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否可以编辑角色")
    private Boolean enableEditRole = false;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否可以选择超管角色")
    private Boolean enableSelectAdminRole = false;
}
