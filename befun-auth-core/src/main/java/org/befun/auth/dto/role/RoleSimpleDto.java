package org.befun.auth.dto.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.Role;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class RoleSimpleDto {

    @Schema(description = "id")
    private Long id;

    @NotEmpty
    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色编号")
    private String code;

    @Schema(description = "角色描述")
    private String description;

    public static RoleSimpleDto map(Role role) {
        if (role == null) {
            return null;
        }
        RoleSimpleDto dto = new RoleSimpleDto();
        dto.setId(role.getId());
        dto.setName(role.getName());
        dto.setCode(role.getCode());
        dto.setDescription(role.getDescription());
        return dto;
    }
}
