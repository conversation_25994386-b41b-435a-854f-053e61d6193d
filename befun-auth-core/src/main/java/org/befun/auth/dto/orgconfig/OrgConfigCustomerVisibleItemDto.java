package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.ExtendCustomerFieldFormat;
import org.befun.auth.constant.ExtendCustomerFieldType;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigCustomerVisibleItemDto {
    @NotEmpty
    @Schema(description = "属性")
    private String prop;
    @NotEmpty
    @Schema(description = "描述")
    private String label;
    @NotNull
    @Schema(description = "是否显示")
    private Boolean visible;
    @NotNull
    @Schema(description = "是否编辑")
    private Boolean editable;

    public boolean equalTo(OrgConfigCustomerVisibleItemDto other) {
        return Objects.equals(prop, other.prop)
                && Objects.equals(label, other.label)
                && Objects.equals(visible, other.visible)
                && Objects.equals(editable, other.editable);
    }
}
