//package org.befun.auth.dto.message;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//import lombok.Setter;
//import org.befun.auth.constant.AppVersion;
//import org.befun.auth.constant.IndustryType;
//import org.befun.auth.dto.RegisterInfoDto;
//import org.befun.auth.entity.User;
//import org.befun.core.dto.BaseDTO;
//
//import java.text.SimpleDateFormat;
//import java.time.LocalDate;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * 由于需要使用云平台做用户开启禁用，已经编辑用户的功能
// * 所以需要推送注册用户信息给api添加用户数据
// */
//@Getter
//@Setter
//@AllArgsConstructor
//public class SyncSurveyPlusUserRegisterDto extends BaseDTO {
//    private boolean init_default_surveys = true;
//    private String username = "";
//    private String email;
//    private String org_code;
//    private String org_name;
//    private String industry_name = IndustryType.OTHERS.getCode();
//    private String tel;
//    private String surveyplus_version = AppVersion.EMPTY.getText();
//    private String cem_version = AppVersion.FREE.getText();
//    private String password;
//    private Integer account_amount = 1;
//    private Integer status = 2;
//    private String start_time;
//    private String end_time;
//
//    public SyncSurveyPlusUserRegisterDto(RegisterInfoDto registerInfoDto, User user, String org_code){
//        this.username = Objects.toString(user.getId());
//        this.org_code = org_code;
//        this.org_name = registerInfoDto.getCompanyName();
//        this.tel = registerInfoDto.getMobile();
//        this.email = registerInfoDto.getEmail();
//        this.password = registerInfoDto.getPassword();
//        this.status = user.getStatus();
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Date now = new Date();
//        Calendar cal = Calendar.getInstance();
//        cal.setTime(now);
//        cal.add(Calendar.YEAR, 1);
//
//        this.start_time = sdf.format(now);
//        this.end_time = sdf.format(cal.getTime());
//    }
//}
