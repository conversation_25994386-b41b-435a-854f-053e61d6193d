package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryDto;

@Getter
@Setter
public class OperateLogQueryDto extends ResourceCustomQueryDto {

    @Schema(description = "操作人")
    private String userName;
    @Schema(description = "功能模块")
    private String module;
    @Schema(description = "操作内容")
    private String action;
    @Schema(description = "操作结果")
    private Boolean result;

}
