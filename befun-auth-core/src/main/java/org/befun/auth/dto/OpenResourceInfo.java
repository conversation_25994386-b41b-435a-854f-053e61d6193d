package org.befun.auth.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OpenResourceInfo {

    @JsonView(ResourceViews.Basic.class)
    private Long id;
    @JsonView(ResourceViews.Basic.class)
    private String url;
    @JsonView(ResourceViews.Basic.class)
    private String message;
}
