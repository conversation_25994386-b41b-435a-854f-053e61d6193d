package org.befun.auth.dto.auth;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.auth.oauth.OauthAuthDto;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Setter
@Getter
public class AllAuthDto {

    @JsonView(ResourceViews.Basic.class)
    private CasAuthDto cas;

    @JsonView(ResourceViews.Basic.class)
    private OauthAuthDto oauth;

    @JsonView(ResourceViews.Basic.class)
    private WechatWorkAuthDto wechatWork;

    @JsonView(ResourceViews.Basic.class)
    private <PERSON>du<PERSON>ongji<PERSON>uthDto baiduTongji;

    @JsonView(ResourceViews.Basic.class)
    private EmailSenderAuthDto emailSender;

    @JsonView(ResourceViews.Basic.class)
    private YouzanAuthDto youzan;

    @JsonView(ResourceViews.Basic.class)
    private List<WechatOpenAuthDto> wechatOpen;

    @JsonView(ResourceViews.Basic.class)
    private ShenCeAuthDto shenCe;

    @JsonView(ResourceViews.Basic.class)
    private SamlAuthDto saml;
}
