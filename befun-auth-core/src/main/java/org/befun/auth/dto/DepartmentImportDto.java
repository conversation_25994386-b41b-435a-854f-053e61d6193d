package org.befun.auth.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.extension.toast.ToastMessageHelper;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentImportDto {

    @ExcelProperty(index = 0, value = "部门ID")
    private String id;
    @ExcelProperty(index = 1, value = "部门编码")
    private List<String> code;
    @ExcelProperty(index = 2, value = "部门名称")
    private String name;

    @Schema(description = "0 无变化 1 待修改 2 待新增")
    @ExcelIgnore
    public int rowEdit;
    @JsonIgnore
    @ExcelIgnore
    public boolean isRoot;
    @JsonIgnore
    @ExcelIgnore
    public String rootName;
    @JsonIgnore
    @ExcelIgnore
    public String leafName;
    @JsonIgnore
    @ExcelIgnore
    public Integer level;
    @JsonIgnore
    @ExcelIgnore
    public List<String> parentNames; // 除了根节点和叶子节点之外的中间节点

    public static final Pattern PATTERN = Pattern.compile("^[\\da-zA-Z]+$");

    public boolean parseName() {
        if (StringUtils.isNotEmpty(name)) {
            List<String> arr = Arrays.stream(name.split("/")).collect(Collectors.toList());
            if (arr.size() > 5) {
                throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_LEVEL_LIMIT);
            }
            if (arr.stream().anyMatch(StringUtils::isEmpty)) {
                return false;           // 有空节点，无效数据
            }
            if (arr.stream().anyMatch(i -> i.length() >= 20)) {
                throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_NAME_TO_LONG);
            }
            if (CollectionUtils.isNotEmpty(code) && code.stream().anyMatch(c -> c.length() >= 20)) {
                throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_CODE_TO_LONG);
            }
            if (CollectionUtils.isNotEmpty(code) && code.stream().anyMatch(c -> !PATTERN.matcher(c).matches())) {
                throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_CODE_INVALID_CHAR);
            }
            isRoot = arr.size() == 1;   // 只有一个节点，则是根节点
            rootName = arr.get(0);      // 第一个节点永远是根节点
            leafName = arr.get(arr.size() - 1); // 最后一个节点永远是叶子
            if (arr.size() > 2) {
                parentNames = arr.subList(1, arr.size() - 1);
            } else {
                parentNames = List.of();
            }
            level = arr.size();
            return true;
        }
        return false;
    }

    public Long parseId() {
        if (NumberUtils.isDigits(id)) {
            long i = Long.parseLong(id);
            if (i > 0) {
                return i;
            }
        }
        return null;
    }
}
