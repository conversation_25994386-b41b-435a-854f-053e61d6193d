package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class UserDepartmentMoveRequestDto {

    @Schema(description = "userId列表")
    @NonNull
    @Size(min = 1)
    private List<@NotNull Long> userIds;

}
