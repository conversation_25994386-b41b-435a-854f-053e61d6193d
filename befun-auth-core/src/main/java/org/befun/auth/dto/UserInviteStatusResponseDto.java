package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class UserInviteStatusResponseDto {

    @Schema(description = "1 邀请链接验证 2 绑定链接验证")
    private int type;

    @Schema(description = "type=2时由此参数，临时的登录token，1分钟内有效，只能使用1次")
    private String refreshToken;

    @Schema(description = "type=1时由此参数，邮箱")
    private String email;
    @Schema(description = "type=1时由此参数，企业名称")
    private String orgName;

    public static UserInviteStatusResponseDto successInvite(String email, String orgName) {
        UserInviteStatusResponseDto dto = new UserInviteStatusResponseDto();
        dto.setType(1);
        dto.setEmail(email);
        dto.setOrgName(orgName);
        return dto;
    }

    public static UserInviteStatusResponseDto successBind(String refreshToken) {
        UserInviteStatusResponseDto dto = new UserInviteStatusResponseDto();
        dto.setType(2);
        dto.setRefreshToken(refreshToken);
        return dto;
    }

}
