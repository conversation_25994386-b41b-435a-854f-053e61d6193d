package org.befun.auth.dto.userconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserConfigType;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserConfigSaveDto {

    @NotNull
    @Schema(description = "需要保存的用户配置类型")
    private UserConfigType type;

    @Valid
    @NotNull
    @Schema(description = "用户配置内容")
    private UserConfigDto config;


}
