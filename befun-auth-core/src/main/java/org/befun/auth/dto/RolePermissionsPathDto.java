package org.befun.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.Permission;
import org.befun.auth.entity.Role;
import org.befun.core.dto.BaseDTO;

import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
public class RolePermissionsPathDto extends BaseDTO {
    private Role role;
    private List<String> permissionsPath;
}