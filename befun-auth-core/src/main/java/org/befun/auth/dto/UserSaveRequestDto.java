package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserSaveRequestDto {

    @Schema(description = "用户id，新增时无参数；修改时必须")
    private Long id;

    @NotEmpty
    @Schema(description = "用户名", required = true)
    private String username;

    @Schema(description = "姓名")
    private String truename;

    @Schema(description = "密码，新增时，必须；修改时，不为空的时候会更新，为空会忽略")
    private String password;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

    @NotNull
    @Schema(description = "部门id", required = true)
    private Long departmentId;

    @NotEmpty
    @Schema(description = "角色id列表,多个,分割", required = true)
    private String roleIds;

}
