package org.befun.auth.dto.userconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

@Getter
@Setter
public class UserConfigCustomerQueryDto {

    @Schema(description = "配置id，删除配置时，需要此id，新增时忽略")
    private Long configId;

    @NotEmpty
    @Schema(description = "名称")
    private String name;

    @NotEmpty
    @Pattern(regexp = "^and|or$")
    @Schema(description = "逻辑关系：and | or")
    private String logic = "and";

    @NotEmpty
    @Schema(description = "条件")
    private List<UserConfigCustomerQueryItemDto> items;

}
