package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import javax.validation.constraints.NotEmpty;
import java.util.function.Function;

@Getter
@Setter
public class UserInviteActiveRequestDto {

    @NotEmpty
    @Schema(description = "邀请码")
    private String code;

    @NotEmpty
    @Schema(description = "姓名")
    private String truename;

    @Schema(description = "明文密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String password;

    @Schema(description = "加密密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String encryptedPassword;

    public void confirmPassword(Function<String, String> decryptPassword) {
        if (StringUtils.isEmpty(password) && StringUtils.isEmpty(encryptedPassword)) {
            throw new BadRequestException("密码不能为空");
        }
        if (StringUtils.isEmpty(password) && StringUtils.isNotEmpty(encryptedPassword)) {
            password = decryptPassword.apply(encryptedPassword);
        }
        if (StringUtils.isEmpty(password)) {
            throw new BadRequestException("加密公钥不正确");
        }
        int length = password.length();
        if (length < 6 || length > 14) {
            throw new BadRequestException("密码长度在6-14个字符");
        }
    }
}
