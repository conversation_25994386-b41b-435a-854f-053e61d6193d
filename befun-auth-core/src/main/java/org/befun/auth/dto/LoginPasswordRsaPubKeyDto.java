package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LoginPasswordRsaPubKeyDto {

    @NotEmpty
    @Schema(description = "公钥")
    private String rsaPublicKey;

    @Schema(description = "mode padding")
    private String rsaModePadding;

}
