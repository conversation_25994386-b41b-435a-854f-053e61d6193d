package org.befun.auth.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.role.MenuTreeDto;
import org.befun.auth.dto.role.RoleSimpleDto;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class RoleRequestDto {

    @Valid
    @NotNull
    private RoleSimpleDto role;

    @Valid
    private List<MenuTreeDto> menus;

}
