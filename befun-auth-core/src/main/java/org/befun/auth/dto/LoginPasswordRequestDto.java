package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.LoginPlatform;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.dto.GraphCaptchaVerifyDto;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.function.Function;

@Getter
@Setter
public class LoginPasswordRequestDto {

    @NotEmpty
    @Schema(description = "账号：手机号或者邮箱")
    private String username;

    @Schema(description = "明文密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String password;

    @Schema(description = "加密密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String encryptedPassword;

    @Valid
    @Schema(description = "图形验证码校验")
    private GraphCaptchaVerifyDto captcha;

    @Hidden
    @Schema(description = "登录平台：pc/wechatWork/mobile", hidden = true)
    private LoginPlatform platform;

    public LoginPlatform getPlatform() {
        return platform == null ? LoginPlatform.pc : platform;
    }

    public void confirmPassword(Function<String, String> decryptPassword) {
        if (StringUtils.isEmpty(password) && StringUtils.isEmpty(encryptedPassword)) {
            throw new BadRequestException("密码不能为空");
        }
        if (StringUtils.isEmpty(password) && StringUtils.isNotEmpty(encryptedPassword)) {
            password = decryptPassword.apply(encryptedPassword);
        }
        if (StringUtils.isEmpty(password)) {
            throw new BadRequestException("加密公钥不正确");
        }
    }

}
