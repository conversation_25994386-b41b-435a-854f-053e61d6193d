package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class UserInviteNotifyRequestDto {

    @NotEmpty
    @Schema(description = "应用类型：cem, surveyplus")
    private String app = "cem";

    @NotNull
    @Schema(description = "邀请id")
    private Long inviteId;

}
