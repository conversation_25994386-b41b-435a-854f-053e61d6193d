package org.befun.auth.dto.ext;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.Role;
import org.befun.core.dto.BaseEntityDTO;

@Getter
@Setter
public class RoleExtDto extends BaseEntityDTO<Role> {

    public RoleExtDto() {
    }

    public RoleExtDto(Role entity) {
        super(entity);
    }

    private int countUser;

    @JsonIgnore
    @Schema(hidden = true, description = "用来查询和用户关系的字段，其他地方用不到")
    private Long userId;
}
