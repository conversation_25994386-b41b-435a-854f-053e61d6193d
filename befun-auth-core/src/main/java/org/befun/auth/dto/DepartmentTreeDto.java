package org.befun.auth.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.entity.Department;
import org.befun.auth.projection.SimpleDepartment;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class DepartmentTreeDto implements TreeDto<DepartmentTreeDto> {

    private Long id;
    private String title;
    private List<String> code;
    private Long pid;
    private Integer level;
    private List<String> equivalentCode = new ArrayList<>();

    // 有的时候需要从子级直接定位到父级，这里加上一个父级的引用
    @JsonIgnore
    private DepartmentTreeDto parent;

    public DepartmentTreeDto(String title) {
        this.title = title;
    }

    public DepartmentTreeDto(Long id, String title, List<String> code) {
        this.id = id;
        this.title = title;
        this.code = code;
    }

    private List<DepartmentTreeDto> subDepartments = new ArrayList<>();

    public static DepartmentTreeDto mapFrom(SimpleDepartment department) {
        if (department == null) {
            return null;
        }
        DepartmentTreeDto tree = new DepartmentTreeDto();
        tree.setId(department.getId());
        tree.setTitle(department.getTitle());
        tree.setCode(department.getCode());
        tree.setPid(department.getPid() == null ? 0 : department.getPid());
        tree.setSubDepartments(new ArrayList<>());
        tree.setEquivalentCode(department.getEquivalentCode() == null ? new ArrayList<>() : department.getEquivalentCode());
        return tree;
    }

    public static DepartmentTreeDto mapFrom(Department department) {
        if (department == null) {
            return null;
        }
        DepartmentTreeDto tree = new DepartmentTreeDto();
        tree.setId(department.getId());
        tree.setTitle(department.getTitle());
        tree.setCode(department.getCode());
        tree.setPid(department.getPid() == null ? 0 : department.getPid());
        tree.setSubDepartments(new ArrayList<>());
        tree.setEquivalentCode(department.getEquivalentCode() == null ? new ArrayList<>() : department.getEquivalentCode());
        return tree;
    }

    @Override
    public List<DepartmentTreeDto> children() {
        return subDepartments;
    }

    @Override
    public void addChild(DepartmentTreeDto departmentTreeDto) {
        subDepartments.add(departmentTreeDto);
    }

}
