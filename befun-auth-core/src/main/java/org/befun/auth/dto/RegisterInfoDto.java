package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.constant.AppVersion;
import org.befun.core.dto.BaseDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.RegHelper;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterInfoDto extends BaseDTO {

    @Schema(description = "v1.9.9新增，授权码，用来在注册的时候绑定第三方账号")
    private String authCode;
    @NotNull
    @Schema(description = "应用类型")
    private AppType app;
    @NotEmpty(message = "手机号不能为空")
    @Length(min = 11, max = 11, message = "手机号只能为11位")
    private String mobile;
    //    @Length(max = 15, min = 6, message = "密码长度在6-14之间")
//    private String password;
    @Schema(description = "明文密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String password;
    @Schema(description = "加密密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String encryptedPassword;
    @NotEmpty(message = "验证码不能为空")
    private String verifyCode;
    //    @NotEmpty(message = "姓名不能为空")
//    @Length(max = 15, message = "姓名不能超过15位")
    private String name;
    //    @Length(max = 15, message = "公司名不能超过15位")
//    @NotEmpty(message = "公司名不能为空")
    private String companyName;
    //    @NotEmpty(message = "邮件不能为空")
//    @Email(message = "邮件格式错误")
    private String email;
    private RegisterCustomerInfoDto customer;
    private AppVersion appVersion;
    private Date availableDateEnd;

    public void clearAppVersion() {
        appVersion = null;
        availableDateEnd = null;
    }

    public void checkParams() {
        if (app == AppType.cem) {
            if (StringUtils.isEmpty(name)) {
                throw new BadRequestException("姓名不能为空");
            } else if (name.length() > 15) {
                throw new BadRequestException("姓名不能超过15位");
            }
            if (StringUtils.isEmpty(companyName)) {
                throw new BadRequestException("公司名不能为空");
            } else if (companyName.length() > 15) {
                throw new BadRequestException("公司名不能超过15位");
            }
            if (StringUtils.isEmpty(email)) {
                throw new BadRequestException("邮件不能为空");
            } else if (!RegHelper.isEmail(email)) {
                throw new BadRequestException("邮件格式错误");
            }
        }

    }

    public void confirmPassword(Function<String, String> decryptPassword) {
        if (StringUtils.isEmpty(password) && StringUtils.isEmpty(encryptedPassword)) {
            throw new BadRequestException("密码长度在6-14之间");
        }
        if (StringUtils.isEmpty(password) && StringUtils.isNotEmpty(encryptedPassword)) {
            password = decryptPassword.apply(encryptedPassword);
        }
        if (StringUtils.isEmpty(password)) {
            throw new BadRequestException("加密公钥不正确");
        }
        int i = password.length();
        if (i < 6 || i > 14) {
            throw new BadRequestException("密码长度在6-14之间");
        }
    }

    public RegisterCustomerInfoDto getCustomer() {
        if (customer == null) {
            customer = new RegisterCustomerInfoDto();
        }

        if (Objects.isNull(customer.getCompany())) {
            customer.setCompany(companyName);
        }
        if (Objects.isNull(customer.getName())) {
            customer.setName(name);
        }
        if (Objects.isNull(customer.getEmail())) {
            customer.setEmail(email);
        }
        if (Objects.isNull(customer.getTelephone())) {
            customer.setTelephone(mobile);
        }

        return customer;
    }
}
