package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.function.Function;

@Getter
@Setter
public class ResetPasswordRequestDto {

    @NotEmpty
    @Schema(description = "账号：手机号或者邮箱")
    private String account;

    @NotEmpty
    @Schema(description = "验证码")
    private String code;

    @Schema(description = "明文密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String newPassword;

    @Schema(description = "加密密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String encryptedNewPassword;

    public void confirmPassword(Function<String, String> decryptPassword) {
        if (StringUtils.isEmpty(newPassword) && StringUtils.isEmpty(encryptedNewPassword)) {
            throw new BadRequestException("密码不能为空");
        }
        if (StringUtils.isEmpty(newPassword) && StringUtils.isNotEmpty(encryptedNewPassword)) {
            newPassword = decryptPassword.apply(encryptedNewPassword);
        }
        if (StringUtils.isEmpty(newPassword)) {
            throw new BadRequestException("加密公钥不正确");
        }
    }
}
