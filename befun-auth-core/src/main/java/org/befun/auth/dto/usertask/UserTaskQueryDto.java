package org.befun.auth.dto.usertask;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryDto;

@Getter
@Setter
public class UserTaskQueryDto extends ResourceCustomQueryDto {

    @Schema(description = "任务类型(多个逗号分隔)，可用值：responseDownload,syncWechatOpenSyncCustomer,syncSurveyQuota")
    private String type_in;
    @Schema(description = "任务状态(多个逗号分隔)，可用值：INIT,RUNNING,SUCCESS,FAILED,CANCELED")
    private String status_in;

}
