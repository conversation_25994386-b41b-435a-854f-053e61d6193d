package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigCustomerVisibleBuilderDto {


    @Valid
    @NotNull
    @Schema(description = "已选择的字段")
    public List<OrgConfigCustomerVisibleItemDto> selectedColumns;

    @Schema(description = "固定的字段")
    public List<OrgConfigCustomerVisibleItemDto> fixedColumns;

    @Schema(description = "可编辑的字段")
    public List<OrgConfigCustomerVisibleItemDto> editableColumns;

    @Schema(description = "自定义的字段")
    public List<OrgConfigCustomerVisibleItemDto> extendColumns;

}
