package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LoginPasswordMfaDto {

    @Schema(description = "验证类型：mobile, email")
    private String mfaVerifyType;
    @Schema(description = "邮箱(已脱敏)")
    private String email;
    @Schema(description = "手机号已脱敏)")
    private String mobile;
    @Schema(description = "token: 发送验证码和确定登录时的凭证")
    private String mfaToken;
}
