package org.befun.auth.dto.usertask;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@NoArgsConstructor
public class UserTaskResponseDownloadDto {

    @JsonView(ResourceViews.Basic.class)
    private Long surveyId;
    @JsonView(ResourceViews.Basic.class)
    private String fileUrl;
    @JsonView(ResourceViews.Basic.class)
    private String fileName;
    @JsonView(ResourceViews.Basic.class)
    private Long fileSize;

    public UserTaskResponseDownloadDto(Long surveyId) {
        this.surveyId = surveyId;
    }


    public Long getFileSize() {
        return fileSize == null ? 0 : fileSize;
    }
}
