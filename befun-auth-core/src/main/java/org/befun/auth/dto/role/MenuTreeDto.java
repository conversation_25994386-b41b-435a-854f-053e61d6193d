package org.befun.auth.dto.role;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.TreeDto;
import org.befun.auth.entity.Menu;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class MenuTreeDto implements TreeDto<MenuTreeDto> {

    @Schema(description = "id")
    @JsonView(ResourceViews.Basic.class)
    private Long id;

    @Schema(description = "父菜单id")
    @JsonView(ResourceViews.Basic.class)
    private Long pid;

    @Schema(description = "菜单名称")
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @Schema(description = "菜单路径")
    @JsonView(ResourceViews.Basic.class)
    private String path;

    @Schema(description = "菜单全路径")
    @JsonView(ResourceViews.Basic.class)
    private String fullPath;

    @Schema(description = "是否选择")
    @JsonView(ResourceViews.Basic.class)
    private boolean selected;

    @Schema(description = "子菜单")
    @JsonView(ResourceViews.Basic.class)
    private List<MenuTreeDto> subMenus = new ArrayList<>();

    @Override
    public List<MenuTreeDto> children() {
        return subMenus;
    }

    @Override
    public void addChild(MenuTreeDto menuTreeDto) {
        subMenus.add(menuTreeDto);
    }

    public static MenuTreeDto formMenu(Menu menu) {
        return formMenu(menu, false);
    }

    public static MenuTreeDto formMenu(Menu menu, boolean selected) {
        if (menu == null) {
            return null;
        }
        MenuTreeDto tree = new MenuTreeDto();
        tree.setId(menu.getId());
        tree.setPid(menu.getPid());
        tree.setName(menu.getName());
        tree.setPath(menu.getPath());
        tree.setFullPath(menu.getFullPath());
        tree.setSelected(selected);
        return tree;
    }
}
