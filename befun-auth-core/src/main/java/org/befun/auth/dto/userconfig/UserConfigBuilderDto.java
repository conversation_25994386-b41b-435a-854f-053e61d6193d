package org.befun.auth.dto.userconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.List;

/**
 * 每次增加用户配置项时，增加一个字段，字段名称和枚举同名，字段类型和枚举定义的类型一致
 */
@Slf4j
@Getter
@Setter
public class UserConfigBuilderDto {

    @Valid
    @Schema(description = "用户配置-客户查询")
    private List<UserConfigCustomerQueryBuilderDto> customerQuery;

}
