package org.befun.auth.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AuthWhiteListDto {

    @Schema(description = "默认cem")
    private String app = "cem";

    @Schema(description = "白名单编号")
    private List<String> whiteCode;
}
