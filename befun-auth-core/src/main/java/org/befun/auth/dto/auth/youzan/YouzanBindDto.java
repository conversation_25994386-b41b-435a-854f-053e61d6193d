package org.befun.auth.dto.auth.youzan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class YouzanBindDto {

    @NotEmpty
    @Schema(required = true, description = "点击去使用时，回调地址上的token")
    private String newUserToken;

    @JsonIgnore
    @Schema(hidden = true)
    private String kdtId;

    @JsonIgnore
    @Schema(hidden = true)
    private String shopName;

}
