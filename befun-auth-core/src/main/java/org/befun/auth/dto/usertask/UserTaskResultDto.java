package org.befun.auth.dto.usertask;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class UserTaskResultDto {

    @JsonView(ResourceViews.Basic.class)
    private List<String> errorMessages;
    @JsonView(ResourceViews.Basic.class)
    private UserTaskResponseDownloadDto responseDownload;

}
