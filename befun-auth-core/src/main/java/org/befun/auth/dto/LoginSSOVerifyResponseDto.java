package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LoginSSOVerifyResponseDto {

    @NotEmpty
    @Schema(description = "0 成功 1 企业编号错误 2 企业未配置cas登录方式")
    private int success;

    public static LoginSSOVerifyResponseDto success() {
        return new LoginSSOVerifyResponseDto(0);
    }

    public static LoginSSOVerifyResponseDto orgCodeError() {
        return new LoginSSOVerifyResponseDto(1);
    }

    public static LoginSSOVerifyResponseDto notConfigError() {
        return new LoginSSOVerifyResponseDto(2);
    }
}
