package org.befun.auth.dto.usertask;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.UserTaskType;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskTypeBelong;
import org.befun.task.dto.TaskProgressDto;
import org.befun.task.entity.TaskProgress;

@Getter
@Setter
@NoArgsConstructor
public class UserTaskDto extends TaskProgressDto {

    @Schema(description = "任务类型")
    private UserTaskType type;

    @Schema(description = "任务所属")
    private TaskTypeBelong typeBelong;

    @Schema(description = "任务结果")
    private UserTaskResultDto result;

    public UserTaskDto(TaskProgress taskProgress) {
        this.setStatus(taskProgress.getStatus());
        this.setTotalSize(taskProgress.getTotalSize());
        this.setSuccessSize(taskProgress.getSuccessSize());
        this.setFailedSize(taskProgress.getFailedSize());
        this.setType(EnumHelper.parse(UserTaskType.values(), taskProgress.getType()));
        this.setTypeBelong(taskProgress.getTypeBelong());
        this.setResult(JsonHelper.toObject(taskProgress.getResult(), UserTaskResultDto.class));
    }
}
