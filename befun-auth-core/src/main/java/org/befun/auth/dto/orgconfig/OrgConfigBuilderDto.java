package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

/**
 * 每次增加企业配置项时，增加一个字段，字段名称和枚举同名，字段类型和枚举定义的类型一致
 */
@Slf4j
@Getter
@Setter
public class OrgConfigBuilderDto {

    @Valid
    @Schema(description = "企业配置-自定义显示客户列表")
    private OrgConfigCustomerVisibleBuilderDto customerVisible;

    @Valid
    @Schema(description = "企业配置-问卷审核")
    private Boolean surveyVerify;

    @Valid
    @Schema(description = "企业配置-发送管理审核")
    private Boolean sendManageAudit;

}
