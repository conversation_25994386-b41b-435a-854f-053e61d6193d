package org.befun.auth.dto.linker;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.temporal.ChronoField;

@Getter
@Setter
public abstract class LinkerParamDto {


    public abstract String buildParams();

    protected String formatDate(LocalDate localDate) {
        int year = localDate.get(ChronoField.YEAR);
        int month = localDate.get(ChronoField.MONTH_OF_YEAR);
        int day = localDate.get(ChronoField.DAY_OF_MONTH);
        return year + (month < 10 ? "0" : "") + month + (day < 10 ? "0" : "") + day;
    }
}
