package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigCustomerVisibleDto {

    @Valid
    @NotNull
    @Schema(description = "已选择的字段")
    public List<OrgConfigCustomerVisibleItemDto> selectedColumns;


}
