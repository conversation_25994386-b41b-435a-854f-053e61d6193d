package org.befun.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.OwnerResourceType;

import java.util.List;

@Getter
@Setter
public class ChangeOwnerResourcesDto {

    @Schema(description = "源资源拥有者id")
    private List<Long> fromUserIds;
    @Schema(description = "目标资源拥有者id")
    private Long targetUserId;
    @Schema(description = "资源类型")
    private List<OwnerResourceType> types;

}
