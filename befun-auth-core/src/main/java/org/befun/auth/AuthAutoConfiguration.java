package org.befun.auth;

import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.PermissionChangeProperties;
import org.befun.auth.configuration.UserConfigProperties;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.auth.workertrigger.IAuthTaskTrigger;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan({"org.befun.auth.service", "org.befun.auth.utils", "org.befun.auth.provider", "org.befun.auth.workertrigger", "org.befun.auth.Aspects"})
@EnableConfigurationProperties({AuthProperties.class, UserConfigProperties.class, PermissionChangeProperties.class})
public class AuthAutoConfiguration {

    public static final String PACKAGE_ENTITY = "org.befun.auth.entity";
    public static final String PACKAGE_REPOSITORY = "org.befun.auth.repository";

    @Bean
    @ConditionalOnMissingBean(IAuthEventTrigger.class)
    public IAuthEventTrigger authEventTrigger() {
        return new IAuthEventTrigger() {
        };
    }

    @Bean
    @ConditionalOnMissingBean(IAuthTaskTrigger.class)
    public IAuthTaskTrigger authTaskTrigger() {
        return new IAuthTaskTrigger() {
        };
    }
}

