package org.befun.auth.projection;

import lombok.Value;
import org.befun.auth.constant.ContentAuditStatus;
import org.befun.auth.constant.ContentAuditType;
import org.befun.auth.entity.ContentAuditRecord;

@Value
public class SimpleContentAuditRecord {

    Long id;
    String sign;
    ContentAuditType type;
    String words;
    ContentAuditStatus status;

    public static SimpleContentAuditRecord fromEntity(ContentAuditRecord entity) {
        return new SimpleContentAuditRecord(entity.getId(), entity.getSign(), entity.getType(), entity.getWords(), entity.getStatus());
    }
}
