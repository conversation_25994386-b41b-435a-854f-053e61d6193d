package org.befun.auth.projection;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Value;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserDto;
import org.befun.core.rest.view.ResourceViews;

@Value
public class SimpleUser {
    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String truename;
    @JsonView(ResourceViews.Basic.class)
    String avatar;
    @JsonView(ResourceViews.Basic.class)
    Boolean isAdmin;
    @JsonView(ResourceViews.Basic.class)
    String mobile;
    @JsonView(ResourceViews.Basic.class)
    String email;

    public static final String SELECT_COLUMN = "u.id,u.truename,u.avatar,u.is_admin isAdmin,u.mobile,u.email";

    public User mapToUser() {
        User user = new User();
        user.setId(id);
        user.setTruename(truename);
        user.setAvatar(avatar);
        user.setIsAdmin(isAdmin);
        user.setMobile(mobile);
        user.setEmail(email);
        return user;
    }

    public static SimpleUser fromUser(User user) {
        return new SimpleUser(user.getId(), user.getTruename(), user.getAvatar(), user.getIsAdmin(), user.getMobile(), user.getEmail());
    }

    public static SimpleUser fromUser(UserDto user) {
        return new SimpleUser(user.getId(), user.getTruename(), user.getAvatar(), user.getIsAdmin(), user.getMobile(), user.getEmail());
    }

}
