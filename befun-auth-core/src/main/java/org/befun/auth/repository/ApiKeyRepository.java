package org.befun.auth.repository;

import org.befun.auth.entity.ApiKey;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;

public interface ApiKeyRepository extends ResourceRepository<ApiKey, Long> {

    ApiKey findFirstByUserId(Long userId);

    @Query(value = "SELECT * from api_key a where a.deleted=0 and a.user_id in ( SELECT u.id from user u WHERE u.org_id=?1 and u.is_admin=1 ) limit 1", nativeQuery = true)
    ApiKey getOrgApiKey(Long orgId);
}
