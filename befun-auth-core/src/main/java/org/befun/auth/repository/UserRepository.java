package org.befun.auth.repository;

import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * The interface description
 *
 * <AUTHOR>
 */
public interface UserRepository extends ResourceRepository<User, Long> {
    List<User> findAllByOrgId(Long orgId);

    int countByOrgIdAndStatusIn(Long orgId, List<Integer> status);

    List<User> findByMobile(String mobile);

    List<User> findByEmail(String email);

    List<User> findByEmployeeNo(String employeeNo);

    User findFirstByMobile(String mobile);

    User findFirstByEmail(String email);

    User findFirstByOrgIdAndEmail(Long orgId, String email);

    User findFirstByOrgIdAndEmployeeNo(Long orgId, String mobile);

    boolean existsByMobile(String mobile);

    User findFirstByOrgIdAndMobile(Long orgId, String mobile);

    boolean existsByOrgIdAndEmployeeNo(Long orgId, String mobile);

    boolean existsByOrgIdAndUsername(Long orgId, String username);

    boolean existsByEmail(String email);

    User getFirstByEmail(String email);

    SimpleUser findSimpleById(Long id);

    List<SimpleUser> findSimpleByIdIn(Set<Long> ids);

    List<User> findByIdIn(List<Long> ids);

    List<SimpleUser> findByTruename(String truename);

    List<SimpleUser> findSimpleByOrgId(Long orgId);

    List<User> findByEmailIn(Collection<String> emails);

    List<User> findByOrgIdAndEmployeeNoIn(Long orgId, Collection<String> employeeNos);

    List<SimpleUser> findByOrgIdAndStatusInOrderByCreateTimeAsc(Long orgId, Collection<Integer> statuses, Pageable pageable);
}
