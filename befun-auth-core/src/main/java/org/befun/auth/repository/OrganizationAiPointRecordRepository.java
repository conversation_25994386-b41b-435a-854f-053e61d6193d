package org.befun.auth.repository;

import org.befun.auth.constant.AiPointRecordStatus;
import org.befun.auth.constant.AiPointRecordType;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface OrganizationAiPointRecordRepository extends ResourceRepository<OrganizationAiPointRecord, Long> {
    OrganizationAiPointRecord findFirstByOrgIdAndTypeAndSourceId(Long orgId, AiPointRecordType type, Long sourceId);

    @Transactional
    @Modifying
    @Query("update OrganizationAiPointRecord o set o.amount = o.amount + ?1 where o.id = ?2")
    int updateAmountById(Integer amount, Long id);

    @Transactional
    @Modifying
    @Query("update OrganizationAiPointRecord o set o.status = ?1 where o.id = ?2")
    int updateStatusById(AiPointRecordStatus status, Long id);

}