package org.befun.auth.repository;

import org.befun.auth.constant.OrgVersionRecordStatus;
import org.befun.auth.entity.OrganizationVersionRecord;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

public interface OrganizationVersionRecordRepository extends ResourceRepository<OrganizationVersionRecord, Long> {
    OrganizationVersionRecord findFirstByOrgIdAndStatusOrderByCreateTimeDesc(Long orgId, OrgVersionRecordStatus status);
    List<OrganizationVersionRecord> findByOrgIdAndStatus(Long orgId, OrgVersionRecordStatus status);

}