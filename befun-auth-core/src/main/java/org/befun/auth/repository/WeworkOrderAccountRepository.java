package org.befun.auth.repository;

import org.befun.auth.entity.wework.WeworkOrderAccount;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface WeworkOrderAccountRepository extends ResourceRepository<WeworkOrderAccount, Long> {

    WeworkOrderAccount findFirstByOrgIdAndCorpIdAndStatus(Long orgId, String corpId, Integer status);

    WeworkOrderAccount findFirstByOrgIdAndCorpIdAndOpenId(Long orgId, String corpId, String openId);

    @Query(nativeQuery = true, value = "select woa.* from wework_order_account woa" +
            " left join thirdparty_user tu on woa.open_id = tu.open_id and tu.org_id=?1" +
            " where woa.status=1 and woa.org_id=?1 and woa.corpId=?2 and tu.id is null limit 1")
    WeworkOrderAccount getReuseAccount( Long orgId, String corpId);

}
