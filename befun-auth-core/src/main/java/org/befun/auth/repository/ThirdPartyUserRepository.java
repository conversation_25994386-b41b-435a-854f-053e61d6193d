package org.befun.auth.repository;

import org.befun.auth.entity.ThirdPartyUser;
import org.befun.core.repository.ResourceRepository;

import java.util.List;
import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
public interface ThirdPartyUserRepository extends ResourceRepository<ThirdPartyUser, Long> {
    List<ThirdPartyUser> findAllByAppAndUserId(String app, Long userId);

    Optional<ThirdPartyUser> findFirstByAppAndSourceAndOpenIdAndUserId(String app, String source, String openId, Long userId);

    Optional<ThirdPartyUser> findFirstByAppAndSourceAndUserId(String app, String source, Long userId);

    ThirdPartyUser findFirstByAppAndSourceAndOpenId(String app, String source, String openId);

    ThirdPartyUser findFirstByOrgIdAndAppAndSourceAndOpenId(Long orgId, String app, String source, String openId);

    List<ThirdPartyUser> findAllByAppAndSourceAndOpenId(String app, String source, String openId);

    List<ThirdPartyUser> findAllByUserId(Long userId);

    List<ThirdPartyUser> findAllByOrgIdAndAppAndSource(Long orgId, String app, String source);

    long countByAppAndSourceAndUserId(String app, String source, Long userId);
}
