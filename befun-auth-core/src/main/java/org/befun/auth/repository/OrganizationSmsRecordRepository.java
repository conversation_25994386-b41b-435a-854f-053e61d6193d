package org.befun.auth.repository;

import org.befun.auth.constant.SmsRecordType;
import org.befun.auth.entity.OrganizationSmsRecord;
import org.befun.core.repository.ResourceRepository;

import java.util.Optional;

public interface OrganizationSmsRecordRepository extends ResourceRepository<OrganizationSmsRecord, Long> {
    Optional<OrganizationSmsRecord> findFirstByOrgIdAndTypeAndTaskProgressId(Long orgId, SmsRecordType type, Long taskProgressId);
}