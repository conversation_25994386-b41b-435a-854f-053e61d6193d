package org.befun.auth.repository;

import org.befun.auth.entity.OrganizationAiPointRecordResponse;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

public interface OrganizationAiPointRecordResponseRepository extends ResourceRepository<OrganizationAiPointRecordResponse, Long> {
    OrganizationAiPointRecordResponse findFirstByOrgIdAndRecordIdAndResponseId(Long orgId, Long recordId, Long responseId);

    List<OrganizationAiPointRecordResponse> findByOrgIdAndResponseIdAndRecordIdIn(Long orgId, Long responseId, Collection<Long> recordIds);

    boolean existsByOrgIdAndRecordIdAndResponseId(Long orgId, Long recordId, Long responseId);
}