package org.befun.auth.repository;

import org.befun.auth.entity.Department;
import org.befun.auth.projection.SimpleDepartment;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DepartmentRepository extends ResourceRepository<Department, Long> {

    List<Department> findByPid(Long pid);

    List<Department> findByOrgId(Long orgId);

    Department findFirstByOrgIdAndPid(Long orgId, Long pid);

    @Query(value = "SELECT * FROM (SELECT *, SUBSTRING_INDEX(SUBSTRING_INDEX(code, ';', n), ';', -1) AS codeX " +
            "FROM department JOIN (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10) AS numbers " +
            "ON CHAR_LENGTH(code) - CHAR_LENGTH(REPLACE(code, ';', '')) >= n - 1) AS extracted_codes " +
            "WHERE org_id = ?1 AND codeX = ?2", nativeQuery = true)
    Department findFirstByOrgIdAndCode(Long orgId, String code);

    @Query(value = "SELECT * FROM department d WHERE d.org_id=?1 and (" +
            "d.equivalent_code = ?2 OR " +
            "d.equivalent_code LIKE CONCAT(?2, ';%') OR " +
            "d.equivalent_code LIKE CONCAT('%;', ?2, ';%') OR " +
            "d.equivalent_code LIKE CONCAT('%;', ?2))",
            nativeQuery = true)
    List<Department> findFirstByOrgIdAndEquivalentCode(Long orgId, String code);

    List<SimpleDepartment> findSimpleByPid(Long pid);

    SimpleDepartment findSimpleByOrgIdAndId(Long orgId, Long id);

    SimpleDepartment findSimpleById(Long id);

    List<SimpleDepartment> findSimpleByOrgId(Long orgId);
}
