package org.befun.auth.repository;

import org.befun.auth.entity.OrganizationWallet;
import org.befun.auth.projection.SimpleWalletAiPoint;
import org.befun.auth.projection.SimpleWalletMoney;
import org.befun.auth.projection.SimpleWalletSms;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface OrganizationWalletRepository extends ResourceRepository<OrganizationWallet, Long> {
    Optional<OrganizationWallet> findByOrgId(Long orgId);

    OrganizationWallet findFirstByOrgId(Long orgId);

    SimpleWalletSms findFirstWalletSmsByOrgId(Long orgId);

    SimpleWalletMoney findFirstWalletMoneyByOrgId(Long orgId);

    SimpleWalletAiPoint findFirstWalletAiPointByOrgId(Long orgId);

    @Modifying
    @Query(nativeQuery = true, value = "update organization_wallet set money=?2 where org_id=?1")
    int updateWalletMoney(Long orgId, Integer money);

    @Modifying
    @Query(nativeQuery = true, value = "update organization_wallet set sms=?2 where org_id=?1")
    int updateWalletSms(Long orgId, Integer sms);

    @Modifying
    @Query(nativeQuery = true, value = "update organization_wallet set ai_point=?2 where org_id=?1")
    int updateWalletAiPoint(Long orgId, Integer aiPoint);
}