package org.befun.auth.repository;

import org.befun.auth.entity.ThirdPartyMessageYouzan;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ThirdPartyMessageYouzanRepository extends ResourceRepository<ThirdPartyMessageYouzan, Long> {

    List<ThirdPartyMessageYouzan> findByTypeAndStatusOrderByIdDesc(String type, String status);

    boolean existsByMsgIdAndKdtId(String msgId, String kdtId);

    Optional<ThirdPartyMessageYouzan> findFirstByKdtIdAndTradeIdAndWxOpenIdNotNull(String kdtId, String tradeId);

    @Modifying
    @Query("update ThirdPartyMessageYouzan t set t.status = ?1 where t.kdtId = ?2 and t.type = ?3 and t.status = ?4")
    void updateStatusByKdtIdAndTypeAndStatus(String newStatus, String kdtId, String type, String oldStatus);

}