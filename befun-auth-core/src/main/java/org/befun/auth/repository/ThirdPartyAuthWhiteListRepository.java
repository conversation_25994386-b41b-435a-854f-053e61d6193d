package org.befun.auth.repository;

import org.befun.auth.entity.ThirdPartyAuthWhiteList;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

public interface ThirdPartyAuthWhiteListRepository extends ResourceRepository<ThirdPartyAuthWhiteList, Long> {

    boolean existsByThirdPartyAuthIdAndOpenId(Long thirdPartyAuthId, String openId);

    List<ThirdPartyAuthWhiteList> findByThirdPartyAuthIdAndOpenIdIn(Long thirdPartyAuthId, List<String> openIds);

    List<ThirdPartyAuthWhiteList> findByThirdPartyAuthId(Long thirdPartyAuthId);
}