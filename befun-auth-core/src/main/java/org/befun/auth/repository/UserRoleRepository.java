package org.befun.auth.repository;

import org.befun.auth.entity.UserRole;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

public interface UserRoleRepository extends ResourceRepository<UserRole, Long> {
    List<UserRole> findByUserId(Long userId);

    List<UserRole> findByRoleIdAndUserId(Long roleId, Long userId);

    List<UserRole> findByUserIdAndRoleIdIn(Long userId, List<Long> roleIds);

    List<UserRole> findByRoleId(Long roleId);

    List<UserRole> findByRoleIdIn(List<Long> roleIds);
}
