package org.befun.auth.repository;

import org.befun.auth.constant.ContentAuditType;
import org.befun.auth.entity.ContentAuditRecord;
import org.befun.auth.projection.SimpleContentAuditRecord;
import org.befun.core.repository.ResourceRepository;

import java.util.Date;

public interface ContentAuditRecordRepository extends ResourceRepository<ContentAuditRecord, Long> {
    SimpleContentAuditRecord findFirstByTypeAndSignAndCreateTimeGreaterThanOrderByCreateTimeDesc(ContentAuditType type, String sign, Date createTime);

    SimpleContentAuditRecord findFirstByTypeAndSignOrderByCreateTimeDesc(ContentAuditType type, String sign);
}
