package org.befun.auth.repository;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ThirdPartyAuthRepository extends ResourceRepository<ThirdPartyAuth, Long> {
    ThirdPartyAuth findFirstByAuthTypeAndApp(ThirdPartyAuthType authType, String app);
    ThirdPartyAuth findFirstByOrgIdAndAuthTypeAndApp(Long orgId, ThirdPartyAuthType authType, String app);
    ThirdPartyAuth findFirstBySourceAndAuthTypeAndApp(String source, ThirdPartyAuthType authType, String app);
    List<ThirdPartyAuth> findBySourceAndAuthTypeAndApp(String source, ThirdPartyAuthType authType, String app);
    List<ThirdPartyAuth> findByAuthType(ThirdPartyAuthType authType, Pageable pageable);
    List<ThirdPartyAuth> findByAuthTypeAndApp(ThirdPartyAuthType authType, String app);
    long countByAuthTypeAndApp(ThirdPartyAuthType authType, String app);
    List<ThirdPartyAuth> findByOrgIdAndAuthTypeAndApp(Long orgId, ThirdPartyAuthType authType, String app);

}