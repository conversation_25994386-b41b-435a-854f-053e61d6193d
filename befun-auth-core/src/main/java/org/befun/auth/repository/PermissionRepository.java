package org.befun.auth.repository;

import org.befun.auth.entity.Permission;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PermissionRepository extends ResourceRepository<Permission, Long> {

    List<Permission> findByRoleId(Long roleId);

    List<Permission> findByRoleIdIn(List<Long> roleIds);

    List<Permission> findByRoleIdAndPermissionIn(Long roleId, List<String> permissions);

    @Query(nativeQuery = true,
            value = "SELECT p.* FROM user_role ur " +
                    "inner join role r on ur.role_id=r.id " +
                    "inner join permission p on p.role_id = ur.role_id " +
                    "where ur.user_id = ?1 and r.enable=1")
    List<Permission> getByUserId(Long userId);

}
