package org.befun.auth.repository;

import org.befun.auth.entity.Role;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

public interface RoleRepository extends ResourceRepository<Role, Long> {

    /**
     * 用来给未登录的请求查询企业的全部角色
     */
    List<Role> findByOrgId(Long orgId);

    List<Role> findByName(String name);

    Role findFirstByOrgIdAndName(Long orgId, String name);

    Role findFirstByOrgIdAndType(Long orgId, Integer type);

    int countByOrgIdAndType(Long orgId, Integer type);

    @Query(nativeQuery = true,
            value = "select r.* from role r" +
                    " inner join permission p on r.id=p.role_id and p.permission=?2" +
                    " where r.org_id=?1")
    List<Role> getByOrgIdAndPermission(Long orgId, String permission);

    @Query(nativeQuery = true,
            value = "select r.* from role r" +
                    " inner join permission p on r.id=p.role_id and p.permission in ?2" +
                    " where r.org_id=?1")
    List<Role> getByOrgIdAndPermissionIn(Long orgId, List<String> permission);

    @Query(nativeQuery = true,
            value = "SELECT r.* FROM user_role ur " +
                    "inner join role r on ur.role_id=r.id " +
                    "where ur.user_id = ?1")
    List<Role> getByUserId(Long userId);

    @Query(nativeQuery = true,
            value = "SELECT count(1) FROM `user` u " +
                    "INNER JOIN user_role ur on u.id=ur.user_id " +
                    "INNER JOIN `role` r on r.id =ur.role_id " +
                    "where u.id = ?1 and r.`type` = ?2 ")
    int countByUserIdAndRoleType(Long userId, int roleType);

    List<Role> findByOrgIdAndCodeIn(Long orgId, Collection<String> codes);

    List<Role> findByOrgIdAndCodeInAndEnable(Long orgId, Collection<String> codes, Boolean enable);
}
