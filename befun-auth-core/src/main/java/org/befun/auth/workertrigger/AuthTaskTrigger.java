package org.befun.auth.workertrigger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AuthTaskTrigger implements IAuthTaskTrigger {

    @Autowired(required = false)
    private List<IAuthTaskConsumer> taskConsumers;

    @Override
    public List<IAuthTaskConsumer> getConsumers() {
        return taskConsumers;
    }
}
