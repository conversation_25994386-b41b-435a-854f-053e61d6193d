package org.befun.auth.workertrigger.consumer;

import org.befun.auth.workertrigger.IAuthEventConsumer;
import org.befun.extension.systemupdate.SystemUpdateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class LocalAuthEventConsumer implements IAuthEventConsumer {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private SystemUpdateHelper systemUpdateHelper;

    @Override
    public void apiKeyRefresh(Long orgId, Long triggerUserId, Long apiKeyId) {
        stringRedisTemplate.opsForHash().delete("cache:api-key", orgId.toString());
    }

    @Override
    public void departmentRefresh(Long orgId, Long triggerUserId) {
        stringRedisTemplate.delete(String.format("cache:department:%d", orgId));
    }

    @Override
    public void departmentCreate(Long orgId, Long triggerUserId, Long departmentId) {
        stringRedisTemplate.delete(String.format("cache:department:%d", orgId));
    }

    @Override
    public void departmentUpdate(Long orgId, Long triggerUserId, Long departmentId) {
        stringRedisTemplate.delete(String.format("cache:department:%d", orgId));
    }

    @Override
    public void departmentDelete(Long orgId, Long triggerUserId, Long departmentId) {
        stringRedisTemplate.delete(String.format("cache:department:%d", orgId));
    }

    @Override
    public void userLogin(Long orgId, Long userId) {
        systemUpdateHelper.userLogin(orgId, userId);
    }
}
