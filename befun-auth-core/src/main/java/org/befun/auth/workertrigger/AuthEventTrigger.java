package org.befun.auth.workertrigger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AuthEventTrigger implements IAuthEventTrigger {

    @Autowired(required = false)
    private List<IAuthEventConsumer> eventConsumers;

    @Override
    public List<IAuthEventConsumer> getConsumers() {
        return eventConsumers;
    }
}
