package org.befun.auth.constant;

import org.befun.core.utils.DateHelper;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.befun.auth.constant.QueryTypeEmptyFlag.*;

public enum QueryValueType {
    inputText,
    inputNumber,
    selectOne,
    selectMulti(true),
    selectDate,
    selectDateRange(true),
    none,
    ;

    private final boolean multiValue;

    QueryValueType() {
        this.multiValue = false;
    }

    QueryValueType(boolean multiValue) {
        this.multiValue = multiValue;
    }

    public String query(QueryType queryType, String column, String value) {
        return query(queryType, column, value, List.of(QueryTypeEmptyFlag.NULL, ZERO_LENGTH_STRING));
    }

    public String query(QueryType queryType, String column, String value, List<QueryTypeEmptyFlag> emptyFlags) {
        switch (queryType) {
            case like -> {
                return like(column, value);
            }
            case notLike -> {
                return notLike(column, value);
            }
            case in -> {
                return in(column, value);
            }
            case notIn -> {
                return notIn(column, value);
            }
            case isEmpty -> {
                return isEmpty(column, value, emptyFlags);
            }
            case notEmpty -> {
                return notEmpty(column, value, emptyFlags);
            }
            case eq -> {
                return eq(column, value);
            }
            case neq -> {
                return neq(column, value);
            }
            case gt -> {
                return gt(column, value);
            }
            case ge -> {
                return ge(column, value);
            }
            case lt -> {
                return lt(column, value);
            }
            case le -> {
                return le(column, value);
            }
            case range -> {
                return range(column, value);
            }
            case beforeDays -> {
                return beforeDays(column, value);
            }
            case afterDays -> {
                return afterDays(column, value);
            }
        }
        return null;
    }

    String like(String column, String value) {
        if (multiValue) {
            return Arrays.stream(value.split(",")).map(v ->
                    String.format("%1$s like '%%%2$s%%'", column, v)
            ).collect(Collectors.joining(" or ", "(", ")"));
        }
        return String.format("%1$s like '%%%2$s%%'", column, value);
    }

    String notLike(String column, String value) {
        if (multiValue) {
            return Arrays.stream(value.split(",")).map(v ->
                    String.format("%1$s not like '%%%2$s%%'", column, v)
            ).collect(Collectors.joining(" and ", "(", ")"));
        }
        return String.format("%1$s not like '%%%2$s%%'", column, value);
    }

    String in(String column, String value) {
        return String.format("%1$s in (%2$s)", column, Arrays.stream(value.split(",")).map(v ->
                String.format("'%s'", v)
        ).collect(Collectors.joining(",")));
    }

    String notIn(String column, String value) {
        return String.format("%1$s not in (%2$s)", column, Arrays.stream(value.split(",")).map(v ->
                String.format("'%s'", v)
        ).collect(Collectors.joining(",")));
    }

    String isEmpty(String column, String ignore, List<QueryTypeEmptyFlag> emptyFlags) {
        if (!emptyFlags.isEmpty()) {
            return emptyFlags.stream().map(flag -> {
                if (flag == NULL_STRING) {
                    return String.format("%1$s = 'null'", column);
                } else if (flag == ZERO_LENGTH_STRING) {
                    return String.format("%1$s = ''", column);
                } else if (flag == ZERO_LENGTH_ARRAY) {
                    return String.format("%1$s = '[]'", column);
                } else {
                    return String.format("%1$s is null", column);
                }
            }).collect(Collectors.joining(" or ", "(", ")"));
        }
        return "";
    }

    String notEmpty(String column, String ignore, List<QueryTypeEmptyFlag> emptyFlags) {
        if (!emptyFlags.isEmpty()) {
            return emptyFlags.stream().map(flag -> {
                if (flag == NULL_STRING) {
                    return String.format("%1$s != 'null'", column);
                } else if (flag == ZERO_LENGTH_STRING) {
                    return String.format("%1$s != ''", column);
                } else if (flag == ZERO_LENGTH_ARRAY) {
                    return String.format("%1$s != '[]'", column);
                } else {
                    return String.format("%1$s is not null", column);
                }
            }).collect(Collectors.joining(" and "));
        }
        return "";
    }

    String eq(String column, String value) {
        if (multiValue) {
            return in(column, value);
        }
        return String.format("%1$s = '%2$s'", column, value);
    }

    String neq(String column, String value) {
        if (multiValue) {
            return notIn(column, value);
        }
        return String.format("%1$s != '%2$s'", column, value);
    }

    String gt(String column, String value) {
        return String.format("%1$s > '%2$s'", column, value);
    }

    String ge(String column, String value) {
        return String.format("%1$s >= '%2$s'", column, value);
    }

    String lt(String column, String value) {
        return String.format("%1$s < '%2$s'", column, value);
    }

    String le(String column, String value) {
        return String.format("%1$s <= '%2$s'", column, value);
    }

    String range(String column, String value) {
        String[] dates = value.split(",");
        LocalDate start = DateHelper.parseDate(dates[0]);
        LocalDate end = DateHelper.parseDate(dates[1]);
        return String.format("DATE_FORMAT(%1$s,'%%Y-%%m-%%d') >= '%2$s' and DATE_FORMAT(%1$s,'%%Y-%%m-%%d') < '%3$s'", column, start.toString(), end.plusDays(1));
    }

    String beforeDays(String column, String value) {
        LocalDate end = LocalDate.now();
        LocalDate start = end.minusDays(Integer.parseInt(value));
        return String.format("DATE_FORMAT(%1$s,'%%Y-%%m-%%d') >= '%2$s' and DATE_FORMAT(%1$s,'%%Y-%%m-%%d') <= '%3$s'", column, start, end);
    }

    String afterDays(String column, String value) {
        LocalDate start = LocalDate.now().plusDays(1);
        LocalDate end = start.plusDays(Integer.parseInt(value));
        return String.format("DATE_FORMAT(%1$s,'%%Y-%%m-%%d') >= '%2$s' and DATE_FORMAT(%1$s,'%%Y-%%m-%%d') < '%3$s'", column, start, end);
    }
}
