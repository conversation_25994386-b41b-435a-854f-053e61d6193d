package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
public enum VerifyCodeUseFor {
    REGISTER("register"),
    LOGIN("login"),
    BIND("bind"),
    RESET_PASSWORD("reset-password"),
    MFA("mfa"),
    SURVEY_QUESTION_MOBILE("survey-question-mobile")
    ;
    private final String value;

    VerifyCodeUseFor(String value) {
        this.value = value;
    }

    public static VerifyCodeUseFor parse(String useFor) {
        return StringUtils.isEmpty(useFor) ? null : Arrays.stream(values()).filter(i -> i.value.equals(useFor)).findFirst().orElse(null);
    }
}
