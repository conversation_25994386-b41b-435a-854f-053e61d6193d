package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum OwnerResourceType {
    ALL(null),
    JOURNEY("update journey_map set user_id=%2$s,group_id=null where user_id in (%1$s)"),
    SURVEY("update survey set user_id=%2$s,group_id=null where user_id in (%1$s)"),
    PERSONA("update customer_persona set user_id=%2$s,group_id=null where user_id in (%1$s)"),
    DASHBOARD("update bi_dashboard set user_id=%2$s where user_id in (%1$s)"),
    SEND("update send_manage set user_id=%2$s,group_id=null where user_id in (%1$s)"),
    WARNING("update event_monitor_rules set user_id=%2$s where user_id in (%1$s)"),
    ;



    private final String changeOwnerSql;

    OwnerResourceType(String changeOwnerSql) {
        this.changeOwnerSql = changeOwnerSql;
    }


    public List<String> buildChangeOwnerSql(List<Long> fromUserIds, Long targetUserId) {
        // 如果是ALL 直接返回所有sql
        ArrayList<String> list = new ArrayList<String>();
        String fromUserIdsConcat = fromUserIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        if (this == ALL) {
            list.addAll(Stream.of(OwnerResourceType.values())
                    .filter(type -> type != ALL)
                    .map(OwnerResourceType::getChangeOwnerSql)
                    .filter(StringUtils::isNotEmpty)
                    .map(sql -> String.format(sql, fromUserIdsConcat, targetUserId))
                    .collect(Collectors.toList()));
            return list;
        }
        if (StringUtils.isNotEmpty(getChangeOwnerSql())) {
            list.add(String.format(getChangeOwnerSql(), fromUserIdsConcat, targetUserId));
        }
        return list;
    }


}
