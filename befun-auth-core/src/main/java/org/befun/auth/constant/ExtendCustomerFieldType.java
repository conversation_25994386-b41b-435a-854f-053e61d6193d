package org.befun.auth.constant;

import lombok.Getter;

import java.util.List;

import static org.befun.auth.constant.ExtendCustomerFieldFormat.*;

@Getter
public enum ExtendCustomerFieldType {

    TEXT(FORMAT_TEXT, List.of(FORMAT_TEXT)),
    NUMBER(FORMAT_INTEGER, List.of(FORMAT_INTEGER, FORMAT_DECIMAL, FORMAT_PERCENT)),
    DATE(FORMAT_DATE_TIME, List.of(FORMAT_YEAR, FORMAT_YEAR_MONTH, FORMAT_DATE, FORMAT_DATE_TIME)),
    SINGLE_CHOICE(true, FORMAT_CHOICE, List.of(FORMAT_CHOICE)),
    MULTIPLE_CHOICE(true, FORMAT_CHOICE, List.of(FORMAT_CHOICE)),
    ;
    private final boolean hasOptions;
    private final ExtendCustomerFieldFormat defaultFormat;
    private final List<ExtendCustomerFieldFormat> supportFormats;

    ExtendCustomerFieldType(ExtendCustomerFieldFormat defaultFormat, List<ExtendCustomerFieldFormat> supportFormats) {
        this.hasOptions = false;
        this.defaultFormat = defaultFormat;
        this.supportFormats = supportFormats;
    }

    ExtendCustomerFieldType(boolean hasOptions, ExtendCustomerFieldFormat defaultFormat, List<ExtendCustomerFieldFormat> supportFormats) {
        this.hasOptions = hasOptions;
        this.defaultFormat = defaultFormat;
        this.supportFormats = supportFormats;
    }

    public boolean testFormat(ExtendCustomerFieldFormat format) {
        return supportFormats.contains(format);
    }
}
