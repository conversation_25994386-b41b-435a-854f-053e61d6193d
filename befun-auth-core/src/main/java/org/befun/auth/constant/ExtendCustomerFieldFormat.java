package org.befun.auth.constant;

import lombok.Getter;

@Getter
public enum ExtendCustomerFieldFormat {

    FORMAT_TEXT,
    FORMAT_CHOICE,
    FORMAT_INTEGER,
    FORMAT_DECIMAL(true),
    FORMAT_PERCENT(true),
    FORMAT_YEAR,
    FORMAT_YEAR_MONTH,
    FORMAT_DATE,
    FORMAT_DATE_TIME,
    ;
    private final boolean hasDecimal;

    ExtendCustomerFieldFormat() {
        this(false);
    }

    ExtendCustomerFieldFormat(boolean hasDecimal) {
        this.hasDecimal = hasDecimal;
    }
}
