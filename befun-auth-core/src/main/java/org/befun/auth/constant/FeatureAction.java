package org.befun.auth.constant;

import lombok.Getter;

@Getter
public enum FeatureAction {

    JOURNEY_INDICATOR_WARNING(AppVersion.PROFESSION),
    JOURNEY_EVENT_WARNING(AppVersion.PROFESSION),
    BI_DASHBOARD_NOTIFY(AppVersion.PROFESSION),
    WEBHOOK_NOTIFY(AppVersion.FREE),
    ;

    FeatureAction(AppVersion defaultMinVersion) {
        this.defaultMinVersion = defaultMinVersion;
    }

    private final AppVersion defaultMinVersion;
}
