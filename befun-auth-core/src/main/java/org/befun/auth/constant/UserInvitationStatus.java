package org.befun.auth.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum UserInvitationStatus {

    // 已激活
    ACTIVE,
    // 已邀请，未发送
    INIT,
    // 已发送
    NOTIFY,
    ;

    public static boolean canNotify(Integer status) {
        return status != null && (status == INIT.ordinal() || status == NOTIFY.ordinal());
    }

    public static boolean isActive(Integer status) {
        return status != null && status == ACTIVE.ordinal();
    }

}
