package org.befun.auth.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.befun.auth.constant.Permissions.*;


/**
 * <AUTHOR>
 */

@Getter
public enum AppVersionPermissions {
    EMPTY(new ArrayList<>()),
    SUPER_ADMIN_FREE(AppVersion.FREE),
    SUPER_ADMIN_BASE(AppVersion.BASE),
    SUPER_ADMIN_UPDATE(AppVersion.UPDATE),
    SUPER_ADMIN_PROFESSION(AppVersion.PROFESSION),
    COSTUMER_MANAGER_BASE(new ArrayList<>()),
    COSTUMER_MANAGER_UPDATE(new ArrayList<>()),
    COSTUMER_MANAGER_PROFESSION(new ArrayList<>()),
    MEMBER(List.of(
            CUSTOMER_LIFE_VIEW,
            TOUCH_MANAGE_SURVEY_VIEW,
            EVENTS_EVENT_WARNING_VIEW,
            EVENTS_EVENT_ACTION_VIEW,
            CUSTOMER_CENTRE_VIEW
    )),
    RESEARCHER(List.of(
            WORKBENCH_DASHBOARD_VIEW,
            BI_INDEX_VIEW,
            BI_INDEX_EDIT,
            BI_INDEX_DOWNLOAD,
            BI_DATASET_VIEW,
            BI_DATASET_EDIT,
            CUSTOMER_LIFE_VIEW,
            CUSTOMER_LIFE_EDIT,
            CUSTOMER_PORTRAIT_VIEW,
            CUSTOMER_PORTRAIT_EDIT,
            CUSTOMER_CENTRE_VIEW,
            CUSTOMER_CENTRE_EDIT,
            TOUCH_MANAGE_SURVEY_VIEW,
            TOUCH_MANAGE_SURVEY_EDIT,
            TOUCH_MANAGE_SURVEY_PUBLISH,
            TOUCH_MANAGE_SURVEY_VERIFY,
            TOUCH_MANAGE_SEND_MANAGE_VIEW,
            EVENTS_EVENT_WARNING_VIEW,
            EVENTS_EVENT_WARNING_EDIT,
            EVENTS_EVENT_ACTION_VIEW,
            EVENTS_EVENT_ACTION_EDIT,
            EVENTS_WARNING_WORD_VIEW,
            SYS_MANAGE_USER_MANAGE_VIEW,
            SYS_MANAGE_USER_MANAGE_EDIT,
            SYS_MANAGE_USER_MANAGE_TRANSFER,
            SYS_MANAGE_LEVEL_MANAGE_VIEW,
            SYS_MANAGE_LEVEL_MANAGE_EDIT,
            SYS_MANAGE_ROLE_MANAGE_VIEW,
            SYS_MANAGE_ROLE_MANAGE_EDIT,
            SYS_MANAGE_ROLE_MANAGE_ENABLE
    )),
    IT(List.of(
            TOUCH_MANAGE_SURVEY_VIEW,
            TOUCH_MANAGE_SEND_MANAGE_VIEW
    )),
    ;

    private final AppVersion version;
    private final List<Permissions> permissions;

    AppVersionPermissions(List<Permissions> permissions) {
        this.version = AppVersion.EMPTY;
        this.permissions = permissions;
    }

    AppVersionPermissions(AppVersion version) {
        this.version = version;
        this.permissions = new ArrayList<>();
    }

    public List<String> mapToString() {
        if (permissions.isEmpty()) {
            return Permissions.permissions(version).stream().map(Permissions::getPath).collect(Collectors.toList());
        }
        return permissions.stream().map(Permissions::getPath).collect(Collectors.toList());
    }
}
