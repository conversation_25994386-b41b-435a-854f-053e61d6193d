package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.ResourcePermissionRelationTypes;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum ResourcePermissionRelationType implements ResourcePermissionRelationTypes {
    OWNER(100),
    ADMIN(90),
    MEMBER(80),
    VERIFY(70),
    NONE(0);

    private final int permission;

    ResourcePermissionRelationType(int permission) {
        this.permission = permission;
    }

    public static ResourcePermissionRelationType parseOrDefault(String name, ResourcePermissionType resourceType) {
        if (StringUtils.isEmpty(name)) {
            return resourceType.getRelations().get(0);
        }
        return Arrays.stream(values()).filter(i -> i.name().equalsIgnoreCase(name)).findFirst().orElse(NONE);
    }

    public static ResourcePermissionRelationType parse(String name) {
        if (StringUtils.isEmpty(name)) {
            return NONE;
        }
        return Arrays.stream(values()).filter(i -> i.name().equalsIgnoreCase(name)).findFirst().orElse(NONE);
    }

    public static Set<ResourcePermissionRelationType> parseTypes(String types) {
        if (StringUtils.isEmpty(types)) {
            return Set.of();
        }
        return Arrays.stream(types.split(",")).map(ResourcePermissionRelationType::parse).filter(j -> j != NONE).collect(Collectors.toSet());
    }

    public static ResourcePermissionRelationType max(List<ResourcePermissionRelationType>  types){
        return types.stream().max(Comparator.comparingInt(o -> o.permission)).orElse(NONE);
    }

    public static ResourcePermissionRelationType max(ResourcePermissionRelationType... types){
        return Arrays.stream(types).max(Comparator.comparingInt(o -> o.permission)).orElse(NONE);
    }

    public boolean isOwner() {
        return this == OWNER;
    }

    public boolean atLeast(ResourcePermissionRelationType type) {
        return permission >= type.permission;
    }

    public boolean atLeastAdmin() {
        return permission >= ADMIN.permission;
    }

    public boolean atLeastMember() {
        return permission >= MEMBER.permission;
    }
}
