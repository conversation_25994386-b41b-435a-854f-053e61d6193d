package org.befun.auth.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum IndustryType {
    HEALTH("health", "医美健康"),
    HOTEL("hotel", "酒店"),
    RESTORE("restore", "零售餐饮"),
    EDUCATION("education", "教育金融"),
    CAR("car", "汽车"),
    FINANCE("finance", "金融保险"),
    OTHERS("others", "其他");

    private final String code;
    private final String text;

    IndustryType(String code, String text){
        this.code = code;
        this.text = text;
    }
}
