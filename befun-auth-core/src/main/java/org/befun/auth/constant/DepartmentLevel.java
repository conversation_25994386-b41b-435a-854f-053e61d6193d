package org.befun.auth.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum DepartmentLevel {
    // 总部
    HEADQUARTERS(1, "总部"),
    // 大区
    REGION(2, "大区"),
    // 城市
    CITY(3, "城市"),
    // 门店
    STORE(4, "门店");

    private final int value;
    private final String text;


    DepartmentLevel(int value, String text) {
        this.value = value;
        this.text = text;
    }
}
