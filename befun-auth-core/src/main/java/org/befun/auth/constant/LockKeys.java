package org.befun.auth.constant;

import org.befun.extension.constant.LockKey;

public enum LockKeys implements Lock<PERSON>ey {

    ai_point_record("lock:ai_point_record:%s:%s"),  // type sourceId
    ai_point_record_response("lock:ai_point_record_response:%s:%s"),  // recordId responseId
    ;
    private final boolean format;
    private final String placeholder;

    LockKeys(String placeholder) {
        this.format = true;
        this.placeholder = placeholder;
    }

    LockKeys(boolean format, String placeholder) {
        this.format = format;
        this.placeholder = placeholder;
    }

    @Override
    public String getPlaceholder() {
        return placeholder;
    }

    @Override
    public boolean isFormat() {
        return format;
    }
}
