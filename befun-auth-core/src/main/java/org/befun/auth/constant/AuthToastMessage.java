package org.befun.auth.constant;

import lombok.Getter;
import org.befun.core.constant.ErrorInternalCode;
import org.befun.extension.toast.ToastMessage;

@Getter
public enum AuthToastMessage implements ToastMessage {

    //    ORG_NOT_EXISTS("您的账号尚未开通，请联系0755-********"),
    ORG_NOT_EXISTS("您的账号已注册“瀚一数据”旗下产品，如需多平台登录请联系0755-********"),
    ORG_DISABLED("您的账号无登录权限，如有问题请联系0755-********"),
    ORG_EXPIRED("您的账号无登录权限，如有问题请联系0755-********"),
    ORG_MEMBER_OVERSIZE("成员数量已达到满额，请联系管理员购买可使用名额！"),

    ACCOUNT_NOT_EXISTS("账户不存在"),
    ACCOUNT_ADMIN_DISABLED("您的账号还未激活，我们将会在48小时内为你激活账号，如需更多帮助请联系0755-********"),
    ACCOUNT_MEMBER_DISABLED("您的账号已被禁用，如有问题请联系系统管理员"),
    ACCOUNT_MEMBER_INACTIVE("您的账号未激活，请前往邮箱激活使用"),
    ACCOUNT_MEMBER_ACTIVATED("账号已激活"),
    ACCOUNT_INVITATION_DISABLED("邀请已失效，请联系管理员重新邀请"),

    LOGIN_PASSWORD_ERROR("账户或密码错误"),
    LOGIN_PASSWORD_ERROR_TIMES("您输入的密码错误，错误%d次之后，账号将被锁定"),
    LOGIN_CODE_ERROR_TIMES("您输入的验证码错误，错误%d次之后，账号将被锁定"),
    LOGIN_PASSWORD_ERROR_LOCK("当前账号已被锁定，请15分钟后再试"),
    LOGIN_PASSWORD_MFA(100001, "请通过安全验证"),
    LOGIN_REQUIRE("请重新登录"),
    LOGIN_USER_LIMIT("账号数量已经达到版本的限额，该账号无法登录，请联系管理员升级版本！"),

    GRAPH_CAPTCHA_REQUIRED(ErrorInternalCode.GRAPH_CAPTCHA_REQUIRED.getValue(), "请输入图形验证码"),
    GRAPH_CAPTCHA_ERROR(ErrorInternalCode.GRAPH_CAPTCHA_ERROR.getValue(), "图形验证码错误"),

    EMAIL_EXISTS("邮箱已存在"),
    EMAIL_EXISTS_USE_BIND("该邮箱已被其他账号绑定"),
    EMAIL_EXISTS_USE_REGISTER("邮箱已注册"),
    EMAIL_NOT_EXISTS("邮箱不存在"),
    EMAIL_NOT_EXISTS_USE_LOGIN("该邮箱还未注册"),
    EMAIL_NOT_EXISTS_USE_RESET_PASSWORD("该邮箱还未注册"),

    MOBILE_EXISTS("该手机号已存在"),
    MOBILE_EXISTS_USE_BIND("该手机号已被其他账号绑定"),
    MOBILE_EXISTS_USE_REGISTER("该手机号已注册，请直接登录"),
    MOBILE_NOT_EXISTS("该手机号不存在"),
    MOBILE_NOT_EXISTS_USE_LOGIN("该手机号还未注册"),
    MOBILE_NOT_EXISTS_USE_RESET_PASSWORD("该手机号还未注册"),

    VERIFY_CODE_SEND_FREEZE("发送太频繁，请稍后再试"),
    VERIFY_CODE_SEND_FAIL("验证码发送失败"),
    VERIFY_CODE_CHECK_ERROR("验证码错误"),
    VERIFY_CODE_EXPIRED("验证码已过期"),
    VERIFY_CODE_MOBILE_BLACKLIST("该手机号未通过安全检查，请更换手机号注册"),

    USER_CHANGE_ROLE_ERROR("不能修改用户角色"),

    ROLE_NAME_EXISTS("该角色名称已存在"),
    ROLE_CODE_EXISTS("该角色编号已存在"),

    DEPARTMENT_SAME_NAME("同一部门层级下名称不能相同"),
    DEPARTMENT_SAME_CODE("部门编号不能重复"),
    DEPARTMENT_SAME_ID("部门id不能重复"),
    DEPARTMENT_MULTI_ROOT("一级部门只能有一个"),
    DEPARTMENT_NAME_TO_LONG("部门名称不能超过20字符"),
    DEPARTMENT_CODE_TO_LONG("部门编号不能超过20字符"),
    DEPARTMENT_CODE_INVALID_CHAR("部门编号只能输入字母、数字"),
    DEPARTMENT_LEVEL_LIMIT("部门层级不能超过5级"),
    DEPARTMENT_PARENT_NOT_FOUND("未找到上级部门"),
    DEPARTMENT_PARENT_IS_CHILD("新的上级部门不能是子部门"),
    DEPARTMENT_UPLOAD_FILE_ERROR("上传文件格式有误，请按模板内格式规范修改后重新上传！"),

    WECHAT_WORK_NO_CROP("您的企业微信尚未添加“体验家”应用，请联系企微管理员添加后使用！"),
    WECHAT_WORK_NO_AGENT("您未在“体验家”应用的可见范围内，请联系企微管理员添加后使用！"),
    WECHAT_WORK_NOT_BIND("企业微信未绑定用户"),
    WECHAT_WORK_NOT_MATCH("待绑定账号的企业微信与管理员配置的企业微信不匹配，请联系企微管理员！"),
    WECHAT_WORK_NOT_SUPPORT("未启用企业微信登录"),
    WECHAT_WORK_FAILURE("企业微信登录失败"),
    WECHAT_WORK_EMAIL_NOT_VALID("邮箱认证未通过"),
    WECHAT_WORK_BIND_CODE_EXPIRE("企业微信绑定已超时"),
    WECHAT_WORK_BIND_EMAIL_NOT_REGISTER("您的邮箱尚未注册，请联系所在企业的体验家系统管理员邀请加入！"),
    WECHAT_WORK_BIND_EMAIL_NOT_MATCH_ORG("该邮箱不属于本企业的体验家账号！"),
    WECHAT_WORK_BIND_EMAIL_EXISTS("该邮箱已被其他账号绑定！"),
    WECHAT_WORK_BIND_EMAIL_SUCCESS("该邮箱已验证，请重新扫码登录！"),
    WECHAT_WORK_BIND_EMAIL_DISABLE("您的账号已被禁用，如有问题请联系系统管理员"),
    WECHAT_WORK_BIND_MEMBER_EXISTS("该企业微信已被其他账号绑定！"),
    WECHAT_WORK_ORDER_NOT_SUCCESS("订单未支付成功"),
    WECHAT_WORK_ORDER_NO_BALANCE("企业微信许可已全部激活"),
    WECHAT_WORK_CREATE_EMAIL_EXISTS("该邮箱已注册！"),
    WECHAT_WORK_CREATE_EMAIL_FAILURE("创建用户失败"),

    LINK_VERIFY_INVITE_EXPIRE("该链接已失效，请联系团队管理员获取新的邀请链接"),
    LINK_VERIFY_BIND_EXPIRE("该链接已失效，请重新获取新的验证链接"),
    LINK_VERIFY_BIND_SUCCESS("该链接已验证通过"),
    LINK_VERIFY_CREATE_WECHAT_WORK_EXPIRE("该链接已失效，请联系团队管理员获取新的邀请链接"),

    BAIDU_TONGJI_BIND_ERROR("您输入的百度统计API信息不正确，请前往检查！"),
    BAIDU_TONGJI_NO_SITE("您还未在网站安装百度统计，请前往检查！"),

    SHEN_CE_BIND_NOT_FOUND("您的神策数据API信息未配置，请前往检查！"),
    SHEN_CE_BIND_ERROR("您输入的神策数据API信息不正确，请前往检查！"),
    SHEN_CE_PARAM_ERROR("您输入的神策数据分析内容不正确！"),
    ;

    private int internalCode = 0;
    private final String key;
    private final String defaultMessage;

    AuthToastMessage(String defaultMessage) {
        this.key = name().toLowerCase();
        this.defaultMessage = defaultMessage;
    }

    AuthToastMessage(int internalCode, String defaultMessage) {
        this.key = name().toLowerCase();
        this.internalCode = internalCode;
        this.defaultMessage = defaultMessage;
    }
}
