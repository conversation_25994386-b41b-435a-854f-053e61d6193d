package org.befun.auth.constant;

public enum WechatWorkOrderStatus {
    // 订单状态，0：待支付，1：已支付，2：未支付，订单已关闭，3：未支付，订单已过期，4：申请退款中，5：退款成功，6：退款被拒绝
    WAIT,
    SUCCESS,
    CLOSE,
    EXPIRE,
    REFUND_WAITING,
    REFUND_SUCCESS,
    REFUND_CANCEL,
    ;

    public static boolean needQueryStatus(Integer status) {
        return status == null || status == WAIT.ordinal();
    }

    public static boolean isSuccess(Integer status) {
        return status != null && status == SUCCESS.ordinal();
    }
}
