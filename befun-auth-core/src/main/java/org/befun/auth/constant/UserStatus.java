package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
public enum UserStatus {

    // 待激活(旧版新增用户的默认状态)
    @Deprecated(since = "1.6.9")
    INIT(0),
    // 启用
    ENABLE(1),
    // 禁用
    DISABLE(2),
    // 邀请待激活
    INVITE(3),
    ;

    private final int status;

    UserStatus(int status) {
        this.status = status;
    }

    public static int mapStatus(Integer status) {
        if (status != null) {
            if (status == INIT.status || status == ENABLE.status) {
                return ENABLE.status;
            } else if (status == INVITE.status) {
                return INVITE.status;
            }
        }
        return DISABLE.status;
    }

    public static UserStatus parseLabel(String label) {
        if (StringUtils.isNotEmpty(label)) {
            return Arrays.stream(values()).filter(i -> i.name().equalsIgnoreCase(label)).findFirst().orElse(null);
        }
        return null;
    }

    public static List<UserStatus> parseLabels(String status) {
        if (StringUtils.isNotEmpty(status)) {
            return Arrays.stream(status.split(","))
                    .map(UserStatus::parseLabel)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return null;
    }

    public static String checkLabels(String status) {
        return Optional.ofNullable(parseLabels(status))
                .map(i -> i.stream()
                        .map(UserStatus::name)
                        .collect(Collectors.joining(",")))
                .orElse(null);
    }

    public static UserStatus parseStatus(String status) {
        if (NumberUtils.isDigits(status)) {
            int s = Integer.parseInt(status);
            return Arrays.stream(values()).filter(i -> i.status == s).findFirst().orElse(null);
        }
        return null;
    }

    public static List<UserStatus> parseStatues(String status) {
        if (StringUtils.isNotEmpty(status)) {
            return Arrays.stream(status.split(","))
                    .map(UserStatus::parseStatus)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return null;
    }

    public static List<Integer> checkStatues(String status) {
        return Optional.ofNullable(parseStatues(status))
                .map(i -> i.stream()
                        .map(UserStatus::getStatus)
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    public static boolean isInvite(Integer status) {
        return status != null && status == INVITE.status;
    }

    public static boolean isEnable(Integer status) {
        return status != null && (status == ENABLE.status || status == INIT.status);
    }

    public static List<Integer> activeStatus() {
        return List.of(INIT.status, ENABLE.status, INVITE.status);
    }

    public static List<Integer> enableStatus() {
        return List.of(INIT.status, ENABLE.status);
    }

    public static boolean isActiveStatus(Integer status) {
        return status != null && activeStatus().contains(status);
    }
}
