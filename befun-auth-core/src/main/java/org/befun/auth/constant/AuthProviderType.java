package org.befun.auth.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum AuthProviderType {
    password,
    email,
    mobile,
    refresh_token,
    ldap,
    cas,
    wechat_mp,
    wechat_work,
    oauth,
    saml,
    ;

    public static AuthProviderType getProvider(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        return Arrays.stream(values()).filter(i -> source.startsWith(i.name())).findFirst().orElse(oauth);
    }

    public static String providerKey(String source) {
        return Optional.ofNullable(getProvider(source))
                .map(j -> "befun_auth_" + j.name())
                .orElse(null);
    }

}
