package org.befun.auth.constant;

import lombok.Getter;
import org.befun.auth.dto.usertask.UserTaskResultDto;
import org.befun.task.constant.TaskTypeBelong;
import org.befun.task.dto.TaskType;

@Getter
public enum UserTaskType implements TaskType {
    responseDownload(TaskTypeBelong.USER, String.class, UserTaskResultDto.class),
    syncWechatOpenSyncCustomer(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    syncSurveyQuota(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    sendSurveyChannel(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    batchAddJourney(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    event_notify_customer(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    syncWechatOpenSyncTemplate(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    openApiAddJourney(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    triggerYouzanAddJourney(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    eventWarningRerun(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    analyseTextProject(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    responseImport(TaskTypeBelong.USER, String.class, UserTaskResultDto.class),
    timerAddJourney(TaskTypeBelong.USER, String.class, UserTaskResultDto.class),
    quotaSync(TaskTypeBelong.ORGANIZATION, String.class, UserTaskResultDto.class),
    ;

    private final TaskTypeBelong belongTo;
    private final Class<?> paramClass;
    private final Class<?> resultClass;

    UserTaskType(TaskTypeBelong belongTo, Class<?> paramClass, Class<?> resultClass) {
        this.belongTo = belongTo;
        this.paramClass = paramClass;
        this.resultClass = resultClass;
    }
}
