package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
public enum UserGuideIndoType {
    JOURNEY_MAP,
    SURVEY,
    CUSTOMER_CENTER,
    OLD_SP_USER_LOGIN,
    MOBILE_LOGIN,
    BIND_WECHAT_OPEN,
    GPT_PROTOCOL;

    public static Set<UserGuideIndoType> parse(String value) {
        if (StringUtils.isEmpty(value)) {
            return new HashSet<>();
        }

        List<String> valueString = Arrays.asList(value.split(","));

        return Arrays.stream(values()).filter(v -> valueString.contains(v.name())).collect(Collectors.toSet());
    }

    public static String format(Set<UserGuideIndoType> value) {
        if(CollectionUtils.isEmpty(value)) {;
            return null;
        }
        return value.stream().map(UserGuideIndoType::name).collect(Collectors.joining(","));
    }


}
