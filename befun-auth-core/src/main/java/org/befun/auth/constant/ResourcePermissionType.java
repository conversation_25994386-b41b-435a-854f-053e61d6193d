package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.ResourcePermissionTypes;

import java.util.List;

import static org.befun.auth.constant.ResourcePermissionRelationType.*;

@Getter
public enum ResourcePermissionType implements ResourcePermissionTypes {

    JOURNEY(true, List.of(ADMIN, MEMBER), "select user_id from journey_map where id=%d"),
    JOURNEY_GROUP(true, List.of(ADMIN, MEMBER), "select user_id from journey_map_group where id=%d"),
    SURVEY(false, List.of(ADMIN), "select user_id from survey where id=%d"),
    SURVEY_GROUP(false, List.of(ADMIN, MEMBER), "select user_id from survey_group where id=%d"),
    PERSONA(false, List.of(ADMIN, MEMBER), "select user_id from customer_persona where id=%d"),
    PERSONA_GROUP(false, List.of(ADMIN, MEMBER), "select user_id from customer_persona_group where id=%d"),
    EVENT(false, List.of(ADMIN), null),
    DASHBOARD(false, List.of(ADMIN), "select user_id from bi_dashboard where id=%d"),
    EVENT_GROUP(false, List.of(ADMIN, OWNER, MEMBER), "select user_id from event_result_group where id=%d"),
    SEND(false, List.of(ADMIN), "select user_id from send_manage where id=%d"),
    SEND_GROUP(false, List.of(ADMIN, MEMBER), "select user_id from send_group where id=%d"),
    ;

    private final boolean checkRelation;
    private final String getOwnerSql;
    private final List<ResourcePermissionRelationType> relations;  // 第一个为默认的数据权限，兼容之前的数据关系，数据库中已经有null了

    ResourcePermissionType(boolean checkRelation, List<ResourcePermissionRelationType> relations, String getOwnerSql) {
        this.checkRelation = checkRelation;
        this.relations = relations;
        this.getOwnerSql = getOwnerSql;
    }

    public String buildOwnerSql(Long resourceId) {
        if (StringUtils.isNotEmpty(getOwnerSql)) {
            return String.format(getOwnerSql, resourceId);
        }
        return null;
    }

    public String emptyIsDefault(String relationType) {
        return emptyRelationIsDefault(relationType).name();
    }

    public ResourcePermissionRelationType emptyRelationIsDefault(String relationType) {
        ResourcePermissionRelationType relation = ResourcePermissionRelationType.parse(relationType);
        if (relations.contains(relation)) {
            return relation;
        } else {
            return relations.get(0);
        }
    }
}
