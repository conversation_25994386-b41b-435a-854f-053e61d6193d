package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Role;

import java.util.zip.Adler32;

/**
 * <AUTHOR>
 */

@Getter
public enum RoleType {
    // editable: 0 不可以 1 可以编辑删除 2 只能编辑
    SUPER_ADMIN(1, "超级管理员", "系统默认角色，拥有所有权限，不可更改权限范围", "cem", 0),
    COSTUMER_MANAGER(2, "客户管理", "客户中心管理员，拥有查看和编辑层级所有客户权限", "cem", 0),
    MEMBER(3, "成员", "邀请子账户时的默认角色", "cem", 2),
    OTHER(4, "", "", "cem", 1), // 企业用户自建的角色
    ;

    private final int type;
    private final String name;
    private final String description;
    private final String platform;
    private final int editable;

    RoleType(int type, String name, String description, String platform, int editable) {
        this.type = type;
        this.name = name;
        this.description = description;
        this.platform = platform;
        this.editable = editable;
    }

    public Role createRole(Long orgId) {
        Role role = new Role();
        role.setOrgId(orgId);
        role.setName(name);
        role.setDescription(description);
        role.setPlatform(platform);
        role.setEditable(editable);
        role.setType(type);
        int currentTime = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        role.setCreated(currentTime);
        role.setUpdated(currentTime);
        return role;
    }

    public static RoleType parseRoleType(Role role) {
        if (role != null && StringUtils.isEmpty(role.getName())) {
            if (role.getName().equals(SUPER_ADMIN.getName())) {
                return SUPER_ADMIN;
            } else if (role.getName().equals(MEMBER.getName())) {
                return MEMBER;
            }
        }
        return RoleType.OTHER;
    }

    /**
     * 判断是否可以修改角色
     */
    public static boolean canUpdate(Role role) {
        if (role != null) {
            if (role.getEditable() == null || role.getType() == null) { // 旧数据可能这些有null,添加一些额外的处理
                RoleType roleType = parseRoleType(role);
                return roleType != SUPER_ADMIN;
            } else {
                return role.getEditable() != SUPER_ADMIN.editable;
            }
        }
        return false;
    }

    public static boolean canDelete(Role role) {
        if (role != null) {
            if (role.getEditable() == null || role.getType() == null) { // 旧数据可能这些有null,添加一些额外的处理
                RoleType roleType = parseRoleType(role);
                return roleType != SUPER_ADMIN && roleType != MEMBER;
            } else {
                return role.getEditable() != SUPER_ADMIN.editable && role.getEditable() != MEMBER.editable;
            }
        }
        return false;
    }
}
