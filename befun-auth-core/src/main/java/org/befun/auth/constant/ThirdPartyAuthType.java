package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.dto.auth.*;
import org.befun.auth.dto.auth.oauth.OauthAuthDto;
import org.befun.auth.service.auth.config.*;

import java.util.*;
import java.util.stream.Collectors;

@Getter
public enum ThirdPartyAuthType {

    CAS("cas", "cas", "-secondary", CasConfig.class, CasAuthDto.class),
    WECHAT_WORK("wechatWork", "wechat-work", null, WechatWorkConfig.class, WechatWorkAuthDto.class),
    BAIDU_TONGJI("baiduTongji", "baidu-tongji", null, BaiduTongjiConfig.class, BaiduTongjiAuthDto.class),
    EMAIL_SENDER("emailSender", "email-sender", null, EmailSenderConfig.class, EmailSenderAuthDto.class),
    YOUZAN("youzan", "youzan", "youzan", YouzanConfig.class, YouzanAuthDto.class),
    WECHAT_OPEN(false, "wechatOpen", "wechat-open", null, WechatOpenConfig.class, WechatOpenAuthDto.class),
    SHEN_CE("shenCe", "shen-ce", null, ShenCeConfig.class, ShenCeAuthDto.class),
    OAUTH("oauth", "oauth", null, OauthConfig.class, OauthAuthDto.class),
    SAML("saml", "saml", null, SamlConfig.class, SamlAuthDto.class),
    ;

    private final boolean singleType;
    private final String camelName;
    private final String simpleName;
    private final String sourcePrefix;
    private final String sourceSuffix;
    private final Class<?> configClass;
    private final Class<?> dtoClass;

    private final static Map<String, ThirdPartyAuthType> nameMap;
    private final static Map<String, ThirdPartyAuthType> camelNameMap;

    ThirdPartyAuthType(String camelName, String simpleName, String sourceSuffix, Class<?> configClass, Class<?> dtoClass) {
        this(true, camelName, simpleName, sourceSuffix, configClass, dtoClass);
    }

    ThirdPartyAuthType(boolean singleType, String camelName, String simpleName, String sourceSuffix, Class<?> configClass, Class<?> dtoClass) {
        this.singleType = singleType;
        this.camelName = camelName;
        this.simpleName = simpleName;
        this.sourcePrefix = simpleName + "-";
        this.sourceSuffix = sourceSuffix;
        this.configClass = configClass;
        this.dtoClass = dtoClass;
    }

    static {
        nameMap = new HashMap<>();
        camelNameMap = new HashMap<>();
        Arrays.stream(values()).forEach(i -> nameMap.put(i.name(), i));
        Arrays.stream(values()).forEach(i -> camelNameMap.put(i.getCamelName(), i));
    }

    public String buildSource(Long orgId) {
        return sourcePrefix + orgId;
    }

    public static List<ThirdPartyAuthType> parseByNames(String types) {
        if (StringUtils.isEmpty(types)) {
            return List.of();
        }
        return Arrays.stream(types.split(","))
                .map(n -> Optional.ofNullable(nameMap.get(n)).orElse(camelNameMap.get(n)))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
