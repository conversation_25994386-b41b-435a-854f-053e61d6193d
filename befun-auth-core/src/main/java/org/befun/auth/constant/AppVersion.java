package org.befun.auth.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.dto.OrganizationOptionalLimitDto;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

@Getter
public enum AppVersion {
    // 不开放使用
    EMPTY("empty", "", new OrganizationOptionalLimitDto(
            0,
            0,
            0,
            0,
            0,
            0,
            false,
            false,
            false)),
    // 免费版
    FREE("free", "免费版", new OrganizationOptionalLimitDto(
            1,
            1,
            1000000,
            0,
            1,
            0,
            false,
            false,
            false)),
    //   基础版
    BASE("base", "基础版", new OrganizationOptionalLimitDto(
            1,
            10,
            1000000,
            0,
            100000,
            0,
            false,
            false,
            false)),
    // 专业版
    UPDATE("update", "专业版", new OrganizationOptionalLimitDto(
            5,
            10,
            1000000,
            5,
            1000000,
            10,
            false,
            false,
            false)),
    // 旗舰版
    PROFESSION("profession", "旗舰版", new OrganizationOptionalLimitDto(
            100,
            1000000,
            1000000,
            1000000,
            1000000,
            1000000,
            true,
            true,
            true));

    private final String text;
    private final String label;
    private final OrganizationOptionalLimitDto optionalLimit;

    AppVersion(String text, String label, OrganizationOptionalLimitDto optionalLimit) {
        this.text = text;
        this.label = label;
        this.optionalLimit = optionalLimit;
    }

    public AppVersionPermissions getPermissions() {
        switch (this) {
            case FREE -> {
                return AppVersionPermissions.SUPER_ADMIN_FREE;
            }
            case BASE -> {
                return AppVersionPermissions.SUPER_ADMIN_BASE;
            }
            case UPDATE -> {
                return AppVersionPermissions.SUPER_ADMIN_UPDATE;
            }
            case PROFESSION -> {
                return AppVersionPermissions.SUPER_ADMIN_PROFESSION;
            }
            default -> {
                return AppVersionPermissions.EMPTY;
            }
        }
    }

    public static AppVersion parseByText(String s) {
        return StringUtils.isEmpty(s) ? EMPTY : Arrays.stream(values()).filter(i -> i.text.equals(s)).findFirst().orElse(EMPTY);
    }
}
