package org.befun.auth.event;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.AuthBindType;
import org.befun.auth.dto.ThirdPartyUserInfoDto;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class AuthBindEvent {
    /* app */
    private String app;

    /* source */
    private String source;

    /* user id */
    private Long userId;

    /* third party openId */
    private String openId;

    /* bind type */
    private AuthBindType bindType;

    private String eventKey;

    /* userInfo */
    private ThirdPartyUserInfoDto userInfoDto;
}
