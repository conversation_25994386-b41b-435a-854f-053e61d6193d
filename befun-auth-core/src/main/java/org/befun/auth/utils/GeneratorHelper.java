package org.befun.auth.utils;

import org.apache.commons.lang3.RandomStringUtils;
import org.befun.auth.constant.GenerateCodeType;
import org.befun.core.generator.SysConvert;
import org.befun.core.utils.DateHelper;

import java.time.LocalDateTime;
import java.util.Date;

public class GeneratorHelper {

    public static String generatorApiKey() {
        return RandomStringUtils.random(18, true, true);
    }

    public static String generatorApiSecret() {
        return RandomStringUtils.random(32, true, true);
    }

    public static String generatorSurveyAesKey() {
        return RandomStringUtils.random(32, true, true);
    }

    public static String generatorPayNo() {
        String ms = DateHelper.format(LocalDateTime.now(), DateHelper.DATE_TIME_FORMATTER2);
        return ms + RandomStringUtils.random(18, true, true);
    }

    public static String generatorPayNo(Long id) {
        String ms = DateHelper.format(LocalDateTime.now(), DateHelper.DATE_TIME_FORMATTER2);
        return ms + SysConvert.toX(id);
    }

    public static String generatorByTimeAndId(Long id) {
        String ms = DateHelper.format(LocalDateTime.now(), DateHelper.DATE_TIME_FORMATTER2);
        return ms + SysConvert.toX(id);
    }

    public static String generatorVerifyCode(int count, GenerateCodeType type) {
        boolean number = false;
        boolean character = false;

        switch (type) {
            case CHARACTER:
                character = true;
                break;
            case NUMBER_AND_CHARACTER:
                character = true;
                number = true;
                break;
            default:
                number = true;
        }
        return RandomStringUtils.random(count, character, number);
    }

    public static String generatorNumberWithCharacter(int count) {
        return RandomStringUtils.randomAlphanumeric(count);
    }

    public static String generatorMD5Timestamp16Or32(int count) {
        Date date = new Date();
        return StringHelper.toMD5(String.valueOf(date.getTime()), count);
    }
}
