package org.befun.auth.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * Excel解析工具类
 * 提供通用的Excel文件解析功能，支持解析头部信息和数据行
 */
@Slf4j
public class ExcelParserHelper {

    /**
     * 解析Excel文件，返回头部信息和数据行
     *
     * @param file 上传的Excel文件
     * @return ExcelParseResult 包含头部和数据的解析结果
     * @throws IOException 文件读取异常
     */
    public static ExcelParseResult parseExcel(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Excel文件不能为空");
        }

        // 检查文件类型
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName) ||
            (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        try (InputStream inputStream = file.getInputStream()) {
            // 设置ZIP安全文件的最小压缩比，防止ZIP炸弹攻击
            ZipSecureFile.setMinInflateRatio(0);

            ExcelDataListener listener = new ExcelDataListener();
            EasyExcel.read(inputStream, listener).sheet().doRead();

            return listener.getResult();
        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", fileName, e);
            throw new IOException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析Excel文件，指定从第几行开始作为数据行
     *
     * @param file 上传的Excel文件
     * @param headerRowIndex 头部行索引（从0开始）
     * @return ExcelParseResult 包含头部和数据的解析结果
     * @throws IOException 文件读取异常
     */
    public static ExcelParseResult parseExcel(MultipartFile file, int headerRowIndex) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Excel文件不能为空");
        }

        if (headerRowIndex < 0) {
            throw new IllegalArgumentException("头部行索引不能小于0");
        }

        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName) ||
            (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        try (InputStream inputStream = file.getInputStream()) {
            ZipSecureFile.setMinInflateRatio(0);

            ExcelDataListener listener = new ExcelDataListener(headerRowIndex);
            EasyExcel.read(inputStream, listener).sheet().doRead();

            return listener.getResult();
        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", fileName, e);
            throw new IOException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析Excel文件，返回指定类型的对象列表
     *
     * @param file 上传的Excel文件
     * @param clazz 目标类型，需要使用EasyExcel注解
     * @param <T> 泛型类型
     * @return 解析后的对象列表
     * @throws IOException 文件读取异常
     */
    public static <T> List<T> parseExcel(MultipartFile file, Class<T> clazz) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Excel文件不能为空");
        }

        if (clazz == null) {
            throw new IllegalArgumentException("目标类型不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName) ||
            (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        try (InputStream inputStream = file.getInputStream()) {
            ZipSecureFile.setMinInflateRatio(0);

            List<T> result = EasyExcel.read(inputStream)
                    .head(clazz)
                    .sheet()
                    .doReadSync();

            log.info("成功解析Excel文件: {}, 共{}行数据", fileName, result.size());
            return result;
        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", fileName, e);
            throw new IOException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * Excel解析结果类
     */
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExcelParseResult {
        /**
         * 头部信息（列名）
         */
        private List<String> headers;

        /**
         * 数据行，每行是一个Map，key为列索引或列名，value为单元格值
         */
        private List<Map<String, Object>> dataRows;

        /**
         * 原始数据行，每行是一个List，按列顺序存储
         */
        private List<List<Object>> rawDataRows;

        /**
         * 总行数（包括头部）
         */
        private int totalRows;

        /**
         * 数据行数（不包括头部）
         */
        private int dataRowCount;

        /**
         * 列数
         */
        private int columnCount;

        /**
         * 是否解析成功
         */
        private boolean success;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 构造成功的解析结果
         */
        public static ExcelParseResult success(List<String> headers, List<Map<String, Object>> dataRows,
                                             List<List<Object>> rawDataRows) {
            ExcelParseResult result = new ExcelParseResult();
            result.setHeaders(headers);
            result.setDataRows(dataRows);
            result.setRawDataRows(rawDataRows);
            result.setTotalRows(dataRows.size() + (CollectionUtils.isNotEmpty(headers) ? 1 : 0));
            result.setDataRowCount(dataRows.size());
            result.setColumnCount(CollectionUtils.isNotEmpty(headers) ? headers.size() : 0);
            result.setSuccess(true);
            return result;
        }

        /**
         * 构造失败的解析结果
         */
        public static ExcelParseResult failure(String errorMessage) {
            ExcelParseResult result = new ExcelParseResult();
            result.setSuccess(false);
            result.setErrorMessage(errorMessage);
            result.setHeaders(new ArrayList<>());
            result.setDataRows(new ArrayList<>());
            result.setRawDataRows(new ArrayList<>());
            return result;
        }
    }

    /**
     * Excel数据监听器
     */
    private static class ExcelDataListener extends AnalysisEventListener<Map<Integer, String>> {
        private final List<String> headers = new ArrayList<>();
        private final List<Map<String, Object>> dataRows = new ArrayList<>();
        private final List<List<Object>> rawDataRows = new ArrayList<>();
        private final int headerRowIndex;
        private int currentRowIndex = 0;
        private boolean headerParsed = false;

        public ExcelDataListener() {
            this.headerRowIndex = 0; // 默认第一行为头部
        }

        public ExcelDataListener(int headerRowIndex) {
            this.headerRowIndex = headerRowIndex;
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            currentRowIndex = context.readRowHolder().getRowIndex();

            if (currentRowIndex == headerRowIndex && !headerParsed) {
                // 解析头部
                parseHeaders(data);
                headerParsed = true;
            } else if (currentRowIndex > headerRowIndex) {
                // 解析数据行
                parseDataRow(data);
            }
        }

        private void parseHeaders(Map<Integer, String> data) {
            headers.clear();
            if (data != null && !data.isEmpty()) {
                int maxColumnIndex = Collections.max(data.keySet());
                for (int i = 0; i <= maxColumnIndex; i++) {
                    String headerValue = data.get(i);
                    headers.add(StringUtils.isNotEmpty(headerValue) ? headerValue.trim() : "列" + (i + 1));
                }
            }
            log.debug("解析到Excel头部: {}", headers);
        }

        private void parseDataRow(Map<Integer, String> data) {
            if (data == null || data.isEmpty()) {
                return;
            }

            // 构建原始数据行
            List<Object> rawRow = new ArrayList<>();
            Map<String, Object> dataRow = new HashMap<>();

            int maxColumnIndex = Math.max(
                headers.size() - 1,
                data.isEmpty() ? 0 : Collections.max(data.keySet())
            );

            for (int i = 0; i <= maxColumnIndex; i++) {
                String cellValue = data.get(i);
                Object value = StringUtils.isNotEmpty(cellValue) ? cellValue.trim() : null;

                rawRow.add(value);

                // 使用头部名称作为key，如果没有头部则使用列索引
                String key = i < headers.size() ? headers.get(i) : "列" + (i + 1);
                dataRow.put(key, value);

                // 同时也提供列索引作为key
                dataRow.put(String.valueOf(i), value);
            }

            rawDataRows.add(rawRow);
            dataRows.add(dataRow);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            log.info("Excel解析完成，头部列数: {}, 数据行数: {}", headers.size(), dataRows.size());
        }

        public ExcelParseResult getResult() {
            return ExcelParseResult.success(headers, dataRows, rawDataRows);
        }
    }
}
