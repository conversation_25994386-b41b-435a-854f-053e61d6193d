package org.befun.auth.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.dto.LoginPasswordRsaPubKeyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Slf4j
@Service
public class PasswordHelper {

    @Autowired
    private AuthProperties authProperties;

    public LoginPasswordRsaPubKeyDto pubKey() {
        return new LoginPasswordRsaPubKeyDto(authProperties.getRsaPublicKey(), authProperties.getRsaModPadding());
    }

    /**
     * 解密前端传入的已加密的登录密码
     */
    public String decryptRsa(String encryptedPassword) {
        return EncryptDecryptUtils.decryptRsa(authProperties.getRsaPrivateKey(), authProperties.getRsaModPadding(), encryptedPassword);
    }

    public static void main(String[] args) {
        AuthProperties authProperties = new AuthProperties();
        String encryptedPassword = EncryptDecryptUtils.encryptRsa(authProperties.getRsaPublicKey(), authProperties.getRsaModPadding(), "Survey+0627");
        System.out.println(encryptedPassword);
        System.out.println(EncryptDecryptUtils.decryptRsa(authProperties.getRsaPrivateKey(), authProperties.getRsaModPadding(), encryptedPassword));
    }

    /**
     * 加密密码
     *
     * @param password 原始密码
     */
    public static String encrypt(String password) {
        if (StringUtils.isEmpty(password)) {
            return null;
        }
        return BCrypt.hashpw(password, BCrypt.gensalt());
    }

    /**
     * 验证密码
     *
     * @param password 原始密码
     * @param encrypt  加密后密码
     * @return 是否匹配
     */
    public static boolean verify(String password, String encrypt) {
        if (StringUtils.isEmpty(password) || StringUtils.isEmpty(encrypt)) {
            return false;
        }
        return BCrypt.checkpw(password, encrypt);
    }

    /**
     * 计算密码强度
     *
     * @param password 原始密码
     */
    public static int passwordStrength(String password) {
        if (StringUtils.isEmpty(password)) {
            return 0;
        }
        int countNumber = 0;
        int countLowercaseLetter = 0;
        int countUppercaseLetter = 0;
        int countSymbol = 0;
        char[] chars = password.toCharArray();
        for (char c : chars) {
            if (isNumber(c)) {
                countNumber++;
            } else if (isLowercaseLetter(c)) {
                countLowercaseLetter++;
            } else if (isUppercaseLetter(c)) {
                countUppercaseLetter++;
            } else {
                countSymbol++;
            }
        }
        int lengthScore = chars.length > 10 ? 2 : 0;
        return lengthScore + countToScore(countNumber) + countToScore(countLowercaseLetter) + countToScore(countUppercaseLetter) + countToScore(countSymbol);
    }

    private static int countToScore(int count) {
        return count > 2 ? 2 : count > 1 ? 1 : 0;
    }

    private static boolean isNumber(char c) {
        return c >= 48 && c < 57;
    }

    private static boolean isLowercaseLetter(char c) {
        return c >= 97 && c <= 122;
    }

    private static boolean isUppercaseLetter(char c) {
        return c >= 65 && c <= 90;
    }
}
