package org.befun.auth.utils;

import cn.hutool.crypto.KeyUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.befun.auth.configuration.AuthProperties;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;

@Slf4j
public class EncryptDecryptUtils {

    public static final String aesModPadding = "AES/CBC/PKCS7Padding";

    /**
     * aes加密
     *
     * @param keyBase64 base64 转换过的秘钥
     * @param content   明文
     */
    public static String encryptAes(String keyBase64, String aesModePadding, String content) {
        try {
            byte[] byteEncode = encryptAes0(keyBase64, aesModePadding, content);
            return Base64.getEncoder().encodeToString(byteEncode);
        } catch (Exception e) {
            log.error("AES加密失败,明文：{}", content, e);
            return null;
        }
    }

    /**
     * aes加密
     *
     * @param keyBase64 base64 转换过的秘钥
     * @param content   明文
     */
    public static String encryptAesByBase64Url(String keyBase64, String aesModePadding, String content) {
        try {
            byte[] byteEncode = encryptAes0(keyBase64, aesModePadding, content);
            return Base64.getUrlEncoder().encodeToString(byteEncode);
        } catch (Exception e) {
            log.error("AES加密失败,明文：{}", content, e);
            return null;
        }
    }

    /**
     * aes加密
     *
     * @param keyBase64 base64 转换过的秘钥
     * @param content   明文
     */
    private static byte[] encryptAes0(String keyBase64, String aesModePadding, String content) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        Security.addProvider(new BouncyCastleProvider());
        byte[] byteKey = Base64.getDecoder().decode(keyBase64.getBytes(StandardCharsets.UTF_8));
        SecretKey key = new SecretKeySpec(byteKey, "AES");
        Cipher cipher = Cipher.getInstance(aesModePadding);
        IvParameterSpec iv = new IvParameterSpec(Arrays.copyOf(byteKey, 16));
        cipher.init(Cipher.ENCRYPT_MODE, key, iv);
        byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
        return cipher.doFinal(byteContent);
    }


    /**
     * aes解密
     */
    public static String decryptAes(String keyBase64, String aesModePadding, String base64Content) {
        try {
            byte[] byteContent = Base64.getDecoder().decode(base64Content);
            return decryptAes0(keyBase64, aesModePadding, byteContent);
        } catch (Exception e) {
            log.error("AES解密失败,密文：{}", base64Content, e);
            return null;
        }
    }

    /**
     * aes解密
     */
    public static String decryptAesByBase64Url(String keyBase64, String aesModePadding, String base64Content) {
        try {
            byte[] byteContent = Base64.getUrlDecoder().decode(base64Content);
            return decryptAes0(keyBase64, aesModePadding, byteContent);
        } catch (Exception e) {
            log.error("AES解密失败,密文：{}", base64Content, e);
            return null;
        }
    }

    /**
     * aes解密
     */
    private static String decryptAes0(String keyBase64, String aesModePadding, byte[] byteContent) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        Security.addProvider(new BouncyCastleProvider());
        byte[] byteKey = Base64.getDecoder().decode(keyBase64.getBytes(StandardCharsets.UTF_8));
        SecretKey key = new SecretKeySpec(byteKey, "AES");
        Cipher cipher = Cipher.getInstance(aesModePadding);
        IvParameterSpec iv = new IvParameterSpec(Arrays.copyOf(byteKey, 16));
        cipher.init(Cipher.DECRYPT_MODE, key, iv);
        byte[] byteDecode = cipher.doFinal(byteContent);
        return new String(byteDecode, StandardCharsets.UTF_8);
    }

    /**
     * rsa解密
     */
    public static String decryptRsa(String privateKeyBase64, String rsaModePadding, String content) {
        try {
            privateKeyBase64 = privateKeyBase64.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");
            KeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyBase64));
            PrivateKey privateKey = KeyFactory.getInstance("RSA").generatePrivate(keySpec);
            Cipher cipher = Cipher.getInstance(rsaModePadding);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] bytes = cipher.doFinal(Base64.getDecoder().decode(content));
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Throwable e) {
            log.error("RSA解密失败,密文：{}", content, e);
            return null;
        }
    }

    /**
     * rsa加密
     */
    public static String encryptRsa(String publicKeyBase64, String rsaModePadding, String content) {
        try {
            publicKeyBase64 = publicKeyBase64.replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll("\\s+", "");
            KeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKeyBase64));
            PublicKey publicKey = KeyFactory.getInstance("RSA").generatePublic(keySpec);
            Cipher cipher = Cipher.getInstance(rsaModePadding);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] bytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Throwable e) {
            log.error("RSA加密失败,明文：{}", content, e);
            return null;
        }
    }


    /**
     * sm2解密
     */
    public static String decryptSm2(String privateKeyBase64, String content) {
        try {
            SM2 sm2 = SmUtil.sm2(privateKeyBase64, null);
            byte[] bytes = sm2.decrypt(content, KeyType.PrivateKey);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Throwable e) {
            log.error("SM2解密失败,密文：{}", content, e);
            return null;
        }
    }

    /**
     * sm2加密
     */
    public static String encryptSm2(String publicKeyBase64, String content) {
        try {
            SM2 sm2 = SmUtil.sm2(null, publicKeyBase64);
            return sm2.encryptBase64(content, KeyType.PublicKey);
        } catch (Throwable e) {
            log.error("SM2加密失败,明文：{}", content, e);
            return null;
        }
    }

    /**
     * sm4加密
     */
    public static String encryptSm4(String keyBase64, String content) {
        try {
            SymmetricCrypto sm4 = SmUtil.sm4(Base64.getDecoder().decode(keyBase64));
            return sm4.encryptBase64(content);
        } catch (Throwable e) {
            log.error("SM4加密失败,明文：{}", content, e);
            return null;
        }
    }

    /**
     * sm4解密
     */
    public static String decryptSm4(String keyBase64, String content) {
        try {
            SymmetricCrypto sm4 = SmUtil.sm4(Base64.getDecoder().decode(keyBase64));
            return sm4.decryptStr(content);
        } catch (Throwable e) {
            log.error("SM4加密失败,明文：{}", content, e);
            return null;
        }
    }


    public static String generatorAesKey() {
        try {
            KeyGenerator keygen = KeyGenerator.getInstance("AES");
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed("dst-aes".getBytes());
            keygen.init(128, random);
            SecretKey key = keygen.generateKey();
            return new String(Base64.getEncoder().encode(key.getEncoded()), StandardCharsets.UTF_8);
        } catch (Throwable e) {

        }
        return null;
    }

    public static Pair<String, String> generatorSm2Key() {
        try {
            KeyPair keyPair = KeyUtil.generateKeyPair("SM2");
            return Pair.create(
                    new String(Base64.getEncoder().encode(keyPair.getPrivate().getEncoded()), StandardCharsets.UTF_8),
                    new String(Base64.getEncoder().encode(keyPair.getPublic().getEncoded()), StandardCharsets.UTF_8)
            );
        } catch (Throwable e) {

        }
        return null;
    }

    public static void main(String[] args) {
        AuthProperties authProperties = new AuthProperties();
        String encryptedPassword = encryptRsa(authProperties.getRsaPublicKey(), authProperties.getRsaModPadding(), "Survey+0627");
        System.out.println("rsa 加密： " + encryptedPassword);
        System.out.println("rsa 解密： " + decryptRsa(authProperties.getRsaPrivateKey(), authProperties.getRsaModPadding(), encryptedPassword));
        String aesKey = generatorAesKey();
        System.out.println("对称秘钥： " + aesKey);
        encryptedPassword = encryptRsa(authProperties.getRsaPublicKey(), authProperties.getRsaModPadding(), aesKey);
        System.out.println("rsa 加密对称秘钥： " + encryptedPassword);
        String content = "{\"clientId\":\"4f40f723-490c-96c2-43273629bd34\",\"data\":{\"SS7vL\":4,\"NFEqu\":\"T95Ru\"},\"responseId\":3698253328707584,\"isCompleted\":false,\"isEarlyCompleted\":false}";
        String encryptedContent = encryptAes(aesKey, authProperties.getAesModPadding(), content);
        System.out.println("aes 加密： " + encryptedContent);
        String decryptedContent = decryptAes(aesKey, authProperties.getAesModPadding(), encryptedContent);
        System.out.println("aes 解密： " + decryptedContent);


        System.out.println("sm2 私钥： " + authProperties.getSm2PrivateKey());
        System.out.println("sm2 公钥： " + authProperties.getSm2PublicKey());
        String sm2EncryptedKey = encryptSm2(authProperties.getSm2PublicKey(), aesKey);
        System.out.println("sm2 加密对称秘钥： " + sm2EncryptedKey);
        System.out.println("sm2 解密对称秘钥： " + decryptSm2(authProperties.getSm2PrivateKey(), sm2EncryptedKey));
        String sm4EncryptedKey = encryptSm4(aesKey, content);
        System.out.println("sm4 加密： " + sm4EncryptedKey);
        System.out.println("sm4 解密： " + decryptSm4(aesKey, sm4EncryptedKey));
    }

}
