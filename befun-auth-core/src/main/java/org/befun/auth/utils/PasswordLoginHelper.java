package org.befun.auth.utils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.LoginPlatform;
import org.befun.auth.constant.OrganizationConfigMfaType;
import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.LoginPasswordMfaCacheDto;
import org.befun.auth.dto.LoginPasswordMfaDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.dto.userconfig.UserConfigDto;
import org.befun.auth.entity.User;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.UserConfigService;
import org.befun.core.constant.ErrorInternalCode;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.GraphCaptchaVerifyDto;
import org.befun.extension.service.GraphCaptchaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.auth.constant.OrganizationConfigMfaType.NONE;
import static org.befun.auth.constant.OrganizationConfigType.mfa;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

@Component
public class PasswordLoginHelper {

    private static final String KEY_TIMES = "user.login.times:%d:%s";
    private static final String KEY_MFA = "user.login.mfa:%s";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private UserConfigService userConfigService;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    protected GraphCaptchaService graphCaptchaService;

    //**********************************************************************************************
    //**************************************   图形验证码   ********************************************
    //**********************************************************************************************
    public void checkGraphCaptcha(GraphCaptchaVerifyDto captcha, int failTimes) {
        int graphCaptchaInFailTimes = authProperties.getGraphCaptchaInFailTimes();
        if (graphCaptchaInFailTimes > 0 && failTimes >= graphCaptchaInFailTimes) {
            if (captcha == null) {
                throw badRequestException(GRAPH_CAPTCHA_REQUIRED);
            } else if (!graphCaptchaService.check(captcha)) {
                throw badRequestException(GRAPH_CAPTCHA_ERROR);
            }
        }
    }

    public void clearGraphCaptcha(GraphCaptchaVerifyDto captcha) {
        if (captcha != null) {
            graphCaptchaService.clear(captcha);
        }
    }

    //**********************************************************************************************
    //**************************************   登录次数   ********************************************
    //**********************************************************************************************
    public int checkFailTimes(User user) {
        int times = 0;
        if (user != null) {
            times = failTimes(user.getId());
            if (times >= 5) {
                throw badRequestException(LOGIN_PASSWORD_ERROR_LOCK);
            }
        }
        return times;
    }

    public void incrementFailTimes(User user, AuthToastMessage message) {
        if (user != null) {
            int graphCaptchaInFailTimes = authProperties.getGraphCaptchaInFailTimes();
            int times = incrementAndGetFailTimes(user.getId());
            if (times < 5) {
                if (graphCaptchaInFailTimes > 0 && times >= graphCaptchaInFailTimes) {
                    throw badRequestException(ErrorInternalCode.GRAPH_CAPTCHA_REQUIRED, message, 5 - times);
                }
                throw badRequestException(message, 5 - times);
            } else {
                expireFailTimes(user.getId());
                throw badRequestException(LOGIN_PASSWORD_ERROR_LOCK);
            }
        }
    }

    public int failTimes(Long userId) {
        String value = stringRedisTemplate.opsForValue().get(keyTimes(userId));
        return NumberUtils.isDigits(value) ? Integer.parseInt(value) : 0;
    }

    public int incrementAndGetFailTimes(Long userId) {
        String key = keyTimes(userId);
        Long value = stringRedisTemplate.opsForValue().increment(key);
        stringRedisTemplate.expire(key, Duration.ofHours(24));
        return value == null ? 1 : value.intValue();
    }

    public void expireFailTimes(Long userId) {
        String key = keyTimes(userId);
        stringRedisTemplate.expire(key, Duration.ofMinutes(15));
    }

    public void clearFailTimes(Long userId) {
        String key = keyTimes(userId);
        stringRedisTemplate.delete(key);
    }

    private String keyTimes(Long userId) {
        return String.format(KEY_TIMES, userId, LocalDate.now());
    }

    //**********************************************************************************************
    //*****************************************   mfa   ********************************************
    //**********************************************************************************************

    public OrganizationConfigMfaType getOrgMfaType(Long orgId) {
        return organizationConfigService.getConfigInfo(orgId, mfa, OrgConfigDto::getMfa, NONE);
    }

    public LocalDateTime getUserMfaInfo(Long orgId, Long userId) {
        String mfa = userConfigService.getConfigInfo(orgId, userId, UserConfigType.mfa, UserConfigDto::getMfa, null);
        return Optional.ofNullable(mfa).map(DateHelper::parseAdjust).orElse(null);
    }

    public void markLastLoginTimeByMfa(Long orgId, Long userId) {
        UserConfigDto configDto = new UserConfigDto();
        configDto.setMfa(DateHelper.formatDateTime(new Date()));
        userConfigService.saveOrUpdateSingleConfig(orgId, userId, UserConfigType.mfa, configDto);
    }

    public void cacheMfaParams(LoginPasswordMfaDto dto, LoginPlatform platform, User user) {
        Map<String, String> mfaCacheData = new HashMap<>();
        mfaCacheData.put("orgId", user.getOrgId().toString());
        mfaCacheData.put("userId", user.getId().toString());
        mfaCacheData.put("mobile", user.getMobile());
        mfaCacheData.put("email", user.getEmail());
        mfaCacheData.put("mfaVerifyType", dto.getMfaVerifyType());
        mfaCacheData.put("platform", platform.name());

        String key = keyMfa(dto.getMfaToken());
        stringRedisTemplate.opsForHash().putAll(key, mfaCacheData);
        stringRedisTemplate.expire(key, Duration.ofMinutes(15));
    }

    public LoginPasswordMfaCacheDto getMfaLoginCacheInfo(String mfaToken) {
        String key = keyMfa(mfaToken);
        HashOperations<String, String, String> hashOperations = stringRedisTemplate.opsForHash();
        Map<String, String> values = hashOperations.entries(key);
        if (MapUtils.isNotEmpty(values)) {
            return JsonHelper.toObject(values, LoginPasswordMfaCacheDto.class);
        }
        return null;
    }

    public String getMfaLoginAccount(String mfaToken) {
        LoginPasswordMfaCacheDto dto = getMfaLoginCacheInfo(mfaToken);
        if (dto != null && StringUtils.isNotEmpty(dto.getMfaVerifyType())) {
            if ("email".equals(dto.getMfaVerifyType())) {
                return dto.getEmail();
            } else if ("mobile".equals(dto.getMfaVerifyType())) {
                return dto.getMobile();
            }
        }
        return null;
    }

    public String keyMfa(String mfaToken) {
        return String.format(KEY_MFA, mfaToken);
    }

    public void clearMfaParams(String mfaToken) {
        stringRedisTemplate.delete(keyMfa(mfaToken));
    }

}
