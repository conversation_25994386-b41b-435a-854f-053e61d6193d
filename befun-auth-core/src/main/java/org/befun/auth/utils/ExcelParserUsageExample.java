package org.befun.auth.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * ExcelParserHelper使用示例
 * 展示如何使用ExcelParserHelper解析Excel文件
 */
@Slf4j
public class ExcelParserUsageExample {

    /**
     * 示例1：基本解析Excel文件
     * 默认第一行为头部，其余行为数据
     */
    public void basicParseExample(MultipartFile file) {
        try {
            ExcelParserHelper.ExcelParseResult result = ExcelParserHelper.parseExcel(file);
            
            if (result.isSuccess()) {
                log.info("解析成功！");
                log.info("头部信息: {}", result.getHeaders());
                log.info("数据行数: {}", result.getDataRowCount());
                log.info("列数: {}", result.getColumnCount());
                
                // 遍历数据行
                for (int i = 0; i < result.getDataRows().size(); i++) {
                    Map<String, Object> row = result.getDataRows().get(i);
                    log.info("第{}行数据: {}", i + 1, row);
                }
            } else {
                log.error("解析失败: {}", result.getErrorMessage());
            }
        } catch (IOException e) {
            log.error("文件读取失败", e);
        }
    }

    /**
     * 示例2：指定头部行索引解析
     * 当Excel文件的头部不在第一行时使用
     */
    public void parseWithHeaderRowExample(MultipartFile file) {
        try {
            // 假设头部在第3行（索引为2）
            int headerRowIndex = 2;
            ExcelParserHelper.ExcelParseResult result = ExcelParserHelper.parseExcel(file, headerRowIndex);
            
            if (result.isSuccess()) {
                log.info("指定头部行解析成功！");
                log.info("头部信息: {}", result.getHeaders());
                
                // 按列名访问数据
                for (Map<String, Object> row : result.getDataRows()) {
                    for (String header : result.getHeaders()) {
                        Object value = row.get(header);
                        log.info("列[{}]的值: {}", header, value);
                    }
                }
            }
        } catch (IOException e) {
            log.error("解析失败", e);
        }
    }

    /**
     * 示例3：解析为指定类型的对象列表
     * 需要目标类使用EasyExcel注解
     */
    public void parseToObjectExample(MultipartFile file) {
        try {
            // 假设有一个UserImportDto类，使用了@ExcelProperty注解
            // List<UserImportDto> users = ExcelParserHelper.parseExcel(file, UserImportDto.class);
            // log.info("解析到{}个用户", users.size());
            
            log.info("此示例需要具体的DTO类，请参考DepartmentImportDto的实现");
        } catch (Exception e) {
            log.error("解析失败", e);
        }
    }

    /**
     * 示例4：处理解析结果的不同方式
     */
    public void processResultExample(MultipartFile file) {
        try {
            ExcelParserHelper.ExcelParseResult result = ExcelParserHelper.parseExcel(file);
            
            if (!result.isSuccess()) {
                log.error("解析失败: {}", result.getErrorMessage());
                return;
            }

            // 方式1：使用列名访问数据
            log.info("=== 使用列名访问数据 ===");
            for (Map<String, Object> row : result.getDataRows()) {
                for (String header : result.getHeaders()) {
                    Object value = row.get(header);
                    log.info("{}={}", header, value);
                }
            }

            // 方式2：使用列索引访问数据
            log.info("=== 使用列索引访问数据 ===");
            for (Map<String, Object> row : result.getDataRows()) {
                for (int i = 0; i < result.getColumnCount(); i++) {
                    Object value = row.get(String.valueOf(i));
                    log.info("第{}列={}", i + 1, value);
                }
            }

            // 方式3：使用原始数据行（List格式）
            log.info("=== 使用原始数据行 ===");
            for (int i = 0; i < result.getRawDataRows().size(); i++) {
                List<Object> rawRow = result.getRawDataRows().get(i);
                log.info("第{}行原始数据: {}", i + 1, rawRow);
            }

        } catch (IOException e) {
            log.error("解析失败", e);
        }
    }

    /**
     * 示例5：数据验证和处理
     */
    public void validateAndProcessExample(MultipartFile file) {
        try {
            ExcelParserHelper.ExcelParseResult result = ExcelParserHelper.parseExcel(file);
            
            if (!result.isSuccess()) {
                log.error("解析失败: {}", result.getErrorMessage());
                return;
            }

            // 检查必要的列是否存在
            List<String> requiredHeaders = List.of("姓名", "年龄", "部门");
            for (String requiredHeader : requiredHeaders) {
                if (!result.getHeaders().contains(requiredHeader)) {
                    log.error("缺少必要的列: {}", requiredHeader);
                    return;
                }
            }

            // 验证和处理数据
            int validRowCount = 0;
            for (int i = 0; i < result.getDataRows().size(); i++) {
                Map<String, Object> row = result.getDataRows().get(i);
                
                String name = (String) row.get("姓名");
                String age = (String) row.get("年龄");
                String department = (String) row.get("部门");
                
                // 数据验证
                if (name == null || name.trim().isEmpty()) {
                    log.warn("第{}行姓名为空，跳过", i + 1);
                    continue;
                }
                
                if (age == null || !age.matches("\\d+")) {
                    log.warn("第{}行年龄格式不正确: {}", i + 1, age);
                    continue;
                }
                
                if (department == null || department.trim().isEmpty()) {
                    log.warn("第{}行部门为空，跳过", i + 1);
                    continue;
                }
                
                // 处理有效数据
                log.info("有效数据: 姓名={}, 年龄={}, 部门={}", name, age, department);
                validRowCount++;
            }
            
            log.info("总共处理{}行数据，有效数据{}行", result.getDataRowCount(), validRowCount);
            
        } catch (IOException e) {
            log.error("解析失败", e);
        }
    }
}
