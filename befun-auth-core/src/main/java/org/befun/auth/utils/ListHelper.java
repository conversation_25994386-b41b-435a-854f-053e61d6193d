package org.befun.auth.utils;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiPredicate;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

public final class ListHelper {

    public static <T> List<T> minus(List<T> list1, List<T> list2, BiPredicate<List<T>, T> predicate) {
        if (CollectionUtils.isEmpty(list1)) {
            return null;
        }
        if (CollectionUtils.isEmpty(list2)) {
            return list1;
        }
        return list1.stream().filter(i -> !predicate.test(list2, i)).collect(Collectors.toList());
    }

    public static <T> List<T> minus(List<T> list1, List<T> list2) {
        return minus(list1, list2, i -> i);
    }

    public static <T> List<T> minus(List<T> list1, List<T> list2, Function<T, Object> compareProperty) {
        if (CollectionUtils.isEmpty(list1)) {
            return null;
        }
        if (CollectionUtils.isEmpty(list2)) {
            return list1;
        }
        // 过滤出 list2 中不存在的
        return list1.stream().filter(i -> list2.stream().noneMatch(j -> {
            Object ii = compareProperty.apply(i);
            Object jj = compareProperty.apply(j);
            if (ii == null || jj == null) {
                return false;
            }
            return ii.equals(jj);
        })).collect(Collectors.toList());
    }

    public static <T> List<T> contain(List<T> list1, List<T> list2, Function<T, Object> compareProperty, BinaryOperator<T> merge) {
        if (CollectionUtils.isEmpty(list1)) {
            return null;
        }
        if (CollectionUtils.isEmpty(list2)) {
            return null;
        }
        List<T> list = new ArrayList<>();
        for (T i : list1) {
            Object ii = compareProperty.apply(i);
            if (ii == null) {
                continue;
            }
            for (T j : list2) {
                Object jj = compareProperty.apply(j);
                if (jj == null) {
                    continue;
                }
                if (ii.equals(jj)) {
                    list.add(merge.apply(i, j));
                    break;
                }
            }
        }
        return list;
    }
}
