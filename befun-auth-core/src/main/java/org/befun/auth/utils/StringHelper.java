package org.befun.auth.utils;

import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.utils.RegHelper;

import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public final class StringHelper {

    public static String maskMobile(String mobile) {
        if (RegHelper.isMobile(mobile)) {
            char[] chars = mobile.toCharArray();
            char[] replace = new char[chars.length];
            for (int i = 0; i < chars.length; i++) {
                if (i >= 4 && i <= 7) {
                    replace[i] = '*';
                } else {
                    replace[i] = chars[i];
                }
            }
            return new String(replace);
        }
        return mobile;
    }

    public static String maskEmail(String email) {
        if (RegHelper.isEmail(email)) {
            String[] as = email.split("@");
            char[] chars = as[0].toCharArray();
            char[] replace = new char[chars.length];
            for (int i = 0; i < chars.length; i++) {
                if (i >= 1) {
                    replace[i] = '*';
                } else {
                    replace[i] = chars[i];
                }
            }
            return new String(replace) + "@" + as[1];
        }
        return email;
    }

    public static void main(String[] args) {
        System.out.println(maskMobile("18600000000"));
        System.out.println(maskEmail("<EMAIL>"));
    }


    public static List<Long> toLongList(String s, String separator, Supplier<List<Long>> getDefault) {
        if (StringUtils.isEmpty(separator) || StringUtils.isEmpty(s)) {
            return getDefault.get();
        }
        return Arrays.stream(s.split(separator))
                .filter(NumberUtils::isDigits)
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    public static List<Long> toLongList(String s) {
        return toLongList(s, ",", () -> null);
    }

    public static List<Long> toLongListOrEmpty(String s) {
        return toLongList(s, ",", ArrayList::new);
    }


    public static String generatorInviteCode(String email) {
        String base64 = StringUtils.isEmpty(email) ? "" : Base64.encodeBase64String(email.getBytes(StandardCharsets.UTF_8));
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + "." + base64;
    }

    public static String generatorVerifyLinkCode(String linkType) {
        String value = "verifyLinkCode:" + linkType;
        String base64 = Base64.encodeBase64String(value.getBytes(StandardCharsets.UTF_8));
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + "." + base64;
    }

    public static boolean isVerifyLinkCode(String code) {
        String[] a = code.split("\\.");
        if (a.length == 2) {
            return Base64.isBase64(a[1]) && new String(Base64.decodeBase64(a[1]), StandardCharsets.UTF_8).startsWith("verifyLinkCode");
        }
        return false;
    }

    public static String getVerifyLinkType(String code) {
        String[] a = code.split("\\.");
        if (a.length == 2) {
            String v;
            if (Base64.isBase64(a[1]) && (v = new String(Base64.decodeBase64(a[1]), StandardCharsets.UTF_8)).startsWith("verifyLinkCode")) {
                return v.substring("verifyLinkCode:".length());
            }
        }
        return null;
    }

//    public static void main(String[] args) {
//        String code = generatorInviteCode("<EMAIL>");
//        System.out.println(code);
//        System.out.println(isVerifyLinkCode(code));
//        code = generatorVerifyLinkCode("wechat-work");
//        System.out.println(code);
//        System.out.println(isVerifyLinkCode(code));
//        System.out.println(getVerifyLinkType(code));
//
//        System.out.println(concatUrl("https://www.xmplus.cn", "cem"));
//        System.out.println(concatUrl("https://www.xmplus.cn", "/cem"));
//        System.out.println(concatUrl("https://www.xmplus.cn/", "cem"));
//        System.out.println(concatUrl("https://www.xmplus.cn/", "/cem"));
//    }

    @SneakyThrows
    public static String toMD5(String s, int count) {
        // todo use DigestUtils.md5Hex(s);
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(s.getBytes());
        byte[] digest = md.digest();
        String md5 = DatatypeConverter.printHexBinary(digest);
        String result = null;
        switch (count) {
            case 16:
                result = md5.substring(8, 25);
                break;
            case 32:
            default:
                result = md5;
        }

        return result.toLowerCase();

    }

    public static String concatUrl(String domain, String path) {
        if (StringUtils.isEmpty(domain)) {
            return path;
        }
        if (!domain.endsWith("/")) {
            domain = domain + "/";
        }
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        return domain + path;
    }


    public static String concatUrlParams(String url, String param) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        if (url.indexOf("?") > 0) {
            return url + "&" + param;
        } else {
            return url + "?" + param;
        }
    }

}
