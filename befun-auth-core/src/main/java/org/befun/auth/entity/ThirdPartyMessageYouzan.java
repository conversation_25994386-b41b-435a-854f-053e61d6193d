package org.befun.auth.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "thirdparty_message_youzan")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ThirdPartyMessageYouzan extends BaseEntity {

    @Column(name = "msg_id")
    private String msgId;

    @Column(name = "client_id")
    private String clientId;

    @Column(name = "kdt_id")
    private String kdtId;

    @Column(name = "trade_id")
    private String tradeId;

    @Column(name = "wx_open_id")
    private String wxOpenId;

    @Column(name = "type")
    private String type;

    @Column(name = "parsed_fields")
    private String parsedFields;

    @Column(name = "message")
    private String message;

    @Schema(description = "消息状态：ignore, wait_binding, bound, trigger_journey")
    @Column(name = "status")
    private String status;

}
