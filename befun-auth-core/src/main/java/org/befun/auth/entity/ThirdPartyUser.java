package org.befun.auth.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "thirdparty_user")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
@EntityScopeStrategy
public class ThirdPartyUser extends EnterpriseEntity {

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "user_id")
    private Long userId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "app")
    private String app;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "source")
    private String source;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "open_id")
    private String openId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "nickname")
    private String nickname;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "is_valid")
    private boolean isValid;
}
