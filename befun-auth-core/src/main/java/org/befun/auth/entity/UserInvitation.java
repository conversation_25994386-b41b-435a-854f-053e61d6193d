package org.befun.auth.entity;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "user_invitation")
@DtoClass
public class UserInvitation extends EnterpriseEntity {

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "from_user_id")
    private Long fromUserId;

    @DtoProperty(description = "邀请码", jsonView = ResourceViews.Basic.class)
    private String code;

    @Column(name = "expire_date")
    @DtoProperty(description = "过期时间", jsonView = ResourceViews.Basic.class)
    private Date expireDate;

    @DtoProperty(description = "状态：1 未发送 2 已发送 0 已激活", jsonView = ResourceViews.Basic.class)
    private Integer status;

    @Column(name = "from_type")
    @DtoProperty(description = "邀请来源：admin 管理后台，wechat-work 企业微信", jsonView = ResourceViews.Basic.class)
    private String formType = "admin";
}