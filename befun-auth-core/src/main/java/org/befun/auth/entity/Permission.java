package org.befun.auth.entity;

import lombok.*;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "permission")
public class Permission extends BaseEntity {

    @Column(name = "role_id")
    private Long roleId;

    @Column(name = "module")
    private String module;

    @Column(name = "permission")
    private String permission;
}
