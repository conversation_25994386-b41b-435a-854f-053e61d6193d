package org.befun.auth.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.OrgVersionRecordStatus;
import org.befun.auth.dto.ext.OrganizationSmsRecordExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_version_record")
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class OrganizationVersionRecord extends EnterpriseEntity {

    @Enumerated(EnumType.STRING)
    @Schema(description = "版本类型")
    @Column(name = "version")
    private AppVersion version;

    @Schema(description = "版本变化：upgrade 升级; renew 续费")
    @Column(name = "type")
    private String type;

    @Schema(description = "版本价格")
    @Column(name = "price")
    private Integer price;

    @Schema(description = "实际支付金额")
    @Column(name = "cost_amount")
    private Integer costAmount;

    @Schema(description = "购买的版本开始时间")
    @Column(name = "start_date")
    private Date startDate;

    @Schema(description = "购买的版本结束时间")
    @Column(name = "end_date")
    private Date endDate;

    @Enumerated(EnumType.STRING)
    @Schema(description = "状态")
    @Column(name = "status")
    private OrgVersionRecordStatus status;

    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;
}