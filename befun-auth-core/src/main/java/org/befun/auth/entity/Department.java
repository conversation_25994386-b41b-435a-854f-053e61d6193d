package org.befun.auth.entity;

import lombok.*;
import org.befun.core.converter.StringListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.List;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "department")
@DtoClass
@EntityScopeStrategy
public class Department extends EnterpriseEntity {

    @Column(name = "title")
    @DtoProperty(description = "部门名称", jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "code")
    @DtoProperty(description = "部门编号", jsonView = ResourceViews.Basic.class)
    @Convert(converter = StringListConverter.class)
    private List<String> code;

    @Column(name = "pid")
    @DtoProperty(description = "上级部门编号", jsonView = ResourceViews.Basic.class)
    private Long pid;

    @DtoProperty(ignore = true)
    @Column(name = "parents_list")
    private String parentIds;

    @DtoProperty(ignore = true)
    @Column(name = "parents_name_list")
    private String parentNames;

    @Column(name = "level_id")
    private Long levelId;
    private Integer created;
    private Integer updated;

    @Column(name = "equivalent_code")
    @DtoProperty(description = "等效部门编号", jsonView = ResourceViews.Basic.class)
    @Convert(converter = StringListConverter.class)
    private List<String> equivalentCode;

    @PrePersist
    public void prePersist() {
        // 兼容旧数据
        levelId = 0L;
        if (created == null) {
            created = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        }
        updated = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
    }
}
