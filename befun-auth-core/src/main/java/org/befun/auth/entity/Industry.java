package org.befun.auth.entity;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "industry")
@DtoClass
public class Industry extends BaseEntity {

    @Column(name = "code")
    @DtoProperty(description = "行业编号")
    private String code;

    @Column(name = "name")
    @DtoProperty(description = "行业名称")
    private String name;

    @Column(name = "description")
    @DtoProperty(description = "描述说明")
    private String description;

    @Column(name = "cover_img")
    @DtoProperty(description = "封面图")
    private String coverImg;

}
