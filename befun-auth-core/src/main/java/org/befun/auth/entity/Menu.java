package org.befun.auth.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "menu")
@DtoClass
public class Menu extends BaseEntity {

    @Column(name = "pid")
    private Long pid;

    @Column(name = "type")
    private Integer type;

    @Column(name = "name")
    private String name;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "path")
    private String path;

    @Column(name = "full_path")
    private String fullPath;

    @Column(name = "display")
    private Integer display;

    @Schema(description = "菜单顺序")
    private Integer sequence;


}
