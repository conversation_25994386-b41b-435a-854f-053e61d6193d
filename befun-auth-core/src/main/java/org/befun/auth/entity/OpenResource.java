package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "open_resource")
public class OpenResource extends BaseEntity {

    @Column(name = "org_id")
    private Long orgId;
    @Column(name = "user_id")
    private Long userId;
    @Column(name = "type")
    private String type;
    @Column(name = "secret")
    private Integer secret;
    @Column(name = "password")
    private String password;
    @Column(name = "expire_time")
    private Date expireTime;
    @Column(name = "params")
    private String params;
}
