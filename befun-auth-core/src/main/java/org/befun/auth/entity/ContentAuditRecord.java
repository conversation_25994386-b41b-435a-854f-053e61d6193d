package org.befun.auth.entity;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.ContentAuditStatus;
import org.befun.auth.constant.ContentAuditType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "content_audit_record")
@Schema(description = "内容审核记录")
@DtoClass
public class ContentAuditRecord extends BaseEntity {

    @Schema(description = "签名")
    private String sign;
    @Enumerated(EnumType.STRING)
    @Schema(description = "类型：text，image，survey")
    private ContentAuditType type;
    @Schema(description = "来源")
    @Column(name = "source_id")
    private Long sourceId;
    @Schema(description = "审核内容")
    private String content;
    @Schema(description = "第三方审核响应")
    private String response;
    @Schema(description = "不合规、疑似关键字，{百度官方违禁词库:[abc,def],文本反作弊:[x,y,z]}")
    private String words;
    @Enumerated(EnumType.STRING)
    @Schema(description = "审核结果：pass 通过、noPass 不通过、suspected 疑似、unknown 未知")
    private ContentAuditStatus status;

}
