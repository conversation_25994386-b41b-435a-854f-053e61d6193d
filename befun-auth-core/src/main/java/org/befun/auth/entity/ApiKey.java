package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "api_key")
@SoftDelete
@Where(clause = "deleted=0")
@DtoClass
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER, enableAdmin = false)
public class ApiKey extends EnterpriseOwnerEntity {

    @Column(name = "api_key")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String apiKey;

    @Column(name = "api_secret")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String apiSecret;

    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

}
