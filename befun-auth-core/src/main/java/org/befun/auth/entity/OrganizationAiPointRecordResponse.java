package org.befun.auth.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "organization_ai_point_record_response")
@EntityScopeStrategy
public class OrganizationAiPointRecordResponse extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "消费记录id")
    @Column(name = "record_id")
    private Long recordId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答卷id")
    @Column(name = "response_id")
    private Long responseId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "AI点数变更")
    @Column(name = "amount")
    private Integer amount;
}