package org.befun.auth.entity.wework;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.WechatWorkOrderStatus;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "wework_order")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EntityScopeStrategy
public class WeworkOrder extends EnterpriseEntity {

    @Column(name = "corp_id")
    private String corpId;

    @Column(name = "buyer_user_id")
    private String buyerUserId;

    @Column(name = "base_count")
    private Integer baseCount;

    @Column(name = "external_contact_count")
    private Integer externalContactCount;

    @Column(name = "use_base_count")
    private Integer useBaseCount;

    @Column(name = "use_external_contact_count")
    private Integer useExternalContactCount;

    @Column(name = "account_duration")
    private Integer accountDuration;

    @Column(name = "wework_order_id")
    private String weworkOrderId;

    @Column(name = "pay_status")
    private Integer payStatus;

    public WeworkOrder(Long orgId, String corpId, String buyerUserId, Integer baseCount, Integer accountDuration, String weworkOrderId) {
        super(orgId);
        this.corpId = corpId;
        this.buyerUserId = buyerUserId;
        this.baseCount = baseCount;
        this.externalContactCount = 0;
        this.accountDuration = accountDuration;
        this.weworkOrderId = weworkOrderId;
        this.useBaseCount = 0;
        this.useExternalContactCount = 0;
        this.payStatus = WechatWorkOrderStatus.WAIT.ordinal();
    }
}
