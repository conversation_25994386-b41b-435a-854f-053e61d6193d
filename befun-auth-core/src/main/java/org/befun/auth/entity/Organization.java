package org.befun.auth.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "organization")
@DtoClass
public class Organization extends BaseEntity {

    @Column(name = "name")
    private String name;

    @Column(name = "industry_id")
    private Long industryId;

    @Column(name = "owner_id")
    private Long ownerId;

    @Column(name = "max_users")
    private Integer maxUsers;

    @Column(name = "is_block")
    private Integer isBlock;

    @Column(name = "code")
    private String code;

    @Column(name = "is_template")
    private Integer isTemplate;

    @Column(name = "available_date_begin")
    private Date availableDateBegin;

    @Column(name = "available_date_end")
    private Date availableDateEnd;

    @Column(name = "created")
    private String created;

    @Column(name = "updated")
    private String updated;

    @Column(name = "version")
    private String version;

    @Column(name = "app_types")
    private String appTypes;

    @Column(name = "wework_code")
    private String weworkCode;

    @Column(name = "wework_corp_id")
    private String weworkCorpId;

    @Column(name = "wework_agent_id")
    private String weworkAgentId;

    @Column(name = "wework_expire")
    private Integer weworkExpire;

    @Column(name = "optional_limit")
    private String optionalLimit;

    @Column(name = "auto_sync_customer")
    private Integer autoSyncCustomer;
}
