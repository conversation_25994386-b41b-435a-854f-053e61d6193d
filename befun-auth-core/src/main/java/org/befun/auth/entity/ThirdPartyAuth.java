package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Entity
@Table(name = "thirdparty_auth")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
@EntityScopeStrategy
public class ThirdPartyAuth extends EnterpriseEntity {

    @Enumerated(EnumType.STRING)
    @Column(name = "auth_type")
    private ThirdPartyAuthType authType;

    @Column(name = "app")
    private String app;

    @Column(name = "source")
    private String source;

    @Column(name = "enable_white_list")
    private Integer enableWhiteList = 0;

    @Column(name = "config")
    private String config;

    private String remark;


}
