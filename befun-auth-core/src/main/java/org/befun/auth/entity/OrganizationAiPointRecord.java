package org.befun.auth.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.AiPointRecordStatus;
import org.befun.auth.constant.AiPointRecordType;
import org.befun.auth.dto.ext.OrganizationAiPointRecordExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "organization_ai_point_record")
@DtoClass(includeAllFields = true, superClass = OrganizationAiPointRecordExtDto.class)
@EntityScopeStrategy
public class OrganizationAiPointRecord extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "消费类型：recharge AI点数充值，text_analysis 文本分析，warning 预警")
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private AiPointRecordType type;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "来源id：recharge 时为null, text_analysis 时为 文本分析id, warning 时为 ruleId")
    @Column(name = "source_id")
    private Long sourceId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "AI点数变更")
    @Column(name = "amount")
    private Integer amount;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private AiPointRecordStatus status;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;
}