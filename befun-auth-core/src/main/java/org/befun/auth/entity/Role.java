package org.befun.auth.entity;

import lombok.*;
import org.befun.auth.dto.ext.RoleExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.PrePersist;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "role")
@Where(clause = "platform='cem'")
@DtoClass(superClass = RoleExtDto.class)
@EntityScopeStrategy
public class Role extends EnterpriseEntity {

    @DtoProperty(description = "角色名称", queryable = true, jsonView = ResourceViews.Basic.class)
    private String name;

    @DtoProperty(description = "角色描述", queryable = true, jsonView = ResourceViews.Basic.class)
    private String description;

    @DtoProperty(description = "角色平台", jsonView = ResourceViews.Basic.class)
    private String platform;

    @DtoProperty(description = "是否可以编辑：0 不可以 1 可以编辑删除 2 只能编辑", jsonView = ResourceViews.Basic.class)
    private Integer editable = 0;

    @DtoProperty(description = "是否启用", jsonView = ResourceViews.Basic.class)
    private Boolean enable = false;

    @DtoProperty(description = "角色类型：1 超级管理员(不能编辑删除) 3 成员(只能编辑) 4 其他(可以编辑删除)", jsonView = ResourceViews.Basic.class)
    private Integer type;

    @DtoProperty(description = "角色编号，企业内唯一", jsonView = ResourceViews.Basic.class)
    private String code;

    private Integer created;
    private Integer updated;

    @PrePersist
    public void prePersist() {
        // 兼容旧数据
        int currentTime = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        if (created == null) {
            created = currentTime;
        }
        updated = currentTime;
    }
}
