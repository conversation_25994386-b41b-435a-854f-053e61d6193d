package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "organization_stat")
@DtoClass
public class OrganizationStat extends EnterpriseEntity {

    @Column(name = "last_login_user_id")
    private Long lastLoginUserId;

    @Column(name = "last_login_time")
    private Date lastLoginTime;

}
