package org.befun.auth.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.SmsRecordStatus;
import org.befun.auth.constant.SmsRecordType;
import org.befun.auth.dto.ext.OrganizationSmsRecordExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_sms_record")
@DtoClass(includeAllFields = true, superClass = OrganizationSmsRecordExtDto.class)
@EntityScopeStrategy
public class OrganizationSmsRecord extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "任务id")
    @Column(name = "task_progress_id")
    private Long taskProgressId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "来源")
    @Column(name = "source")
    private String source;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信内容")
    @Column(name = "content")
    private String content;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "消费类型：短信发送，短信购买")
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private SmsRecordType type;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信变更")
    @Column(name = "amount")
    private Integer amount;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信余额")
    @Column(name = "balance")
    private Integer balance;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private SmsRecordStatus status;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;
}