package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "organization_config")
public class OrganizationConfig extends EnterpriseEntity {

    @Column(name = "type")
    private String type;

    @Column(name = "config")
    private String config;

}