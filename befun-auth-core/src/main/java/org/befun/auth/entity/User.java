package org.befun.auth.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.auth.dto.ext.UserExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.ListHelper;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrePersist;
import javax.persistence.Table;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "user")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Where(clause = "is_delete=0")
@Builder
@DtoClass(superClass = UserExtDto.class)
@SoftDelete(propertyType = Integer.class, property = "isDelete", deleteValue = "1")
@EntityScopeStrategy
public class User extends EnterpriseEntity {

    @DtoProperty(description = "是否为管理员", jsonView = ResourceViews.Basic.class)
    @Column(name = "is_admin")
    private Boolean isAdmin;

//    @Deprecated(since = "v1.9.3")
//    @Schema(description = "v1.9.3 用户部门修改为多部门，此字段废弃，使用departmentIds")
//    @Column(name = "department_id")
//    private Long departmentId;

    @Schema(description = "v1.9.3 新增，部门id列表：[[123],[456],[789]]")
    @Column(name = "department_ids")
    private String formatDepartmentIds;

    @Column(name = "role_id")
    private String oldRoleId;

    @DtoProperty(description = "用户名", jsonView = ResourceViews.Basic.class)
    @Column(name = "username")
    private String username;

    @Column(name = "password")
    private String password;

    @Column(name = "password_strength")
    private Integer passwordStrength = 0;

    @DtoProperty(description = "姓名", queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "truename")
    private String truename;

    @DtoProperty(description = "手机号", queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "mobile")
    private String mobile;

    @DtoProperty(description = "邮箱", queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "email")
    private String email;

    @DtoProperty(description = "员工号", queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "employee_no")
    private String employeeNo;

    @Column(name = "wx_cem_openid")
    private String wxCemOpenid;

    @Column(name = "wx_cem_info")
    private String wxCemInfo;

    @DtoProperty(description = "头像", jsonView = ResourceViews.Basic.class)
    @Column(name = "avatar")
    private String avatar;

    @DtoProperty(description = "昵称", jsonView = ResourceViews.Basic.class)
    @Column(name = "nickname")
    private String nickname;

    @DtoProperty(description = "可使用的系统")
    @Column(name = "available_systems")
    private String availableSystems;

    @DtoProperty(description = "用户状态：0-(启用)旧版本初始化状态 1-启用 2-禁用 3-邀请待激活 ", jsonView = ResourceViews.Basic.class)
    @Column(name = "status")
    private Integer status;

    @Column(name = "platform")
    private String platform;

    @Column(name = "is_finished_guide")
    private String isFinishedGuide;

    // 兼容旧数据
    private Integer created = 0;
    private Integer updated = 0;

    @Column(name = "is_delete", columnDefinition = "bit(1) default 0")
    private Integer isDelete = 0;

    @Column(name = "latest_login")
    private Integer latestLogin = 0;

    @Column(name = "guide_info")
    private String guideInfo;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "ip")
    @Schema(description = "ip")
    private String ip;

    @Column(name = "country")
    @Schema(description = "国家")
    private String country;

    @Column(name = "province")
    @Schema(description = "省份")
    private String province;

    @Column(name = "city")
    @Schema(description = "城市")
    private String city;

    @Column(name = "old_sp_user", columnDefinition = "bit(1) default 0")
    @DtoProperty(description = "是否老调研家用户 ", jsonView = ResourceViews.Basic.class)
    private Integer oldSurveyPlusUser = 0;

    @PrePersist
    public void prePersist() {
        // 兼容旧数据
        if (created == null) {
            created = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        }
        updated = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
    }

    public Set<Long> parseDepartmentIds() {
        return new HashSet<>(parseDepartmentIds2());
    }

    public List<Long> parseDepartmentIds2() {
        return ListHelper.parseDepartmentIds(formatDepartmentIds);
    }

    public void formatDepartmentIds(Collection<Long> ids) {
        formatDepartmentIds = ListHelper.formatDepartmentIds(ids);
    }

    public void appendDepartmentId(Long appendDepartmentId) {
        if (appendDepartmentId == null) {
            return;
        }
        Set<Long> oldIds = parseDepartmentIds();
        if (oldIds == null) {
            oldIds = new HashSet<>();
        }
        oldIds.add(appendDepartmentId);
        formatDepartmentIds(oldIds);
    }
}
