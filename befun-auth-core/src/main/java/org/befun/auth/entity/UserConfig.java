package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.EnterpriseOwnerEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "user_config")
public class UserConfig extends EnterpriseOwnerEntity {

    @Column(name = "type")
    private String type;

    @Column(name = "config")
    private String config;

}