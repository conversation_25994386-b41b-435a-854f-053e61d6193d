package org.befun.auth.entity.wework;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "wework_order_account")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EntityScopeStrategy
public class WeworkOrderAccount extends EnterpriseEntity {

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "corp_id")
    private String corpId;

    @Column(name = "active_code")
    private String activeCode;

    @Column(name = "open_id")
    private String openId;

    @Column(name = "status")
    private Integer status;

    @Column(name = "active_time")
    private Date activeTime;

    @Column(name = "expire_time")
    private Date expireTime;

    public WeworkOrderAccount(Long orgId,Long orderId, String corpId, String activeCode) {
        super(orgId);
        this.corpId = corpId;
        this.orderId = orderId;
        this.activeCode = activeCode;
        this.status = 0;
    }
}
