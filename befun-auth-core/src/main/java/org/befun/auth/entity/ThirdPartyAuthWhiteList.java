package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "thirdparty_auth_white_list")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EntityScopeStrategy
public class ThirdPartyAuthWhiteList extends EnterpriseEntity {

    @Column(name = "third_party_auth_id")
    private Long thirdPartyAuthId;

    @Column(name = "open_id")
    private String openId;

}
