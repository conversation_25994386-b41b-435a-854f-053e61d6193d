package org.befun.auth.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "organization_wallet")
public class OrganizationWallet extends EnterpriseEntity {

    @Column(name = "money", nullable = false)
    private Integer money;

    @Column(name = "sms", nullable = false)
    private Integer sms;

    @Column(name = "ai_point")
    private Integer aiPoint;

}