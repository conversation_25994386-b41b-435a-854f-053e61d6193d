package org.befun.auth.service;

import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.core.exception.BadRequestException;

public interface IResourcePermissionRelationService {

    ResourcePermissionService getResourcePermissionService();

    ResourcePermissionType permissionType();

    default boolean isMember(Long id) {
        return currentRelationType(id).atLeastMember();
    }

    default void checkMember(Long id) {
        if (!isMember(id)) {
            throw new BadRequestException("无权限");
        }
    }

    default boolean isAdmin(Long id) {
        return currentRelationType(id).atLeastAdmin();
    }

    default void checkAdmin(Long id) {
        if (!isAdmin(id)) {
            throw new BadRequestException("无权限");
        }
    }

    default boolean isOwner(Long id) {
        return currentRelationType(id).isOwner();
    }

    default void checkOwner(Long id) {
        if (!isOwner(id)) {
            throw new BadRequestException("无权限");
        }
    }

    default ResourcePermissionRelationType currentRelationType(Long id) {
        return getResourcePermissionService().calculateCurrentRelations(id, permissionType());
    }

}
