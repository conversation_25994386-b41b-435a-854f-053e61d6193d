package org.befun.auth.service;

import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.entity.Role;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserRole;
import org.befun.auth.entity.UserRoleDto;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.UserRoleRepository;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserRoleService extends BaseService<UserRole, UserRoleDto, UserRoleRepository> {

    @Autowired
    private UserRoleRepository userRoleRepository;
    @Lazy
    @Autowired
    private RoleService roleService;

    public Map<Long, List<SimpleUser>> getByRoles(Long orgId, List<Long> roleIds) {
        Map<Long, List<SimpleUser>> map = new HashMap<>();
        if (orgId == null || CollectionUtils.isEmpty(roleIds)) {
            return map;
        }
        Map<Long, SimpleUser> userMap = new HashMap<>();
        List<UserRole> list = userRoleRepository.findByRoleIdIn(roleIds);
        list.forEach(i -> {
            Role role = i.getRole();
            User user = i.getUser();
            if (role != null && user != null && orgId.equals(user.getOrgId()) && orgId.equals(role.getOrgId())) {
                SimpleUser simpleUser = userMap.computeIfAbsent(user.getId(), k -> SimpleUser.fromUser(user));
                map.computeIfAbsent(role.getId(), k -> new ArrayList<>()).add(simpleUser);
            }
        });
        return map;
    }

    public List<User> getByRole(Long roleId) {
        return Optional.ofNullable(userRoleRepository.findByRoleId(roleId)).map(i -> i.stream().map(UserRole::getUser).filter(Objects::nonNull).collect(Collectors.toList())).orElse(null);
    }

    public List<Role> getByUser(Long userId) {
        return Optional.ofNullable(userRoleRepository.findByUserId(userId)).map(i -> i.stream().map(UserRole::getRole).filter(r -> Objects.nonNull(r) && r.getEnable()).collect(Collectors.toList())).orElse(null);
    }

    public void addUserRole(User user, Role role) {
        List<UserRole> userRoles = userRoleRepository.findByRoleIdAndUserId(role.getId(), user.getId());
        if (userRoles.isEmpty()) {
            UserRole userRole = new UserRole();
            userRole.setUser(user);
            userRole.setRole(role);
            userRoleRepository.save(userRole);
        }
    }

    public void addUserRole(Long userId, Long roleId) {
        User user = new User();
        user.setId(userId);
        Role role = new Role();
        role.setId(roleId);
        addUserRole(user, role);
    }

    public void deleteUserRole(Long userId, Long roleId) {
        List<UserRole> userRoles = userRoleRepository.findByRoleIdAndUserId(roleId, userId);
        if (!userRoles.isEmpty()) {
            userRoleRepository.deleteAll(userRoles);
        }
    }

    public boolean saveUserRole(Long userId, List<Long> newRoleIds) {
        boolean currentIsAdmin = currentIsAdmin();
        boolean currentHasPermissionRoleEdit = currentIsAdmin || currentHasPermissions(PermissionPath.SYS_MANAGE_ROLE_MANAGE_EDIT);
        return saveUserRole(userId, newRoleIds, currentIsAdmin, currentHasPermissionRoleEdit);
    }

    /**
     * v1.10.9
     * 当用户没有超级管理员的角色：
     * 如果该用户同时具有角色管理编辑权限，可以修改用户角色，但不能为用户分配“超级管理员”角色，复选框禁用。
     * 如果该用户不具有角色管理编辑权限，则不能修改用户角色，所有复选框禁用。
     */
    public boolean saveUserRole(Long userId, List<Long> newRoleIds, boolean currentIsAdmin, boolean currentHasPermissionRoleEdit) {
        List<Long> oldRoleIds = userRoleRepository.findByUserId(userId).stream().map(i -> i.getRole() == null ? null : i.getRole().getId()).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> add = minus(newRoleIds, oldRoleIds);
        Role superAdminRole = null;
        if (!add.isEmpty()) {
            superAdminRole = checkRoleChange(currentIsAdmin, currentHasPermissionRoleEdit, superAdminRole, add);
            userRoleRepository.saveAll(add.stream().map(i -> {
                UserRole entity = new UserRole();
                User user = new User();
                user.setId(userId);
                entity.setUser(user);
                Role role = new Role();
                role.setId(i);
                entity.setRole(role);
                return entity;
            }).collect(Collectors.toList()));
        }
        List<Long> delete = minus(oldRoleIds, newRoleIds);
        if (!delete.isEmpty()) {
            checkRoleChange(currentIsAdmin, currentHasPermissionRoleEdit, superAdminRole, delete);
            List<UserRole> deletes = userRoleRepository.findByUserIdAndRoleIdIn(userId, delete);
            if (!deletes.isEmpty()) {
                userRoleRepository.deleteAll(deletes);
            }
        }
        return true;
    }

    private Role checkRoleChange(boolean currentIsAdmin, boolean currentHasPermissionRoleEdit, Role superAdmin, List<Long> change) {
        if (!currentHasPermissionRoleEdit) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.USER_CHANGE_ROLE_ERROR);
        }
        if (!currentIsAdmin) {
            // 不能存在超管
            if (superAdmin == null) {
                superAdmin = roleService.getSuperAdminByOrg(TenantContext.getCurrentTenant());
            }
            Long superAdminRoleId = superAdmin == null ? null : superAdmin.getId();
            if (superAdminRoleId != null) {
                if (change.stream().anyMatch(i -> i.equals(superAdminRoleId))) {
                    throw ToastMessageHelper.badRequestException(AuthToastMessage.USER_CHANGE_ROLE_ERROR);
                }
            }
        }
        return superAdmin;
    }

    private List<Long> minus(List<Long> l1, List<Long> l2) {
        return l1.stream().filter(i -> !l2.contains(i)).collect(Collectors.toList());
    }

}
