package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.exception.AmountNotEnoughException;
import org.befun.auth.projection.SimpleWalletAiPoint;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class OrganizationAIPointService {

    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Transactional
    public int recharge(Long orgId, int amount) {
        balance(orgId);
        Long balance = getValueOpt().increment(balanceKey(orgId), amount);
        if (balance != null) {
            syncToDb(orgId);
            return balance.intValue();
        }
        return 0;
    }

    /**
     * 查询真实余额
     */
    public int balance(Long orgId) {
        String cache = getValueOpt().get(balanceKey(orgId));
        if (NumberUtils.isDigits(cache)) {
            return Integer.parseInt(cache);
        }
        SimpleWalletAiPoint wallet = organizationWalletRepository.findFirstWalletAiPointByOrgId(orgId);
        int balance;
        if (wallet == null || wallet.getAiPoint() == null) {
            balance = 0;
        } else {
            balance = wallet.getAiPoint();
        }
        getValueOpt().setIfAbsent(balanceKey(orgId), Integer.toString(balance));
        return balance;
    }

    /**
     * 消耗 AI 点数
     *
     * @return balance 余量
     */
    public int consumer(Long orgId, Integer cost) {
        if (cost <= 0) {
            throw new BadRequestException("消耗AI点数必须大于0");
        }
        if (!hasBalance(orgId, cost)) {
            throw new AmountNotEnoughException("AI点数余量不足");
        }
        String key = balanceKey(orgId);
        Long balance = getValueOpt().decrement(key, cost);
        if (balance == null || balance < 0) {
            getValueOpt().increment(key, cost);
            throw new AmountNotEnoughException("AI点数余量不足");
        }
        log.info("企业({})AI点数余量扣除成功(-{})，余量：{}", orgId, cost, balance);
        syncToDb(orgId);
        return balance.intValue();
    }

    /**
     * 是否有额度
     */
    public boolean hasBalance(Long orgId, Integer cost) {
        int balance = balance(orgId);
        return balance - cost >= 0;
    }

    @Transactional
    public void syncToDb(Long orgId) {
        int balance = balance(orgId);
        organizationWalletRepository.updateWalletAiPoint(orgId, balance);
    }

    private ValueOperations<String, String> getValueOpt() {
        return stringRedisTemplate.opsForValue();
    }

    private String balanceKey(Long orgId) {
        return String.format("ai-point-balance:%d", orgId);
    }
}
