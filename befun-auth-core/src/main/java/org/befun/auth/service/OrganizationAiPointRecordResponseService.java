package org.befun.auth.service;

import org.befun.auth.constant.AiPointRecordStatus;
import org.befun.auth.constant.AiPointRecordType;
import org.befun.auth.constant.LockKeys;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.auth.entity.OrganizationAiPointRecordResponse;
import org.befun.auth.exception.AmountNotEnoughException;
import org.befun.auth.repository.OrganizationAiPointRecordResponseRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.service.LockRunnableHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class OrganizationAiPointRecordResponseService {

    @Autowired
    private OrganizationAiPointRecordResponseRepository repository;
    @Autowired
    private OrganizationAIPointService aiPointService;
    @Autowired
    private OrganizationAiPointRecordService aiPointRecordService;
    @Autowired
    private LockRunnableHelper lockRunnableHelper;

    @Transactional
    public void addByWarningTrigger(Long orgId, Long userId, Long responseId, Map<Long, Integer> ruleCostMap, Runnable runnable) throws AmountNotEnoughException {
        if (orgId == null || orgId <= 0
                || userId == null || userId <= 0
                || responseId == null || responseId <= 0
                || ruleCostMap == null || ruleCostMap.isEmpty()
                || runnable == null
        ) {
            throw new BadRequestException();
        }
        Set<Long> costRuleIds = new HashSet<>();
        AtomicInteger totalCostCounter = new AtomicInteger();
        Map<Long/*ruleId*/, OrganizationAiPointRecord> recordMap = new HashMap<>();
        ruleCostMap.forEach((ruleId, cost) -> {
            AiPointRecordStatus status = AiPointRecordStatus.init;
            if (cost != null && cost > 0) {
                costRuleIds.add(ruleId);
                totalCostCounter.addAndGet(cost);
                status = AiPointRecordStatus.success;
            }
            OrganizationAiPointRecord record = aiPointRecordService.getOrAddRecord(orgId, userId, AiPointRecordType.warning, ruleId, status);
            recordMap.put(ruleId, record);
        });
        int totalCost = totalCostCounter.get();
        if (totalCost <= 0) {
            // 没有AI预警，直接运行
            runnable.run();
            recordMap.forEach((ruleId, record) -> {
                addRecordResponse(orgId, record.getId(), responseId, 0);
            });
        } else if (aiPointService.hasBalance(orgId, totalCost)) {
            // 点数足够，直接运行
            // 运行成功之后，在扣除点数
            runnable.run();
            recordMap.forEach((ruleId, record) -> {
                int cost = ruleCostMap.getOrDefault(ruleId, 0);
                addRecordResponse(orgId, record.getId(), responseId, cost);
                if (cost > 0) {
                    aiPointRecordService.appendAmount(record.getId(), cost);
                    aiPointService.consumer(orgId, cost); // 这里会抛出余额不足异常
                    if (record.getStatus() != AiPointRecordStatus.success) {
                        aiPointRecordService.updateStatus(record.getId(), AiPointRecordStatus.success);
                    }
                }
            });
        } else {
            throw new AmountNotEnoughException("AI点数余量不足");
        }
    }

    @Transactional
    public void addByWarningRerun(Long orgId, Long userId, Long responseId, Long ruleId, int cost, boolean forceRun, Runnable runnable) throws AmountNotEnoughException {
        if (orgId == null || orgId <= 0
                || userId == null || userId <= 0
                || responseId == null || responseId <= 0
                || ruleId == null || ruleId <= 0
                || runnable == null
        ) {
            throw new BadRequestException();
        }
        // 确认项目存在
        AiPointRecordStatus status = cost > 0 ? AiPointRecordStatus.success : AiPointRecordStatus.init;
        OrganizationAiPointRecord record = aiPointRecordService.getOrAddRecord(orgId, userId, AiPointRecordType.warning, ruleId, status);
        // 必须强制执行，或者未执行过
        if (forceRun || !repository.existsByOrgIdAndRecordIdAndResponseId(orgId, record.getId(), responseId)) {
            // 没有余额
            if (cost > 0 && !aiPointService.hasBalance(orgId, cost)) {
                throw new AmountNotEnoughException("AI点数余量不足");
            }
            // 开始执行
            runnable.run();
            // 添加规则执行记录
            addRecordResponse(orgId, record.getId(), responseId, cost);
            // 扣除AI点
            if (cost > 0) {
                // 项目消费AI点累加
                aiPointRecordService.appendAmount(record.getId(), cost);
                // 消费AI点
                aiPointService.consumer(orgId, cost); // 这里会抛出余额不足异常
                if (record.getStatus() != AiPointRecordStatus.success) {
                    // 如果项目的状态不是success，改一下状态
                    aiPointRecordService.updateStatus(record.getId(), AiPointRecordStatus.success);
                }
            }
        }
    }

    @Transactional
    public void addByTextAnalysis(Long orgId, Long userId, Long analysisId, Long responseId, Integer cost, Runnable runnable) {
        addByTextAnalysis(orgId, userId, analysisId, responseId, cost, false, runnable);
    }

    @Transactional
    public void addByTextAnalysis(Long orgId, Long userId, Long analysisId, Long responseId, Integer cost, boolean forceRun, Runnable runnable) {
        if (orgId == null || orgId <= 0
                || userId == null || userId <= 0
                || analysisId == null || analysisId <= 0
                || responseId == null || responseId <= 0
                || cost == null
                || runnable == null
        ) {
            throw new BadRequestException();
        }
        OrganizationAiPointRecord record = aiPointRecordService.getOrAddRecord(orgId, userId, AiPointRecordType.text_analysis, analysisId);
        OrganizationAiPointRecordResponse exist = repository.findFirstByOrgIdAndRecordIdAndResponseId(orgId, record.getId(), responseId);
        if (!forceRun && exist != null) {
            return; // 已处理
        }
        if (cost <= 0 || aiPointService.hasBalance(orgId, cost)) {
            lockRunnableHelper.run(LockKeys.ai_point_record_response, List.of(record.getId(), responseId), () -> {
                runnable.run();
                if (cost > 0) {
                    aiPointRecordService.appendAmount(record.getId(), cost);
                    aiPointService.consumer(orgId, cost);
                }
                if (exist == null) {
                    addRecordResponse(orgId, record.getId(), responseId, cost);
                }
            });
            return;
        }
        throw new AmountNotEnoughException("AI点数余量不足");
    }

    private void addRecordResponse(Long orgId, Long recordId, Long responseId, Integer cost) {
        OrganizationAiPointRecordResponse entity = new OrganizationAiPointRecordResponse();
        entity.setOrgId(orgId);
        entity.setRecordId(recordId);
        entity.setResponseId(responseId);
        entity.setAmount(cost);
        repository.save(entity);
    }
}
