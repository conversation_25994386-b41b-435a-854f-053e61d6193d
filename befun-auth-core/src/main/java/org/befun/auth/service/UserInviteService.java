package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.InvitationProperty;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.LoginPlatform;
import org.befun.auth.constant.UserInvitationStatus;
import org.befun.auth.constant.UserStatus;
import org.befun.auth.dto.*;
import org.befun.auth.entity.*;
import org.befun.auth.provider.local.RefreshTokenProvider;
import org.befun.auth.repository.UserInvitationRepository;
import org.befun.auth.utils.PasswordHelper;
import org.befun.auth.utils.StringHelper;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.auth.constant.UserStatus.ENABLE;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

@Slf4j
@Service
public class UserInviteService extends BaseService<UserInvitation, UserInvitationDto, UserInvitationRepository> {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private UserInvitationRepository userInvitationRepository;
    @Autowired
    private OrganizationService organizationService;
    @Lazy
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private UserEmailHelper userEmailHelper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private RefreshTokenProvider refreshTokenProvider;
    @Autowired
    private PasswordHelper passwordHelper;

    /**
     * 使用账号信息邀请用户
     */
    @Transactional
    public UserInviteResponseDto inviteMemberByUserInfo(UserInviteUserInfoRequestDto inviteInfo) {
        inviteInfo.confirmPassword(passwordHelper::decryptRsa);
        Organization currentOrg = organizationService.requireCurrent();
        User currentUser = userService.requireCurrent();
        UserInviteResponseDto responseDto = checkExistsAndLimit(currentOrg, inviteInfo);
        if (responseDto != null) {
            return responseDto;
        }
        List<Role> roles = confirmRoles(currentOrg.getId(), inviteInfo.getRoleIds());
        List<Department> departments = confirmDepartments(currentOrg.getId(), inviteInfo.getDepartmentIds());
        // 添加用户
        User user = userService.addUserByEmailInvite(currentOrg.getId(), inviteInfo, roles, departments);
        // 添加用户角色
        roles.forEach(role -> userRoleService.addUserRole(user, role));
        if (inviteInfo.isNotifyEmail()) {
            asyncService.inviteNotifyByUserInfo(currentOrg, currentUser, inviteInfo);
        }
        return UserInviteResponseDto.success();
    }


    /**
     * 使用邮箱邀请用户
     */
    @Transactional
    public UserInviteResponseDto inviteMembers(UserInviteRequestDto dto) {
        // 判断是否有重复的邮箱
        checkRepeat(dto.getEmails());
        Map<UserInvitation, User> invitations = new HashMap<>();
        Organization currentOrg = organizationService.requireCurrent();
        User currentUser = userService.requireCurrent();
        // 检查所有前置条件
        List<IUserInviteInfo> inviteInfos = IUserInviteInfo.ofEmails(dto.getEmails(), dto.getApp());
        UserInviteResponseDto responseDto = checkExistsAndLimit(currentOrg, inviteInfos);
        if (responseDto != null) {
            return responseDto;
        }
        List<Role> roles = confirmRoles(currentOrg.getId(), dto.getRoleIds());
        List<Department> departments = confirmDepartments(currentOrg.getId(), dto.getDepartmentIds());
        Date expireDate = expireInviteDate();
        inviteInfos.forEach(inviteInfo -> {
            createInviteUser(currentOrg, "admin", currentUser, inviteInfo, expireDate, roles, departments, invitations);
        });
        asyncService.inviteNotify(currentOrg, currentUser, dto.getApp(), invitations);
        return UserInviteResponseDto.success();
    }

    public UserInviteResponseDto inviteMembers(Long orgId, String email, String app, Consumer<Long> afterUserCreate) {
        Map<UserInvitation, User> invitations = new HashMap<>();
        Organization org = organizationService.requireById(orgId);
        IUserInviteInfo inviteInfo = IUserInviteInfo.ofEmail(email, app);
        UserInviteResponseDto responseDto = checkExistsAndLimit(org, inviteInfo);
        if (responseDto != null) {
            return responseDto;
        }
        List<Role> roles = confirmRoles(orgId, null);
        List<Department> departments = confirmDepartments(orgId, null);
        Date expireDate = expireInviteDate();
        User user = createInviteUser(org, "wechat-work", null, inviteInfo, expireDate, roles, departments, invitations);
        afterUserCreate.accept(user.getId());
        asyncService.inviteNotify(org, null, app, invitations);
        return UserInviteResponseDto.success();
    }

    private User createInviteUser(Organization org, String fromType, User fomrUser, IUserInviteInfo inviteInfo, Date expireDate, List<Role> roles, List<Department> departments, Map<UserInvitation, User> invitations) {
        User user = userService.addUserByEmailInvite(org.getId(), inviteInfo, roles, departments);
        // 添加用户角色
        roles.forEach(role -> userRoleService.addUserRole(user, role));
        // 添加邀请记录
        UserInvitation invitation = addInvitation(inviteInfo.getEmail(), expireDate, fromType, fomrUser == null ? user : fomrUser, user);
        invitations.put(invitation, user);
        return user;
    }

    private List<Role> confirmRoles(Long orgId, List<Long> roleIds) {
        List<Role> roles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roleIds)) {
            // 如果有设置角色则使用设置的角色
            List<Role> roles1 = roleService.getByIds(roleIds);
            if (CollectionUtils.isNotEmpty(roles1)) {
                roles.addAll(roles1);
            }
        }
        if (roles.isEmpty()) {
            // 如果没有设置角色则使用成员角色
            roles.add(roleService.getOrCreateMemberRole(orgId));
        }
        return roles;
    }

    private List<Department> confirmDepartments(Long orgId, List<Long> departmentIds) {
        List<Department> departments = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            // 如果有设置部门则使用设置的部门
            List<Department> departments1 = departmentService.getByIds(departmentIds);
            if (CollectionUtils.isNotEmpty(departments1)) {
                departments.addAll(departments1);
            }
        }
        if (departments.isEmpty()) {
            // 如果没有设置部门则使用根部门
            departments.add(departmentService.getRoot(orgId));
        }
        return departments;
    }

    /**
     * 发送邀请邮件
     */
    public boolean inviteNotify(@Nullable Organization org, @Nullable User fromUser, UserInvitation invitation, @Nullable User targetUser, String app, boolean refreshCode, boolean throwable) {
        if (invitation == null) {
            log.warn("邀请不存在");
            return inviteError(throwable, "邀请不存在");
        }
        // 此条记录有效 status = 1 | 2
        if (!UserInvitationStatus.canNotify(invitation.getStatus())) {
            log.warn("邀请无效 invitation.status={}", invitation.getStatus());
            return inviteError(throwable, "邀请无效");
        }
        // targetUser 如果新创建的用户，则会通过参数传进来，如果是重发邮件，则可能是null, 需要重新查询一次
        if (targetUser == null) {
            targetUser = userService.get(invitation.getUserId());
        }
        if (targetUser == null) {
            log.warn("成员已删除 userId={}", invitation.getUserId());
            return inviteError(throwable, "成员已删除");
        }
        if (StringUtils.isEmpty(targetUser.getEmail())) {
            log.warn("成员未配置邮箱 userId={}", targetUser.getId());
            return inviteError(throwable, "成员未配置邮箱");
        }
        // 此用户的状态为3
        if ("admin".equals(invitation.getFormType()) && !UserStatus.isInvite(targetUser.getStatus())) {
            log.warn("成员已激活 user.status={}", targetUser.getStatus());
            return inviteError(throwable, "成员已激活");
        }
        if (refreshCode) {
            invitation.setCode(StringHelper.generatorInviteCode(targetUser.getEmail()));
            invitation.setExpireDate(expireInviteDate());
        }
        InvitationProperty properties = authProperties.getInvitation();
        String templateName;
        Map<String, Object> params;
        if ("wechat-work".equals(invitation.getFormType())) {
            templateName = properties.getEmailTemplateNameWechatWork();
            params = userEmailHelper.buildBindParams(targetUser.getEmail(), app, properties.getInviteUrl() + invitation.getCode());
        } else {
            templateName = properties.getEmailTemplateName();
            params = userEmailHelper.buildInviteParams(org, targetUser, fromUser, invitation, app, properties.getInviteUrl());
        }
        boolean result = userEmailHelper.sendEmail(templateName, targetUser.getEmail(), params);
        if (result) {
            invitation.setStatus(UserInvitationStatus.NOTIFY.ordinal());
            userInvitationRepository.save(invitation);
            log.info("成员邀请邮件发送成功，email={}", targetUser.getEmail());
        } else {
            log.error("成员邀请邮件发送失败，email={}", targetUser.getEmail());
            if (throwable) {
                throw new BadRequestException("成员邀请邮件发送失败");
            }
        }
        return true;
    }

    /**
     * 发送通知邮件，使用用户信息邀请的用户，不用再激活了，只是发送一个通知邮件
     */
    public void inviteNotifyByUserInfo(Organization org, User fromUser, IUserInviteInfo inviteInfo) {
        InvitationProperty properties = authProperties.getInvitation();
        String templateName = properties.getEmailTemplateNameByUserInfo();
        Map<String, Object> params = userEmailHelper.buildInviteUserInfoParams(org, fromUser, inviteInfo);
        boolean result = userEmailHelper.sendEmail(templateName, inviteInfo.getEmail(), params);
        if (result) {
            log.info("成员邀请(用户信息)邮件发送成功，email={}", inviteInfo.getEmail());
        } else {
            log.error("成员邀请(用户信息)邮件发送失败，email={}", inviteInfo.getEmail());
        }
    }

    public boolean bindNotify(String email, String code, String app) {
        InvitationProperty properties = authProperties.getInvitation();
        String templateName = properties.getEmailTemplateNameWechatWork();
        Map<String, Object> params = userEmailHelper.buildBindParams(email, app, properties.getInviteUrl() + code);
        return userEmailHelper.sendEmail(templateName, email, params);
    }

    /**
     * 抛出异常或者返回false
     */
    private boolean inviteError(boolean throwable, String msg) {
        if (throwable) {
            throw new BadRequestException(msg);
        }
        return false;
    }

    /**
     * 保存邀请记录
     */
    private UserInvitation addInvitation(String email, Date expireDate, String fromType, User fromUser, User targetUser) {
        UserInvitation entity = new UserInvitation();
        entity.setOrgId(targetUser.getOrgId());
        entity.setCode(StringHelper.generatorInviteCode(email));
        entity.setUserId(targetUser.getId());
        entity.setFromUserId(fromUser.getId());
        entity.setFormType(fromType);
        entity.setExpireDate(expireDate);
        entity.setStatus(UserInvitationStatus.INIT.ordinal());
        userInvitationRepository.save(entity);
        return entity;
    }

    /**
     * 计算邀请链接的过期时间
     */
    private Date expireInviteDate() {
        Duration duration = authProperties.getInvitation().getExpireTime();
        if (duration == null) {
            duration = Duration.ofHours(72);
        }
        return Date.from(LocalDateTime.now().plusSeconds(duration.toSeconds()).atZone(ZoneOffset.systemDefault()).toInstant());
    }

    /**
     * 判断邀请注册的邮箱是否有重复的
     */
    private void checkRepeat(List<String> emails) {
        List<String> repeat = emails.stream().collect(Collectors.toMap(k -> k, v -> 1, Integer::sum)).entrySet().stream().filter(i -> i.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(repeat)) {
            throw new BadRequestException("邮箱地址重复：" + String.join(",", repeat));
        }
    }

    /**
     * 1 检查邮箱是否存在，员工号是否存在
     * 2 检查额度，已经存在的用户数量（包括 启用中，待激活 状态）
     */
    private UserInviteResponseDto checkExistsAndLimit(Organization org, IUserInviteInfo inviteInfo) {
        UserInviteResponseDto responseDto = new UserInviteResponseDto();
        responseDto.setLimit(userService.maxUserSize(org));
        responseDto.setOverSize(Math.max(0, 1 - userService.remainingUserSize(org)));
        String email = inviteInfo.getEmail();
        if (StringUtils.isNotEmpty(email)) {
            if (!userService.isUniqueInAllOrg("email", email, null)) {
                responseDto.setExistsEmail(List.of(email));
            }
        }
        String employeeNo = inviteInfo.getEmployeeNo();
        if (StringUtils.isNotEmpty(employeeNo)) {
            if (!userService.isUniqueInOrg(org.getId(), "employeeNo", employeeNo, null)) {
                responseDto.setExistsEmployeeNo(List.of(employeeNo));
            }
        }
        return responseDto.isFail();
    }

    /**
     * 1 检查邮箱是否存在，员工号是否存在
     * 2 检查额度，已经存在的用户数量（包括 启用中，待激活 状态）
     */
    private UserInviteResponseDto checkExistsAndLimit(Organization org, List<IUserInviteInfo> inviteInfos) {
        UserInviteResponseDto responseDto = new UserInviteResponseDto();
        responseDto.setLimit(userService.maxUserSize(org));
        responseDto.setOverSize(Math.max(0, 1 - userService.remainingUserSize(org)));
        List<String> emails = inviteInfos.stream().map(IUserInviteInfo::getEmail).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emails)) {
            List<User> users = userService.scopeQuery(EntityScopeStrategyType.NONE, () -> userService.getRepository().findByEmailIn(emails));
            if (CollectionUtils.isNotEmpty(users)) {
                responseDto.setExistsEmail(users.stream().map(User::getEmail).filter(StringUtils::isNoneEmpty).collect(Collectors.toList()));
            }
        }
        List<String> employeeNos = inviteInfos.stream().map(IUserInviteInfo::getEmployeeNo).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(employeeNos)) {
            List<User> users = userService.scopeQuery(EntityScopeStrategyType.NONE, () -> userService.getRepository().findByOrgIdAndEmployeeNoIn(org.getId(), employeeNos));
            if (CollectionUtils.isNotEmpty(users)) {
                responseDto.setExistsEmployeeNo(users.stream().map(User::getEmployeeNo).filter(StringUtils::isNoneEmpty).collect(Collectors.toList()));
            }
        }
        return responseDto.isFail();
    }

    /**
     * 查询邀请注册的激活地址的状态
     */
    @Transactional
    public UserInviteStatusResponseDto inviteStatus(String code) {
        // 验证链接
        if (StringHelper.isVerifyLinkCode(code)) {
            String linkType = StringHelper.getVerifyLinkType(code);
            String key = String.format("verify-link:%s", code);
            Object value = stringRedisTemplate.opsForValue().get(key);
            if (value != null) {
                String v = value.toString();
                if (v.equals("success")) {
                    throw ToastMessageHelper.businessException(AuthToastMessage.LINK_VERIFY_BIND_SUCCESS);
                } else if (NumberUtils.isDigits(v)) {
                    if ("wechat-work".equals(linkType)) {
                        try {
                            long thirdPartyUserId = Long.parseLong(value.toString());
                            ThirdPartyUser thirdPartyUser = thirdPartyUserService.require(thirdPartyUserId);
                            if (thirdPartyUser != null) {
                                User user = activeUser(thirdPartyUser.getUserId(), null);
                                thirdPartyUserService.valid(thirdPartyUser);
                                String refreshToken = refreshTokenProvider.cacheRefreshToken(user.getId(), LoginPlatform.wechatWork);
                                Long expire = stringRedisTemplate.getExpire(key, TimeUnit.SECONDS);
                                if (expire != null && expire > 0) {
                                    stringRedisTemplate.opsForValue().set(key, "success", Duration.ofSeconds(expire));
                                }
                                return UserInviteStatusResponseDto.successBind(refreshToken);
                            }
                        } catch (Throwable e) {
                            throw ToastMessageHelper.businessException(AuthToastMessage.ACCOUNT_MEMBER_DISABLED);
                        }
                    }
                }
            }
            throw ToastMessageHelper.businessException(AuthToastMessage.LINK_VERIFY_BIND_EXPIRE);
        }

        // 激活邀请
        UserInvitation invitation = userInvitationRepository.findFirstByCode(code);
        if (invitation != null && UserInvitationStatus.canNotify(invitation.getStatus()) && invitation.getUserId() != null && invitation.getExpireDate() != null && invitation.getExpireDate().after(new Date())) {
            Organization org = organizationService.requireById(invitation.getOrgId());
            User user = userService.get(invitation.getUserId());
            if (org != null && user != null && UserStatus.isInvite(user.getStatus())) {
                return UserInviteStatusResponseDto.successInvite(user.getEmail(), org.getName());
            }
        }
        throw ToastMessageHelper.businessException(AuthToastMessage.ACCOUNT_INVITATION_DISABLED);
    }

    /**
     * 获得邀请链接和复制文案
     */
    @Transactional
    public UserInviteUrlResponseDto inviteUrl(Long inviteId, String app) {
        UserInvitation invitation = userInvitationRepository.findById(inviteId).orElse(null);
        checkInviteStatus(invitation);
        Organization org = organizationService.requireById(invitation.getOrgId());
        User fromUser = userService.require(invitation.getFromUserId());
        InvitationProperty property = authProperties.getInvitation();
        String inviteUrl = userEmailHelper.domain(app) + property.getInviteUrl() + invitation.getCode();
        String copyText = String.format(property.getCopyText(), fromUser.getTruename(), org.getName(), inviteUrl);
        return new UserInviteUrlResponseDto(inviteUrl, copyText);
    }

    /**
     * 重新发送邀请邮件
     */
    @Transactional
    public UserInvitationDto inviteNotify(Long inviteId, String app) {
        UserInvitation entity = userInvitationRepository.findById(inviteId).orElse(null);
        boolean r = inviteNotify(null, null, entity, null, app, true, true);
        if (r) {
            return mapperService.map(entity, UserInvitationDto.class);
        }
        throw new BadRequestException();
    }

    private void checkInviteStatus(UserInvitation invitation) {
        if (invitation == null || invitation.getExpireDate() == null || invitation.getExpireDate().before(new Date())) {
            throw badRequestException(ACCOUNT_INVITATION_DISABLED);
        }
        if (UserInvitationStatus.isActive(invitation.getStatus())) {
            throw badRequestException(ACCOUNT_MEMBER_ACTIVATED);
        }
        User user = userService.require(invitation.getUserId());
        if (!UserStatus.isInvite(user.getStatus())) {
            throw badRequestException(ACCOUNT_MEMBER_ACTIVATED);
        }
    }

    /**
     * 激活邀请
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean inviteActive(String code, String truename, String password) {
        UserInvitation invitation = userInvitationRepository.findFirstByCode(code);
        checkInviteStatus(invitation);
        User user = activeUser(invitation.getUserId(), u -> {
            u.setTruename(truename);
            u.setPassword(PasswordHelper.encrypt(password));
            u.setPasswordStrength(PasswordHelper.passwordStrength(password));
        });
        invitation.setStatus(UserInvitationStatus.ACTIVE.ordinal());
        userInvitationRepository.save(invitation);
        if ("wechat-work".equals(invitation.getFormType())) {
            thirdPartyUserService.validWechatWorkMember(user.getOrgId(), user.getId());
        }
        return true;
    }

    private User activeUser(Long userId, Consumer<User> buildUser) {
        User user = userService.require(userId);
        if (user == null || !UserStatus.isActiveStatus(user.getStatus())) {
            throw badRequestException(ACCOUNT_MEMBER_DISABLED);
        }
        Optional.ofNullable(buildUser).ifPresent(i -> i.accept(user));
        user.setStatus(ENABLE.getStatus());
        userService.save(user);
        return user;
    }
}
