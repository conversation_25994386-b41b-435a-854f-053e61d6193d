package org.befun.auth.service;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.CasProperty;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.constant.UserStatus;
import org.befun.auth.dto.auth.AuthWhiteListDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyAuthWhiteList;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.repository.ThirdPartyAuthWhiteListRepository;
import org.befun.auth.service.auth.ThirdPartyAuthHelper;
import org.befun.auth.utils.ListHelper;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ThirdPartyAuthType.CAS;

@Service
public class ThirdPartyAuthWhiteListService {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ThirdPartyAuthWhiteListRepository thirdPartyAuthWhiteListRepository;
    @Autowired
    private ThirdPartyAuthHelper thirdPartyAuthHelper;
    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService userService;

    public boolean isInWhiteList(Long thirdPartyAuthId, String openId) {
        return thirdPartyAuthWhiteListRepository.existsByThirdPartyAuthIdAndOpenId(thirdPartyAuthId, openId);
    }

    public AuthWhiteListDto getWhiteList(String type, String app) {
        ThirdPartyAuthType sso = ThirdPartyAuthType.valueOf(type.toUpperCase());
        ThirdPartyAuth auth = thirdPartyAuthHelper.getOrCreateSingleAuth(sso, app);
        if (auth == null) {
            throw new BadRequestException("未设置sso登录方式");
        }
        List<String> openIds = thirdPartyAuthWhiteListRepository.findByThirdPartyAuthId(auth.getId())
                .stream().map(ThirdPartyAuthWhiteList::getOpenId).collect(Collectors.toList());
        return new AuthWhiteListDto(app, openIds);
    }

    public boolean syncWhiteList(String type, String app, List<String> openIds) {
        ThirdPartyAuthType sso = ThirdPartyAuthType.valueOf(type.toUpperCase());
        ThirdPartyAuth auth = thirdPartyAuthHelper.getOrCreateSingleAuth(sso, app);
        if (auth == null) {
            throw new BadRequestException("未设置sso登录方式");
        }
        Long orgId = TenantContext.getCurrentTenant();
        List<ThirdPartyAuthWhiteList> oldList = thirdPartyAuthWhiteListRepository.findByThirdPartyAuthId(auth.getId());
        List<ThirdPartyAuthWhiteList> newList = openIds.stream().map(String::trim).filter(StringUtils::isNotEmpty).map(j -> {
            ThirdPartyAuthWhiteList entity = new ThirdPartyAuthWhiteList();
            entity.setOpenId(j);
            entity.setThirdPartyAuthId(auth.getId());
            entity.setOrgId(orgId);
            return entity;
        }).collect(Collectors.toList());

        List<ThirdPartyAuthWhiteList> del = ListHelper.minus(oldList, newList, ThirdPartyAuthWhiteList::getOpenId);
        List<ThirdPartyAuthWhiteList> add = ListHelper.minus(newList, oldList, ThirdPartyAuthWhiteList::getOpenId);

        if (CollectionUtils.isNotEmpty(del)) {
            thirdPartyAuthWhiteListRepository.deleteAll(del);
        }
        if (CollectionUtils.isNotEmpty(add)) {
            thirdPartyAuthWhiteListRepository.saveAll(add);
        }
        if (enableAutoCreateUser(CAS)) {
            List<ThirdPartyAuthWhiteList> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(add)) {
                list.addAll(add);
            }
            Optional.ofNullable(ListHelper.minus(oldList, del, ThirdPartyAuthWhiteList::getOpenId)).ifPresent(list::addAll);
            if (!list.isEmpty()) {
                autoCreateUserByWhiteList(orgId, app, auth, list); // 自动把白名单中的用户添加进成员列表
            }
        }
        return true;
    }

    private boolean enableAutoCreateUser(ThirdPartyAuthType authType) {
        return Optional.ofNullable(authProperties.getCas())
                .map(CasProperty::isWhiteListAutoCreateUser)
                .orElse(false);
    }

    private void autoCreateUserByWhiteList(Long orgId, String app, ThirdPartyAuth auth, List<ThirdPartyAuthWhiteList> add) {
        if (auth == null || CollectionUtils.isEmpty(add)) {
            return;
        }
        Organization org = organizationService.getById(orgId).orElse(null);
        if (org == null) {
            return;
        }
        String source = auth.getAuthType().buildSource(orgId);
        add.forEach(i -> {
            ThirdPartyUser thirdPartyUser = thirdPartyUserService.getBySourceAppOpenId(source, app, i.getOpenId());
            if (thirdPartyUser == null) {
                // 创建成员
                userService.addUserByThirdParty(org, auth, UserStatus.DISABLE, i.getOpenId(), i.getOpenId(), null, null, null, null);
            }
        });

    }

}
