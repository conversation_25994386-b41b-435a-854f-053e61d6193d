package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.annotations.AddTenantContext;
import org.befun.auth.constant.TenantContext.TenantFields;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.entity.RoleDto;
import org.befun.auth.entity.User;
import org.befun.core.dto.UserPermissions;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OpenTenantContextService {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @AddTenantContext({TenantFields.ISAdmin, TenantFields.SubDepartmentId, TenantFields.Permissions})
    public void addTenantContext() {

    }

    public void addIsAdmin() {
        TenantContext.setCurrentIsAdmin(userService.currentHasSuperAdminRole());
    }

    public void addSubDepartmentIdList() {
        if (TenantContext.getCurrentIsAdmin() == null) {
            addIsAdmin();
        }
        if (TenantContext.getCurrentTenant() == null) {
            addTenant();
        }
        if (TenantContext.getCurrentDepartmentIds() == null) {
            addDepartmentIds();
        }
        TenantContext.setCurrentSubDepartmentIds(departmentService.getSubDepartmentIdList(
                TenantContext.getCurrentTenant(),
                TenantContext.getCurrentUserId()
        ));
    }

    public void addTenant() {
        TenantContext.setCurrentTenant(userService.requireCurrent().getOrgId());
    }

    public void addRoleIds() {
        TenantContext.setCurrentRoleIds(roleService.getByUserId(TenantContext.getCurrentUserId()).stream().map(
                RoleDto::getId).collect(Collectors.toList()));
    }

    public void addDepartmentIds() {
        User user = userService.requireCurrent();
        TenantContext.setCurrentDepartmentIds(new ArrayList<>(user.parseDepartmentIds()));
    }

    public void addIsTop() {
        User user = userService.requireCurrent();
        Set<Long> departmentIds = user.parseDepartmentIds();
        List<DepartmentTreeDto> departments = departmentService.childrenTreeByDepartments(user.getOrgId(), departmentIds);
        boolean isTop = CollectionUtils.isNotEmpty(departments) && departments.stream().anyMatch(i -> i.getPid() == null || i.getPid() == 0);
        TenantContext.setCurrentIsTop(isTop);
    }

    public void addPermissions() {
        TenantContext.setCurrentPermissions(JsonHelper.toObject(userService.getPermissions().getPermissions(), UserPermissions.class));
    }
}
