package org.befun.auth.service;

import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.dto.TreeDto;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;

@Service
public class TreeConvertService {

    public <T, TREE extends TreeDto<TREE>> List<TREE> listToTree(List<T> tree, Function<T, TREE> convert) {
        if (CollectionUtils.isEmpty(tree)) {
            return null;
        }
        Map<Long, TREE> map = new LinkedHashMap<>();
        tree.forEach(item -> {
            Optional.ofNullable(convert.apply(item)).ifPresent(r -> {
                Optional.ofNullable(r.getId()).ifPresent(id -> {
                    map.put(id, r);
                });
            });
        });
        List<TREE> root = new ArrayList<>();
        map.values().forEach(i -> {
            TREE parent;
            if (i.getPid() != null && i.getPid() > 0 && (parent = map.get(i.getPid())) != null) {
                parent.addChild(i);
            } else {
                // 如果pid不在map中，则是root
                root.add(i);
            }
        });
        return root;
    }

    /**
     * 一层一层的遍历
     */
    public <TREE extends TreeDto<TREE>, R> List<R> treeToList(List<TREE> tree, Function<TREE, R> convert) {
        if (CollectionUtils.isEmpty(tree)) {
            return null;
        }
        List<R> list = new ArrayList<>();
        List<TREE> children = tree;
        while (!children.isEmpty()) {
            List<TREE> cache = new ArrayList<>();
            children.forEach(i -> {
                Optional.ofNullable(convert.apply(i)).ifPresent(r -> {
                    list.add(r);
                    List<TREE> sub = i.children();
                    if (CollectionUtils.isNotEmpty(sub)) {
                        cache.addAll(sub);
                    }
                });

            });
            children = cache;
        }
        return list;
    }

    /**
     * 先当前节点，在子节点（从左到右）
     */
    public <TREE extends TreeDto<TREE>, R> List<R> treeToList2(List<TREE> tree, Function<TREE, R> convert) {
        if (CollectionUtils.isEmpty(tree)) {
            return null;
        }
        List<R> list = new ArrayList<>();
        Function<TREE, R> convert2 = t -> {
            R r = convert.apply(t);
            list.add(r);
            return r;
        };
        tree.forEach(node -> iterateTree(node, convert2));
        return list;
    }

    private <TREE extends TreeDto<TREE>, R> R iterateTree(TREE node, Function<TREE, R> convert) {
        R rNode = convert.apply(node);
        List<TREE> children = node.children();
        if (CollectionUtils.isNotEmpty(children)) {
            children.forEach(subNode -> {
                R rSubNode = iterateTree(subNode, convert);
                if (rNode != node && rNode instanceof TreeDto) {
                    ((TreeDto) rNode).addChild(rSubNode);
                }
            });
        }
        return rNode;
    }

    /**
     * 先节点，在子节点（从左到右）
     */
    public <TREE extends TreeDto<TREE>, R extends TreeDto<R>> List<R> treeToTree(List<TREE> tree, Function<TREE, R> convert) {
        if (CollectionUtils.isEmpty(tree)) {
            return null;
        }
        List<R> rTree = new ArrayList<>();
        tree.forEach(node -> rTree.add(iterateTree(node, convert)));
        return rTree;
    }

    public <TREE extends TreeDto<TREE>, K, R> Map<K, R> treeToMap(List<TREE> tree, Function<TREE, K> getKey, Function<TREE, R> convert) {
        if (CollectionUtils.isEmpty(tree)) {
            return null;
        }
        Map<K, R> map = new HashMap<>();
        List<TREE> children = tree;
        while (!children.isEmpty()) {
            List<TREE> cache = new ArrayList<>();
            children.forEach(i -> {
                Optional.ofNullable(convert.apply(i)).ifPresent(r -> {
                    map.put(getKey.apply(i), r);
                    List<TREE> sub = i.children();
                    if (CollectionUtils.isNotEmpty(sub)) {
                        cache.addAll(sub);
                    }
                });

            });
            children = cache;
        }
        return map;
    }

}
