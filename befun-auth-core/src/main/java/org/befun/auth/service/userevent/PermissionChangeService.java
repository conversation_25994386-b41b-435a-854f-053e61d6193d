package org.befun.auth.service.userevent;

import org.befun.core.security.UserEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 可能导致权限变动的地方
 * 1 修改了角色
 * 2 删除了角色
 * 3 修改了用户的角色
 */
@Service
public class PermissionChangeService implements UserEvent {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public StringRedisTemplate redisTemplate() {
        return stringRedisTemplate;
    }

    @Override
    public String key() {
        return "EVENT_PERMISSION_CHANGE";
    }

    @Async
    public void listenerRoleChange(Long roleId) {

    }

    @Async
    public void listenerUserRoleChange(Long userId) {

    }

    @Async
    public void listenerUserRoleChange(List<Long> userIds) {

    }


}
