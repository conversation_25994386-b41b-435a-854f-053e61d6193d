package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.entity.OrganizationStat;
import org.befun.auth.entity.OrganizationStatDto;
import org.befun.auth.repository.OrganizationStatRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Service
public class OrganizationStatService extends BaseService<OrganizationStat, OrganizationStatDto, OrganizationStatRepository> {

    @Transactional
    public void updateLastLogin(Long orgId, Long userId) {
        OrganizationStat entity = repository.findFirstByOrgId(orgId);
        if (entity == null) {
            entity = new OrganizationStat();
            entity.setOrgId(orgId);
        }
        entity.setLastLoginUserId(userId);
        entity.setLastLoginTime(new Date());
        repository.save(entity);
    }
}
