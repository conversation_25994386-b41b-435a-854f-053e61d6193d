package org.befun.auth.service.orgconfig;

import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;

public interface BaseOrgConfigService {

    OrganizationConfigType type();

    OrgConfigDto getDefaultConfig();

    void checkConfig(OrgConfigDto data);

    OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config);

    void copyConfig(OrgConfigDto source, OrgConfigDto target);

    /**
     * 由于关联的配置项有变化，所有在查询出配置后，需要做出适当的调整
     */
    default void correction(OrgConfigDto data) {

    }
}
