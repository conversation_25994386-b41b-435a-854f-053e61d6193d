package org.befun.auth.service;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyAuthDto;
import org.befun.auth.repository.ThirdPartyAuthRepository;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ThirdPartyAuthService extends BaseService<ThirdPartyAuth, ThirdPartyAuthDto, ThirdPartyAuthRepository> {

    // ****************************************************************
    // **************************** 登录状态 ****************************
    // ****************************************************************

    public ThirdPartyAuth getSingleAuth(ThirdPartyAuthType type, String app) {
        return repository.findFirstByAuthTypeAndApp(type, app);
    }

    public List<ThirdPartyAuth> getListAuth(ThirdPartyAuthType type, String app) {
        return repository.findByAuthTypeAndApp(type, app);
    }

    public ThirdPartyAuth getBySourceWithLogin(ThirdPartyAuthType type, String source, String app) {
        return repository.findFirstBySourceAndAuthTypeAndApp(source, type, app);
    }

    public long countAuth(ThirdPartyAuthType type, String app) {
        return repository.countByAuthTypeAndApp(type, app);
    }

    // ****************************************************************
    // *************************** 未登录状态 ***************************
    // ****************************************************************

    public List<ThirdPartyAuth> getListBySource(String source, ThirdPartyAuthType authType, String app) {
        return scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findBySourceAndAuthTypeAndApp(source, authType, app));
    }

    public ThirdPartyAuth getBySource(String source, ThirdPartyAuthType authType, String app) {
        return scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findFirstBySourceAndAuthTypeAndApp(source, authType, app));
    }

    public ThirdPartyAuth getByOrg(long orgId, ThirdPartyAuthType authType, String app) {
        return scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findFirstByOrgIdAndAuthTypeAndApp(orgId, authType, app));
    }

    public List<ThirdPartyAuth> getListByOrg(long orgId, ThirdPartyAuthType authType, String app) {
        return scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findByOrgIdAndAuthTypeAndApp(orgId, authType, app));
    }
}
