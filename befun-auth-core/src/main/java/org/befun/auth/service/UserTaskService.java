package org.befun.auth.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.dto.usertask.UserTaskDto;
import org.befun.auth.dto.usertask.UserTaskQueryDto;
import org.befun.auth.dto.usertask.UserTaskResultDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.befun.task.entity.TaskProgress;
import org.befun.task.service.TaskProgressService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
public class UserTaskService extends TaskProgressService {

    public Page<UserTaskDto> findAll(UserTaskQueryDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        List<UserTaskType> types = EnumHelper.parseList(UserTaskType.values(), dto.getType_in());
        List<TaskStatus> statuses = EnumHelper.parseList(TaskStatus.values(), dto.getStatus_in());
        ResourceEntityQueryDto<UserTaskDto> queryDto = dto.transform();
        Pageable pageable = PageRequest.of(dto.getPage() - 1, dto.getLimit(), queryDto.getSorts());
        Page<TaskProgress> page = repository.findAll((root, query, build) -> {
            List<Predicate> where = new ArrayList<>();
            where.add(build.equal(root.get("orgId"), orgId));
            if (CollectionUtils.isNotEmpty(types)) {
                where.add(root.get("type").in(types.stream().map(Enum::name).collect(Collectors.toList())));
            }
            if (CollectionUtils.isNotEmpty(statuses)) {
                where.add(root.get("status").in(statuses));
            }
            return build.and(where.toArray(new Predicate[0]));
        }, pageable);
        if (page.hasContent()) {
            List<UserTaskDto> list = page.getContent().stream().map(UserTaskDto::new).collect(Collectors.toList());
            return new PageImpl<>(list, pageable, page.getTotalElements());
        }
        return Page.empty();
    }

    public List<TaskProgress> allTask(Long orgId, Long userId, UserTaskType type, Long relationId) {
        return getAllByType(orgId, userId, type, relationId);
    }

    public Optional<TaskProgress> lastTask(Long orgId, Long userId, UserTaskType type, Long relationId) {
        return getLastByType(orgId, userId, type, relationId);
    }

    public Optional<TaskProgress> lastTask(Long orgId, Long userId, UserTaskType type) {
        return getLastByType(orgId, userId, type);
    }

    public boolean lastTaskIsCompleted(Long orgId, Long userId, UserTaskType type) {
        return getLastByType(orgId, userId, type).map(TaskProgress::getStatus).map(TaskStatus::isCompleted).orElse(true);
    }

    public TaskProgress createTask(Long orgId, Long userId, UserTaskType type, int totalSize, Object params) {
        return super.createTask(orgId, userId, type, totalSize, params);
    }

    public TaskProgress createTask(Long orgId, Long userId, UserTaskType type, int totalSize, Object params, Long relationId) {
        return super.createTask(orgId, userId, type, totalSize, params, relationId);
    }

    public TaskProgress resetTask(Long id, Object params) {
        return super.updateTask(id, TaskStatus.INIT, oldParams -> {
            if (params != null) {
                List<Object> parsedParams = parseParams(oldParams);
                parsedParams.add(params);
                return JsonHelper.toJson(parsedParams);
            }
            return oldParams;
        }, null);
    }

    public TaskProgress failedTask(Long id, String errorMessage) {
        return super.updateTask(id, TaskStatus.FAILED, null, oldResult -> {
            if (StringUtils.isNotEmpty(errorMessage)) {
                UserTaskResultDto result = parseResult(oldResult);
                result.getErrorMessages().add(errorMessage);
                return JsonHelper.toJson(true, result);
            }
            return oldResult;
        });
    }

    public TaskProgress successTask(Long id, Consumer<UserTaskResultDto> setResult) {
        return super.updateTask(id, TaskStatus.SUCCESS, null, oldResult -> {
            if (setResult != null) {
                UserTaskResultDto result = parseResult(oldResult);
                setResult.accept(result);
                return JsonHelper.toJson(true, result);
            }
            return oldResult;
        });
    }

    private List<Object> parseParams(String paramsText) {
        List<Object> params = null;
        if (StringUtils.isNotEmpty(paramsText)) {
            params = JsonHelper.toList(paramsText, Object.class);
        }
        if (params == null) {
            params = new ArrayList<>();
        }
        return params;
    }

    private UserTaskResultDto parseResult(String resultText) {
        UserTaskResultDto result = null;
        if (StringUtils.isNotEmpty(resultText)) {
            result = JsonHelper.toObject(resultText, UserTaskResultDto.class);
        }
        if (result == null) {
            result = new UserTaskResultDto();
        }
        List<String> errorMessages = result.getErrorMessages();
        if (errorMessages == null) {
            errorMessages = new ArrayList<>();
            result.setErrorMessages(errorMessages);
        }
        return result;
    }
}
