package org.befun.auth.service;

import org.befun.auth.dto.role.MenuTreeDto;
import org.befun.auth.entity.Menu;
import org.befun.auth.repository.MenuRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MenuService {

    @Autowired
    private MenuRepository menuRepository;
    @Autowired
    private TreeConvertService treeConvertService;

    /**
     * 获得所有 menu 的列表
     */
    public List<Menu> getAllMenus() {
        return menuRepository.findAll(Sort.by(Sort.Order.by("sequence")));
    }

    /**
     * 获得 key 为 fullPath value 为 menu 的 map 结构
     *
     * @return notnull
     */
    public Map<String/**/, Menu> getMenuMap() {
        return Optional.ofNullable(getAllMenus()).map(l -> l.stream().collect(Collectors.toMap(Menu::getFullPath, i -> i, (o1, o2) -> o1))).orElse(new HashMap<>());
    }

    /**
     * 获得所有 menu 的 tree 结构
     */
    public List<MenuTreeDto> tree() {
        return treeConvertService.listToTree(getAllMenus(), MenuTreeDto::formMenu);
    }

    /**
     * 获得所有 menu 的 tree 结构，默认全部是选中的
     */
    public List<MenuTreeDto> treeDefaultSelect() {
        List<MenuTreeDto> tree = tree();
        selectMenu(tree);
        return tree;
    }

    private void selectMenu(List<MenuTreeDto> menus) {
        Optional.ofNullable(menus).ifPresent(l -> l.forEach(i -> {
            i.setSelected(true);
            selectMenu(i.getSubMenus());
        }));
    }
}