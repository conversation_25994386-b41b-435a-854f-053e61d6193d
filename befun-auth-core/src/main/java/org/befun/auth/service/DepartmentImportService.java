package org.befun.auth.service;

import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.dto.DepartmentImportDto;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.entity.Department;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.exception.BusinessException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.property.TemplateFileProperty;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DepartmentImportService {

    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private IAuthEventTrigger authEventTrigger;
    @Autowired(required = false)
    private TemplateFileProperty templateFileProperty;

    public void download(HttpServletResponse response) {
        Long orgId = TenantContext.getCurrentTenant();
        String fileUrl = "";
        if (this.templateFileProperty != null && this.templateFileProperty.isEnable()) {
            fileUrl = this.templateFileProperty.getItems()
                    .stream().filter((i) -> "department-import-template".equals(i.getType()))
                    .findFirst()
                    .map(TemplateFileProperty.TemplateFileItem::getFileUrl)
                    .orElse(null);
        }
        DepartmentTreeDto root = departmentService.treeByOrg(orgId);
        if (root == null) {
            return;
        }
        InputStream is = null;
        if (StringUtils.isNotEmpty(fileUrl)) {
            try {
                is = new ByteArrayInputStream(Request.Get(fileUrl).execute().returnResponse().getEntity().getContent().readAllBytes());
            } catch (IOException e) {
                log.warn("未获取部门默认模板 {}", e.getMessage());
            }
        }
        List<DepartmentTreeDto> list = departmentService.treeToList2(root, Function.identity());
        Map<Long/*id*/, DepartmentTreeDto> nameMap = list.stream().collect(Collectors.toMap(DepartmentTreeDto::getId, i -> i));
        List<DepartmentImportDto> data = list.stream().map(i -> {
            DepartmentImportDto dto = new DepartmentImportDto();
            dto.setId(i.getId() + "");
            dto.setCode(i.getCode());
            List<String> namePath = new ArrayList<>();
            departmentService.parseFullPath(null, namePath, nameMap, i);
            dto.setName(String.join("/", namePath));
            return dto;
        }).collect(Collectors.toList());
        try (OutputStream os = response.getOutputStream()) {
            // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("组织架构" + LocalDate.now(), StandardCharsets.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            if (is == null) {
                EasyExcel.write(os, DepartmentImportDto.class).sheet().doWrite(data);
            } else {
                EasyExcel.write(os).withTemplate(is).sheet().doFill(data);
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public List<DepartmentImportDto> check(MultipartFile file) {
        Long orgId = TenantContext.getCurrentTenant();
        return sync0(true, orgId, file);
    }

    @Transactional
    public boolean sync(MultipartFile file) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        sync0(false, orgId, file);
        departmentService.clearCache(orgId);
        departmentService.updateParentList(orgId);
        authEventTrigger.departmentRefresh(orgId, userId);
        return true;
    }


    private List<DepartmentImportDto> sync0(boolean check, Long orgId, MultipartFile file) {
        try (InputStream is = file.getInputStream()) {
            ZipSecureFile.setMinInflateRatio(0);
            List<DepartmentImportDto> lines = EasyExcel.read(is).head(DepartmentImportDto.class).sheet().doReadSync();

            DepartmentTreeDto root = currentRoot(orgId);

            Map<String, DepartmentTreeDto> codeMap = new HashMap<>();
            Map<Long, DepartmentTreeDto> idMap = departmentService.treeToList2(root, i -> {
                String code = i.getCode().toString();
                if (StringUtils.isNotEmpty(code)) {
                    codeMap.put(code, i);
                }
                return i;
            }).stream().collect(Collectors.toMap(DepartmentTreeDto::getId, Function.identity()));

            List<DepartmentImportDto> validData = checkImportData(codeMap, lines);
            lines = validData.stream().sorted(Comparator.comparing(o -> o.level)).collect(Collectors.toList());

            List<DepartmentTreeDto> update = new ArrayList<>();
            List<DepartmentTreeDto> add = new ArrayList<>();

            DepartmentImportDto first = lines.get(0);
            boolean updateRoot = false;
            if (!first.rootName.equals(root.getTitle())) {
                root.setTitle(first.rootName);
                updateRoot = true;
            }
            if (first.isRoot && !Objects.equals(first.getCode(), root.getCode())) {
                root.setCode(first.getCode());
                updateRoot = true;
                first.setRowEdit(1);
            }
            if (updateRoot) {
                update.add(root);
                DepartmentTreeDto rootParent = new DepartmentTreeDto();
                rootParent.setId(0L);
                root.setParent(rootParent);
            }

            for (DepartmentImportDto row : lines) {
                if (row.isRoot) {
                    continue;
                }
                Long id = row.parseId();
                int i = 0;
                DepartmentTreeDto lastParent = root;
                while (row.parentNames.size() > i) {
                    String parentName = row.parentNames.get(i);
                    DepartmentTreeDto find = lastParent.children().stream().filter(node -> parentName.equals(node.getTitle())).findFirst().orElse(null);
                    if (find == null) {
                        find = new DepartmentTreeDto(null, parentName, null);
                        add.add(find);
                        addChild(lastParent, find);
                        find.setParent(lastParent);
                    }
                    lastParent = find;
                    i++;
                }
                DepartmentTreeDto current;
                if (id != null) {
                    current = idMap.get(id);
                    if (current != null) {
                        boolean updateRow = false;
                        // 当前部门存在，则先比较一下是否修改了名称和编号
                        if (change(row.leafName, row.getCode(), current)) {
                            updateRow = true;
                        }
                        if (!current.getPid().equals(lastParent.getId())) {
                            updateRow = true;
                            Optional.ofNullable(idMap.get(current.getPid())).ifPresent(oldParent -> {
                                oldParent.children().removeIf(c -> c.getId().equals(id));
                            });
                            addChild(lastParent, current);
                        }
                        if (updateRow) {
                            update.add(current);
                            current.setParent(lastParent);
                            row.setRowEdit(1);
                        }
                    } else {
                        addNode(row, add, lastParent);
                    }
                } else {
                    addNode(row, add, lastParent);
                }
            }
            departmentService.calcLevel(1, List.of(root));
            List<DepartmentTreeDto> list = departmentService.treeToList2(root, Function.identity());
            if (list.stream().anyMatch(i -> i.getLevel() != null && i.getLevel() > 5)) {
                throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_LEVEL_LIMIT);
            }
            if (!check) {
                if (!add.isEmpty()) {
                    add.forEach(i -> saveCurrent(orgId, i));
                }
                if (!update.isEmpty()) {
                    update.forEach(i -> departmentService.updateDepartmentNoCheck(i.getId(), i.getTitle(), i.getParent().getId(), i.getCode()));
                }
            }
            return validData;
        } catch (BusinessException e) {
            log.error("导入部门错误", e);
            throw e;
        } catch (Exception e) {
            log.error("导入部门错误", e);
            throw ToastMessageHelper.badRequestException(AuthToastMessage.DEPARTMENT_UPLOAD_FILE_ERROR);
        }
    }

    private void addNode(DepartmentImportDto row, List<DepartmentTreeDto> add, DepartmentTreeDto lastParent) {
        DepartmentTreeDto current = new DepartmentTreeDto(null, row.leafName, row.getCode());
        add.add(current);
        addChild(lastParent, current);
        current.setParent(lastParent);
        row.setRowEdit(2);
    }

    private Long saveCurrent(Long orgId, DepartmentTreeDto current) {
        if (current.getId() != null) {
            return null;
        }
        DepartmentTreeDto parent = current.getParent();
        if (parent == null) {
            return null;
        }
        Long parentId = parent.getId();
        if (parentId == null) {
            parentId = saveCurrent(orgId, parent);
            parent.setId(parentId);
        }
        if (parentId != null) {
            Department department = departmentService.addDepartmentNoCheck(orgId, current.getTitle(), parentId, current.getCode());
            current.setId(department.getId());
        }
        return parentId;
    }

    private DepartmentTreeDto currentRoot(Long orgId) {
        DepartmentTreeDto root = departmentService.treeByOrg(orgId);
        if (root == null) {
            departmentService.addDepartmentNoCheck(orgId, "一级部门", 0L, null);
            return departmentService.treeByOrg(orgId);
        }
        return root;
    }

    private boolean change(String newTitle, List<String> newCode, DepartmentTreeDto exist) {
        boolean update = false;
        if (!Objects.equals(newTitle, exist.getTitle())) {
            exist.setTitle(newTitle);
            update = true;
        }
        if (!Objects.equals(newCode, exist.getCode())) {
            exist.setCode(newCode);
            update = true;
        }
        return update;
    }

    private void addChild(DepartmentTreeDto parent, DepartmentTreeDto child) {
        if (parent.children().stream().anyMatch(i -> i.getTitle().equals(child.getTitle()))) {
            throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_SAME_NAME);
        }
        parent.addChild(child);
    }

    /**
     * 删除无效的数据
     */
    private List<DepartmentImportDto> checkImportData(Map<String, DepartmentTreeDto> codeMap, List<DepartmentImportDto> data) {
        // 无数据
        if (CollectionUtils.isEmpty(data)) {
            throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_UPLOAD_FILE_ERROR);
        }
        List<DepartmentImportDto> validData = new ArrayList<>();
        // 不能有重复的id和code
        Set<Long> idSet = new HashSet<>();
        Set<String> codeSet = new HashSet<>();
        Set<String> rootNames = new HashSet<>();
        data.forEach(i -> {
            Long id = i.parseId();
            String code = JsonHelper.toJson(i.getCode());
            if (StringUtils.isNotEmpty(code)) {
                if (codeSet.contains(code)) {
                    throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_SAME_CODE);
                } else {
                    DepartmentTreeDto existCode = codeMap.get(code);
                    if (existCode != null) {
                        if (id != null && !id.equals(existCode.getId())) {
                            // 这条数据有id，并且和已存在的部门id不同，
                            throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_SAME_CODE);
                        } else if (id == null) {
                            // 补齐这条数据的id
                            i.setId(existCode.getId() + "");
                            id = existCode.getId();
                        }
                    }
                    codeSet.add(code);
                }
            }
            if (id != null) {
                if (idSet.contains(id)) {
                    throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_SAME_ID);
                } else {
                    idSet.add(id);
                }
            }
            if (i.parseName()) {
                validData.add(i);
                rootNames.add(i.getRootName());
            }
        });
        if (rootNames.size() > 1) {
            throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_MULTI_ROOT);
        }
        if (CollectionUtils.isEmpty(validData)) {
            throw ToastMessageHelper.businessException(AuthToastMessage.DEPARTMENT_UPLOAD_FILE_ERROR);
        }
        return validData;
    }
}