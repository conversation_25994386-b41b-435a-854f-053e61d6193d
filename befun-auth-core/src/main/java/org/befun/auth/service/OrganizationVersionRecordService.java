package org.befun.auth.service;

import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.OrgVersionRecordStatus;
import org.befun.auth.entity.OrganizationVersionRecord;
import org.befun.auth.entity.OrganizationVersionRecordDto;
import org.befun.auth.repository.OrganizationVersionRecordRepository;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class OrganizationVersionRecordService extends BaseService<OrganizationVersionRecord, OrganizationVersionRecordDto, OrganizationVersionRecordRepository> {


    public OrganizationVersionRecord getLastVersionRecord(Long orgId) {
        return repository.findFirstByOrgIdAndStatusOrderByCreateTimeDesc(orgId, OrgVersionRecordStatus.success);
    }

    public List<OrganizationVersionRecord> getByStatus(Long orgId, OrgVersionRecordStatus status) {
        return repository.findByOrgIdAndStatus(orgId, status);
    }

    /**
     * 添加版本购买记录
     */
    public OrganizationVersionRecord add(Long orgId, Long userId, AppVersion version, String type, Integer price, Integer costAmount, LocalDate start, LocalDate end) {
        OrganizationVersionRecord entity = new OrganizationVersionRecord();
        entity.setOrgId(orgId);
        entity.setCreateUserId(userId);
        entity.setVersion(version);
        entity.setType(type);
        entity.setPrice(price);
        entity.setCostAmount(costAmount);
        entity.setStartDate(DateHelper.toDate(start));
        entity.setEndDate(DateHelper.toDate(end));
        entity.setStatus(OrgVersionRecordStatus.init);
        repository.save(entity);
        return entity;
    }

    public void updateStatus(OrganizationVersionRecord record, OrgVersionRecordStatus status) {
        if (record != null) {
            record.setStatus(status);
            repository.save(record);
        }
    }
}
