package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.AppType;
import org.befun.auth.dto.IUserInviteInfo;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserInvitation;
import org.befun.extension.service.MailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class UserEmailHelper {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService userService;
    @Autowired
    private MailService mailService;
    @Value("${xmplus.domain:}")
    private String xmplusDomain;
    @Value("${surveyplus.domain:}")
    private String surveyplusDomain;

    public String domain(String app) {
        if (AppType.surveyplus.name().equals(app)) {
            return surveyplusDomain;
        }
        return xmplusDomain;
    }

    /**
     * 必须的参数
     * email        邮箱地址
     * orgName      企业名称
     * truename     邀请人姓名
     * inviteUrl    激活地址
     */
    public Map<String, Object> buildInviteParams(@Nullable Organization org, User target, User from, UserInvitation invitation, String app, String inviteUrl) {
        if (org == null) {
            org = organizationService.requireById(invitation.getOrgId());
        }
        if (from == null) {
            from = userService.require(invitation.getFromUserId());
        }
        Map<String, Object> params = new HashMap<>();
        params.put("email", target.getEmail());
        params.put("truename", from.getTruename());
        params.put("orgName", org.getName());
        params.put("inviteUrl", domain(app) + inviteUrl + invitation.getCode());
        return params;
    }

    /**
     * 必须的参数
     * email        邮箱地址
     * inviteUrl    激活地址
     */
    public Map<String, Object> buildBindParams(String email, String app, String inviteUrl) {
        Map<String, Object> params = new HashMap<>();
        params.put("email", email);
        params.put("inviteUrl", domain(app) + inviteUrl);
        return params;
    }

    /**
     * 必须的参数
     * email        邮箱地址
     * loginUrl    激活地址
     */
    public Map<String, Object> buildInviteUserInfoParams(Organization org, User fromUser, IUserInviteInfo inviteInfo) {
        Map<String, Object> params = new HashMap<>();
        params.put("orgName", org.getName());
        params.put("truename", fromUser.getTruename());
        params.put("targetTrueName", inviteInfo.getTruename());
        params.put("email", inviteInfo.getEmail());
        params.put("password", inviteInfo.getPassword());
        params.put("loginUrl", domain(inviteInfo.getApp()) + "/cem/login");
        return params;
    }

    public boolean sendEmail(String templateName, String email, Map<String, Object> params) {
        try {
            mailService.sendMessageByTemplate(templateName, email, params);
            return true;
        } catch (RuntimeException e) {
            log.error("邮件发送失败：templateName={}， email={}", templateName, email, e);
        }
        return false;
    }
}
