package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.security.saml2.provider.service.registration.Saml2MessageBinding;

@Getter
@Setter
public class SamlConfig extends AbstractConfig {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "退出地址")
    private String logoutRedirectUrl = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "回调方式：POST，REDIRECT", required = true)
    private Saml2MessageBinding binding = Saml2MessageBinding.POST;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "idp元数据地址")
    private String idpMetadataUrl;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "idp元数据（xml内容）")
    private String idpMetadata;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：id", required = true)
    private String paramId = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：姓名")
    private String paramName = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：部门")
    private String paramDepartment = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：手机")
    private String paramMobile = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：邮箱")
    private String paramEmail = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：员工号")
    private String paramEmployeeNo = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：角色编号")
    private String paramRoleCode = "";

}
