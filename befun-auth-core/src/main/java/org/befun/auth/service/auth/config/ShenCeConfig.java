package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class ShenCeConfig extends AbstractConfig implements IClearConfigSecret {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接口地址", required = true)
    private String url;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "鉴权模式：apiSecret（需要参数 url,projectName,apiSecret,） | accessToken（默认模式，需要参数 url, accessToken）", required = true)
    private String authMode = "accessToken";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "项目名称")
    private String projectName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "apiSecret")
    private String apiSecret;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "accessToken")
    private String accessToken;

    @NotNull
    @Min(0)
    @Max(2)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "配置状态：0 未配置 1 正常 2 异常。(如果状态为 1 则其他配置项不能为空)")
    private Integer status = 0;

    public boolean checkConfig() {
        if (status != null && status == 1) {
            if ("accessToken".equals(authMode)) {
                return StringUtils.isNotEmpty(accessToken);
            } else if ("apiSecret".equals(authMode)) {
                return StringUtils.isNotEmpty(projectName) && StringUtils.isNotEmpty(apiSecret);
            }
            return false;
        }
        return true;
    }
}

