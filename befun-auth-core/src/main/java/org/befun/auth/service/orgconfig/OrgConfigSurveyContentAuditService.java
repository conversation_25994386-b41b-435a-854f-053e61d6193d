package org.befun.auth.service.orgconfig;

import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.springframework.stereotype.Service;

@Service
public class OrgConfigSurveyContentAuditService implements BaseOrgConfigService {

    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.surveyContentAudit;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        config.setSurveyContentAudit(true);
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {

    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        return null;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setSurveyContentAudit(source.getSurveyContentAudit());
    }

}
