package org.befun.auth.service.orgconfig;

import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.*;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrgConfigSurveyVerifyService implements BaseOrgConfigService {

    @Autowired
    private AuthProperties authProperties;


    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.surveyVerify;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        Boolean defaultConfig = Boolean.valueOf(authProperties.getOrgDefaultConfig().getOrDefault(type().name(), "false"));
        config.setSurveyVerify(defaultConfig);
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {

    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        OrgConfigBuilderDto builder = new OrgConfigBuilderDto();
        Boolean defaultConfig = Boolean.valueOf(authProperties.getOrgDefaultConfig().getOrDefault(type().name(), "false"));
        builder.setSurveyVerify(defaultConfig);
        return builder;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setSurveyVerify(source.getSurveyVerify());
    }

}
