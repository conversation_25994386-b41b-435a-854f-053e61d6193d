package org.befun.auth.service.auth;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.EmailSenderAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.EmailSenderConfig;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.Properties;

import static org.befun.auth.constant.ThirdPartyAuthType.EMAIL_SENDER;

@Slf4j
@Service
public class AuthEmailSenderService extends BaseAuthService<EmailSenderConfig, EmailSenderAuthDto> {

    @Autowired
    private PasswordHelper passwordHelper;

    @Override
    public ThirdPartyAuthType getAuthType() {
        return EMAIL_SENDER;
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, EmailSenderAuthDto dto) {
        allAuthDto.setEmailSender(dto);
    }

    @Override
    public void checkConfig(EmailSenderConfig config) {
        if (!testConfig(config)) {
            throw new BadRequestException("无法连接邮件服务器，请检查邮箱设置内容！");
        }
    }

    @Override
    @Transactional
    public boolean deleteSingle(String app) {
        ThirdPartyAuth entity = getSingleEntity(app);
        if (entity == null) {
            throw new BadRequestException("有邮箱未配置");
        }
        // 删除 third party auth
        thirdPartyAuthService.delete(entity);
        return true;
    }

    public boolean testConfig(EmailSenderConfig config) {
        config.setPassword(passwordHelper.decryptRsa(config.getEncryptedPassword()));
        return mailSender(config).map(i -> {
            try {
                i.testConnection();
                return true;
            } catch (Throwable e) {
                return false;
            }
        }).orElse(true);
    }

    public Optional<JavaMailSenderImpl> mailSender(EmailSenderConfig config) {
        if (config.isEnable()) {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(config.getHost());
            mailSender.setPort(config.getPort());

            mailSender.setUsername(config.getUsername());
            mailSender.setPassword(config.getPassword());

            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.smtp.connectiontimeout", 10000);
            props.put("mail.smtp.timeout", 10000);
            props.put("mail.smtp.auth", true);
            props.put("mail.smtp.starttls.enable", true);
            if (config.getEnableSSL() != null && config.getEnableSSL()) {
                props.put("mail.smtp.ssl.enable", true);
            }
            if (!config.getConfigs().isEmpty()) {
                config.getConfigs().forEach(i -> {
                    String[] as = i.split("=");
                    if (as.length == 2) {
                        props.put(as[0], as[1]);
                    }
                });
            }
            mailSender.setJavaMailProperties(props);
            return Optional.of(mailSender);
        }
        return Optional.empty();
    }

    public EmailSenderConfig getConfig(Long orgId) {
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, EMAIL_SENDER, "cem");
        if (auth == null) {
            return null;
        }
        EmailSenderConfig config = getConfig(auth);
        if (config == null || !config.isEnable()) {
            return null;
        }
        return config;
    }

    public boolean emailSenderEnable(Long orgId) {
        return getConfig(orgId) != null;
    }

}
