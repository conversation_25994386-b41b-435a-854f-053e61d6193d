package org.befun.auth.service;

import org.befun.auth.constant.SmsRecordStatus;
import org.befun.auth.constant.SmsRecordType;
import org.befun.auth.dto.ext.OrganizationSmsRecordExtDto;
import org.befun.auth.entity.OrganizationSmsRecord;
import org.befun.auth.entity.OrganizationSmsRecordDto;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.OrganizationSmsRecordRepository;
import org.befun.core.dto.fillvalue.FillValue;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrganizationSmsRecordService extends BaseService<OrganizationSmsRecord, OrganizationSmsRecordDto, OrganizationSmsRecordRepository> {

    @Autowired
    private UserService userService;

    @Override
    public void afterMapToDto(List<OrganizationSmsRecord> entity, List<OrganizationSmsRecordDto> dto) {
        userService.fillValueById(dto, SimpleUser::fromUser, FillValue.create(OrganizationSmsRecordDto::getCreateUserId, OrganizationSmsRecordExtDto::setCreateUser));
    }

    /**
     * 添加短信消费记录-充值短信
     */
    public OrganizationSmsRecord addByRecharge(Long orgId, Long userId, Integer sms) {
        OrganizationSmsRecord entity = new OrganizationSmsRecord();
        entity.setOrgId(orgId);
        entity.setType(SmsRecordType.recharge);
        entity.setAmount(sms);
        entity.setStatus(SmsRecordStatus.init);
        entity.setCreateUserId(userId);
        repository.save(entity);
        return entity;
    }

    public void rechargeSuccess(Long recordId, Integer balance) {
        OrganizationSmsRecord entity = get(recordId);
        if (entity != null) {
            entity.setBalance(balance);
            entity.setStatus(SmsRecordStatus.success);
            repository.save(entity);
        }
    }

    public void rechargeFailure(Long recordId) {
        OrganizationSmsRecord entity = get(recordId);
        if (entity != null) {
            entity.setStatus(SmsRecordStatus.failure);
            repository.save(entity);
        }
    }

    public void rechargeCancel(Long recordId) {
        OrganizationSmsRecord entity = get(recordId);
        if (entity != null) {
            entity.setStatus(SmsRecordStatus.cancel);
            repository.save(entity);
        }
    }


    /**
     * 添加短信消费记录-发送短信任务预扣的数量
     */
    public OrganizationSmsRecord addBySmsTaskSend(Long orgId, Long userId, Long taskProgressId, String content, Integer planCost,Integer balance) {
        OrganizationSmsRecord entity = new OrganizationSmsRecord();
        entity.setOrgId(orgId);
        entity.setType(SmsRecordType.send);
        entity.setTaskProgressId(taskProgressId);
        entity.setAmount(planCost);
        entity.setBalance(balance);
        entity.setContent(content);
        entity.setStatus(SmsRecordStatus.success);
        entity.setCreateUserId(userId);
        repository.save(entity);
        return entity;
    }

    /**
     * 添加短信消费记录-发送短信任务扣除的数量
     */
    public OrganizationSmsRecord addBySmsTaskSendBySource(Long orgId, Long userId, String source, String content, Integer planCost,Integer balance) {
        OrganizationSmsRecord entity = new OrganizationSmsRecord();
        entity.setOrgId(orgId);
        entity.setType(SmsRecordType.send);
        entity.setSource(source);
        entity.setAmount(planCost);
        entity.setBalance(balance);
        entity.setContent(content);
        entity.setStatus(SmsRecordStatus.success);
        entity.setCreateUserId(userId);
        repository.save(entity);
        return entity;
    }


    /**
     * 添加短信消费记录-发送短信任务退还预扣的数量
     */
    public OrganizationSmsRecord addBySmsTaskSendBack(Long orgId, Long userId, Long taskProgressId, Integer backCost,Integer balance) {
        OrganizationSmsRecord source = repository.findFirstByOrgIdAndTypeAndTaskProgressId(orgId, SmsRecordType.send, taskProgressId).orElse(null);
        String content = source == null ? null : source.getContent();
        OrganizationSmsRecord entity = new OrganizationSmsRecord();
        entity.setOrgId(orgId);
        entity.setType(SmsRecordType.send_back);
        entity.setTaskProgressId(taskProgressId);
        entity.setAmount(backCost);
        entity.setBalance(balance);
        entity.setContent(content);
        entity.setStatus(SmsRecordStatus.success);
        entity.setCreateUserId(userId);
        repository.save(entity);
        return entity;
    }

    /**
     * 添加短信消费记录-发送短信任务补足预扣的数量
     */
    public OrganizationSmsRecord addBySmsTaskSendAppend(Long orgId, Long userId, Long taskProgressId, Integer appendCost,Integer balance) {
        OrganizationSmsRecord source = repository.findFirstByOrgIdAndTypeAndTaskProgressId(orgId, SmsRecordType.send, taskProgressId).orElse(null);
        String content = source == null ? null : source.getContent();
        OrganizationSmsRecord entity = new OrganizationSmsRecord();
        entity.setOrgId(orgId);
        entity.setType(SmsRecordType.send_append);
        entity.setTaskProgressId(taskProgressId);
        entity.setAmount(appendCost);
        entity.setBalance(balance);
        entity.setContent(content);
        entity.setStatus(SmsRecordStatus.success);
        entity.setCreateUserId(userId);
        repository.save(entity);
        return entity;
    }
}
