package org.befun.auth.service.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.client.fluent.Request;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.BaiduTongjiAuthDto;
import org.befun.auth.dto.auth.baidu.BaiduTongjiAuthSiteDto;
import org.befun.auth.dto.auth.baidu.BaiduTongjiAuthUrlDto;
import org.befun.auth.dto.linker.LinkerBaiduTongjiParamDto;
import org.befun.auth.dto.linker.LinkerResponseDataDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.BaiduTongjiConfig;
import org.befun.auth.service.linker.ILinkerData;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ThirdPartyAuthType.BAIDU_TONGJI;

@Slf4j
@Service
public class AuthBaiduTongjiService extends BaseAuthService<BaiduTongjiConfig, BaiduTongjiAuthDto> implements ILinkerData<LinkerBaiduTongjiParamDto> {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public ThirdPartyAuthType getAuthType() {
        return BAIDU_TONGJI;
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, BaiduTongjiAuthDto dto) {
        allAuthDto.setBaiduTongji(dto);
    }

    private static final String AUTH_URL = "http://openapi.baidu.com/oauth/2.0/authorize?response_type=code&client_id=%s&redirect_uri=oob&scope=basic&display=popup";
    private static final String ACCESS_TOKEN = "http://openapi.baidu.com/oauth/2.0/token?grant_type=authorization_code&code=%s&client_id=%s&client_secret=%s&redirect_uri=oob";
    private static final String REFRESH_TOKEN = "http://openapi.baidu.com/oauth/2.0/token?grant_type=refresh_token&refresh_token=%s&client_id=%s&client_secret=%s";
    private static final String GET_SITES = "https://openapi.baidu.com/rest/2.0/tongji/config/getSiteList?access_token=%s";
    private static final String GET_DATA = "https://openapi.baidu.com/rest/2.0/tongji/report/getData?max_results=0&access_token=%s&site_id=%s";

    /**
     * 获得百度统计授权地址
     */
    public BaiduTongjiAuthUrlDto getAuthUrl(String apiKey, String app) {
        return new BaiduTongjiAuthUrlDto(String.format(AUTH_URL, apiKey));
    }

    /**
     * 获得百度统计站点列表
     *
     * @param auth 1 先获取授权信息，在查询站点 0 直接用已保存的授权信息查询站点
     */
    public List<BaiduTongjiAuthSiteDto> getSites(Integer auth, String apiKey, String apiSecret, String code, String app) {
        if (auth == null || StringUtils.isEmpty(app) || (auth == 1 && (StringUtils.isEmpty(apiKey) || StringUtils.isEmpty(apiSecret) || StringUtils.isEmpty(code)))) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        Long orgId = TenantContext.getCurrentTenant();
        List<BaiduTongjiAuthSiteDto> sites;
        if (auth == 1) {
            // 授权，查询站点
            BaiduTongjiConfig cacheConfig = getCacheConfig(orgId, apiKey);
            if (cacheConfig != null
                    && apiKey.equals(cacheConfig.getApiKey())
                    && apiSecret.equals(cacheConfig.getApiSecret())
                    && code.equals(cacheConfig.getCode())) {
                // 参数相同，直接使用返回缓存
                sites = getSites(cacheConfig.getAccessToken());
            } else {
                BaiduTongjiConfig config = getAccessToken(apiKey, apiSecret, code);
                // 缓存当前配置的信息(apiKey,apiSecret,accessToken)
                cacheConfig(orgId, config);
                sites = getSites(config.getAccessToken());
            }
        } else {
            // 直接查询已绑定的数据
            sites = getSites(getSingleEntity(app));
        }
        if (CollectionUtils.isEmpty(sites)) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_NO_SITE);
        }
        return sites;
    }

    /**
     * 更新百度统计站点
     */
    public BaiduTongjiAuthDto updateSite(String app, BaiduTongjiAuthSiteDto site) {
        ThirdPartyAuth entity = getSingleEntity(app);
        BaiduTongjiConfig config;
        if (entity == null || (config = getConfig(entity)) == null || StringUtils.isEmpty(config.getAccessToken())) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        config.setStatus(1);
        config.setSiteId(site.getSiteId());
        config.setSiteDomain(site.getSiteDomain());
        entity.setConfig(JsonHelper.toJson(config));
        thirdPartyAuthService.save(entity);
        return mapToDto(entity, app);
    }

    /**
     * 百度统计配置临时缓存的key
     */
    private String cacheKey(Long orgId, String apiKey) {
        return String.format("cache:baidu-tongji-config:%s:%d", apiKey, orgId);
    }

    /**
     * 临时缓存百度统计配置
     */
    private void cacheConfig(Long orgId, BaiduTongjiConfig config) {
        String key = cacheKey(orgId, config.getApiKey());
        stringRedisTemplate.opsForValue().set(key, JsonHelper.toJson(config), Duration.ofHours(1));
    }

    /**
     * 获取临时缓存百度统计配置
     */
    private BaiduTongjiConfig getCacheConfig(Long orgId, String apiKey) {
        String key = cacheKey(orgId, apiKey);
        Object value = stringRedisTemplate.opsForValue().get(key);
        if (value != null) {
            return JsonHelper.toObject(value.toString(), BaiduTongjiConfig.class);
        }
        return null;
    }

    /**
     * 保存百度统计配置，并删除临时缓存
     */
    @Override
    @Transactional
    public BaiduTongjiAuthDto saveSingle(BaiduTongjiAuthDto dto) {
        BaiduTongjiAuthDto r = super.saveSingle(dto);
        stringRedisTemplate.delete(cacheKey(TenantContext.getCurrentTenant(), r.getConfig().getApiKey()));
        return r;
    }

    /**
     * 保存百度统计配置，并删除临时缓存
     */
    @Override
    public String formatConfig(BaiduTongjiAuthDto dto) {
        BaiduTongjiConfig config = dto.getConfig();
        if (config == null || StringUtils.isEmpty(config.getApiKey())) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        BaiduTongjiConfig cacheConfig = getCacheConfig(TenantContext.getCurrentTenant(), config.getApiKey());
        if (cacheConfig == null) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        if (config.getApiKey().equals(cacheConfig.getApiKey())
                && config.getApiSecret().equals(cacheConfig.getApiSecret())) {
            cacheConfig.setStatus(1);
            cacheConfig.setSiteId(config.getSiteId());
            cacheConfig.setSiteDomain(config.getSiteDomain());
            dto.setConfig(cacheConfig);
            return super.formatConfig(dto);
        }
        throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
    }

    /**
     * 第一次授权时，查询百度统计授权信息
     */
    private BaiduTongjiConfig getAccessToken(String apiKey, String apiSecret, String code) {
        String url = String.format(ACCESS_TOKEN, code, apiKey, apiSecret);
        BaiduTongjiAccessToken response = httpGet(url, BaiduTongjiAccessToken.class);
        if (response != null && response.success()) {
            return buildConfig(apiKey, apiSecret, code, response);
        }
        throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
    }

    /**
     * 通过返回的授权信息，构建配置
     */
    private BaiduTongjiConfig buildConfig(String apiKey, String apiSecret, String code, BaiduTongjiAccessToken accessToken) {
        BaiduTongjiConfig config = new BaiduTongjiConfig();
        config.setApiKey(apiKey);
        config.setApiSecret(apiSecret);
        config.setCode(code);
        updateConfigAccessToken(config, accessToken);
        return config;
    }

    /**
     * 通过返回的授权信息，构建配置
     */
    private void updateConfigAccessToken(BaiduTongjiConfig config, BaiduTongjiAccessToken accessToken) {
        config.setRefreshToken(accessToken.getRefreshToken());
        config.setAccessToken(accessToken.getAccessToken());
        config.setExpiresIn(accessToken.getExpiresIn());
        config.setSessionKey(accessToken.getSessionKey());
        config.setSessionSecret(accessToken.getSessionSecret());
        config.setScope(accessToken.getScope());
    }

    /**
     * 刷新授权信息
     */
    public String refreshAccessToken(ThirdPartyAuth entity, BaiduTongjiConfig config) {
        if (entity == null
                || config == null
                || StringUtils.isEmpty(config.getRefreshToken())
                || StringUtils.isEmpty(config.getApiKey())
                || StringUtils.isEmpty(config.getApiSecret())) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        String url = String.format(REFRESH_TOKEN, config.getRefreshToken(), config.getApiKey(), config.getApiSecret());
        BaiduTongjiAccessToken response = httpGet(url, BaiduTongjiAccessToken.class);
        if (response != null && response.success()) {
            updateConfigAccessToken(config, response);
            entity.setConfig(JsonHelper.toJson(config));
            thirdPartyAuthService.save(entity);
            return response.getAccessToken();
        } else {
            disableConfig(entity, config);
        }
        return null;
    }

    /**
     * 标记配置异常
     */
    private void disableConfig(ThirdPartyAuth entity, BaiduTongjiConfig config) {
        config.setStatus(2);
        entity.setConfig(JsonHelper.toJson(config));
        thirdPartyAuthService.save(entity);
    }

    /**
     * 获取站点
     */
    private List<BaiduTongjiAuthSiteDto> getSites(ThirdPartyAuth entity) {
        BaiduTongjiConfig config;
        if (entity == null || (config = getConfig(entity)) == null) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        return getSites(true, config.getAccessToken(),
                () -> refreshAccessToken(entity, config),
                i -> disableConfig(entity, config)
        );
    }

    /**
     * 获取站点
     */
    private List<BaiduTongjiAuthSiteDto> getSites(String accessToken) {
        return getSites(false, accessToken, null, null);
    }

    /**
     * 获取站点
     */
    private List<BaiduTongjiAuthSiteDto> getSites(boolean refreshIfExpire, String accessToken,
                                                  Supplier<String> refreshAccessToken,
                                                  Consumer<String> disableConfig) {
        if (StringUtils.isEmpty(accessToken)) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        String url = String.format(GET_SITES, accessToken);
        BaiduTongjiSite response = httpGet(url, BaiduTongjiSite.class);
        if (response != null) {
            if (response.success()) {
                List<BaiduTongjiSiteItem> sites = response.getList();
                if (sites != null) {
                    return sites.stream().map(i -> new BaiduTongjiAuthSiteDto(i.getSiteId(), i.getSiteDomain())).collect(Collectors.toList());
                }
            } else if (response.invalidSite()) {
                if (disableConfig != null) {
                    disableConfig.accept(null);
                }
            } else if (refreshIfExpire && refreshAccessToken != null && response.expire()) {
                accessToken = refreshAccessToken.get();
                return getSites(accessToken);
            }
        }
        return null;
    }

    @Override
    public boolean supportLinker(String linkerType) {
        return getAuthType().getCamelName().equals(linkerType);
    }

    @Override
    public LinkerResponseDataDto getData(String app, LinkerBaiduTongjiParamDto param) {
        String params;
        BaiduTongjiSiteData data = null;
        if (StringUtils.isNotEmpty(app) && param != null) {
            params = param.buildParams();
            data = getData(app, params);
        }
        return transformData(param, data);
    }

    private LinkerResponseDataDto transformData(LinkerBaiduTongjiParamDto param, BaiduTongjiSiteData data) {
        LinkerResponseDataDto dto = new LinkerResponseDataDto(getAuthType().getCamelName());
        dto.setLinkerType(getAuthType().getCamelName());
        BaiduTongjiSiteDataResult result;
        if (param == null || data == null || (result = data.getResult()) == null) {
            return dto;
        }
        try {
            dto.setRecentLabel(result.getFields());
            dto.setCurrentValue(result.getSum().get(0).get(0));
            List<List<Object>> dateList = result.getItems().get(0);
            List<List<Object>> dataList = result.getItems().get(1);
            for (int i = 0; i < dateList.size(); i++) {
                String label = dateList.get(i).get(0).toString();
                Double value = null;
                Object value0 = dataList.get(i).get(0);
                if (value0 != null && NumberUtils.isCreatable(value0.toString())) {
                    value = Double.parseDouble(value0.toString());
                }
                dto.addRecentValue(label, value);
            }
        } catch (Throwable e) {
            log.error("解析百度统计数据失败，params={}", param.buildParams(), e);
        }
        return dto;
    }

    /**
     * 获取站点数据
     */
    private BaiduTongjiSiteData getData(String app, String params) {
        ThirdPartyAuth entity = getSingleEntity(app);
        BaiduTongjiConfig config;
        if (entity == null
                || (config = getConfig(entity)) == null
                || config.getStatus() == null
                || config.getStatus() != 1
                || StringUtils.isEmpty(config.getAccessToken())) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        return getData(true, config.getAccessToken(), config.getSiteId(), params,
                () -> refreshAccessToken(entity, config),
                i -> disableConfig(entity, config));
    }

    /**
     * 获取站点数据
     */
    private BaiduTongjiSiteData getData(boolean refreshIfExpire, String accessToken, Integer siteId, String params,
                                        Supplier<String> refreshAccessToken,
                                        Consumer<String> disableConfig) {
        if (StringUtils.isEmpty(accessToken) || siteId == null) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.BAIDU_TONGJI_BIND_ERROR);
        }
        String url = String.format(GET_DATA, accessToken, siteId);
        if (params != null) {
            url += params;
        }
        BaiduTongjiSiteData response = httpGet(url, BaiduTongjiSiteData.class);
        if (response != null) {
            if (response.success()) {
                return response;
            } else if (response.invalidSite()) {
                if (disableConfig != null) {
                    disableConfig.accept(null);
                }
            } else if (refreshIfExpire && refreshAccessToken != null && response.expire()) {
                accessToken = refreshAccessToken.get();
                return getData(false, accessToken, siteId, params, null, null);
            }
        }
        return null;
    }


    @Getter
    @Setter
    public static class BaiduTongjiResponse<T> {

        public static final int SUCCESS = 0;
        public static final int EXPIRE1 = 110;
        public static final int EXPIRE2 = 111;
        public static final int INVALID_SITE = 21601;
        public static final String INVALID_SITE_MESSAGE = "Invalid Site Or No Permission";
        public static final String INVALID_TOKEN_MESSAGE1 = "Access token invalid or no longer valid";
        public static final String INVALID_TOKEN_MESSAGE2 = "Access token expired";


        // 错误码
        @JsonProperty("error_code")
        private Integer errorCode;
        @JsonProperty("error_msg")
        private String errorMsg;
        // 错误码
        @JsonProperty("error")
        private String error;
        @JsonProperty("error_description")
        private String errorDescription;

        private List<T> list;
        private T data;
        private T result;

        public boolean success() {
            return (errorCode == null || errorCode == SUCCESS) && error == null;
        }

        public boolean invalidSite() {
            return (errorCode != null && errorCode == INVALID_SITE)
                    || (INVALID_SITE_MESSAGE.equals(errorMsg));
        }

        public boolean expire() {
            return (errorCode != null && (errorCode == EXPIRE1 || errorCode == EXPIRE2))
                    || (INVALID_TOKEN_MESSAGE1.equals(errorMsg))
                    || (INVALID_TOKEN_MESSAGE2.equals(errorMsg));
        }
    }

    @Getter
    @Setter
    public static class BaiduTongjiSite extends BaiduTongjiResponse<BaiduTongjiSiteItem> {
    }

    @Getter
    @Setter
    public static class BaiduTongjiAccessToken extends BaiduTongjiResponse<Object> {

        @JsonProperty("expires_in")
        private Integer expiresIn;
        @JsonProperty("refresh_token")
        private String refreshToken;
        @JsonProperty("access_token")
        private String accessToken;
        @JsonProperty("session_secret")
        private String sessionSecret;
        @JsonProperty("session_key")
        private String sessionKey;
        @JsonProperty("scope")
        private String scope;
    }

    @Getter
    @Setter
    public static class BaiduTongjiSiteItem {

        @JsonProperty("site_id")
        private Integer siteId;
        @JsonProperty("domain")
        private String siteDomain;
    }

    @Getter
    @Setter
    public static class BaiduTongjiSiteData extends BaiduTongjiResponse<BaiduTongjiSiteDataResult> {
    }

    @Getter
    @Setter
    public static class BaiduTongjiSiteDataResult {
        private List<String> fields;
        private List<List<Double>> sum;
        private List<List<List<Object>>> items;
    }

    private <T> T httpGet(String url, Class<T> tClass) {
        try {
            String response = Request.Get(url)
                    .socketTimeout(30000)
                    .connectTimeout(30000)
                    .execute().returnContent().toString();
            log.info("baidu tongji http get:\n" +
                    "url={}\n" +
                    "response={}", url, response);
            return JsonHelper.toObject(response, tClass);
        } catch (IOException e) {
            log.error("baidu tongji get({}) http error ", url, e);
        }
        return null;
    }

    private Map<String, Object> httpGet(String url) {
        try {
            String response = Request.Get(url)
                    .socketTimeout(30000)
                    .connectTimeout(30000)
                    .execute().returnContent().toString();
            log.info("baidu tongji http get:\n" +
                    "url={}\n" +
                    "response={}", url, response);
            return JsonHelper.toMap(response);
        } catch (IOException e) {
            log.error("baidu tongji get({}) http error ", url, e);
        }
        return null;
    }
}
