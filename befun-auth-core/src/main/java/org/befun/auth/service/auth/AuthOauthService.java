package org.befun.auth.service.auth;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.CasAuthDto;
import org.befun.auth.dto.auth.oauth.OauthAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.CasConfig;
import org.befun.auth.service.auth.config.OauthConfig;
import org.springframework.stereotype.Service;

import static org.befun.auth.constant.ThirdPartyAuthType.CAS;
import static org.befun.auth.constant.ThirdPartyAuthType.OAUTH;

@Service
public class AuthOauthService extends BaseAuthService<OauthConfig, OauthAuthDto> {

    @Override
    public ThirdPartyAuthType getAuthType() {
        return OAUTH;
    }

    @Override
    public void afterMapToDto(ThirdPartyAuth auth, OauthAuthDto dto) {
        dto.setEnableWhiteList(auth == null ? 0 : auth.getEnableWhiteList());
    }

    @Override
    protected void afterMapToEntity(ThirdPartyAuth auth, OauthAuthDto dto) {
        auth.setEnableWhiteList(dto.getEnableWhiteList());
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, OauthAuthDto dto) {
        allAuthDto.setOauth(dto);
    }
}
