package org.befun.auth.service.orgconfig;

import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBaseInfoDto;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrgConfigBaseInfoService implements BaseOrgConfigService {

    @Autowired
    private AuthProperties authProperties;


    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.baseInfo;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        config.setBaseInfo(new OrgConfigBaseInfoDto());
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {

    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        return null;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setBaseInfo(source.getBaseInfo());
    }
}
