package org.befun.auth.service.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.YouzanProperty;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.YouzanAuthDto;
import org.befun.auth.dto.auth.youzan.YouzanBindDto;
import org.befun.auth.dto.auth.youzan.YouzanSupportEventDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyMessageYouzan;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.ThirdPartyMessageYouzanRepository;
import org.befun.auth.service.UserService;
import org.befun.auth.service.auth.config.YouzanConfig;
import org.befun.auth.workertrigger.AuthTaskTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.time.Duration;
import java.util.*;

import static org.befun.auth.constant.ThirdPartyAuthType.YOUZAN;

@Slf4j
@Service
public class AuthYouzanService extends BaseAuthService<YouzanConfig, YouzanAuthDto> {

    private static final String URL_GET_USER_TOKEN = "https://open.youzanyun.com/api/youzan.appstore.open.user.get/1.0.0";
    private static final String URL_POST_ACCESS_TOKEN = "https://open.youzanyun.com/auth/token";
    private static final String URL_POST_USE_INFO = "https://open.youzanyun.com/api/youzan.users.info.query/1.0.0";

    private static final String STATUS_IGNORE = "ignore";
    private static final String STATUS_WAIT_BINDING = "wait_binding";
    private static final String STATUS_BOUND = "bound";
    private static final String STATUS_TRIGGER_JOURNEY = "trigger_journey";

    private static final String TYPE_AUTH_CODE = "AUTH_CODE";

    private static final String CACHE_ACCESS_TOKEN = "youzan-access-token:%s"; // hash kdtId

    private static final String MOCK_CODE_PREFIX = "mock-code-";
    private static final String MOCK_USER_TOKEN_PREFIX = "mock-newUserToken-";

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private AuthTaskTrigger authTaskTrigger;
    @Autowired
    private UserService userService;
    @Autowired
    private ThirdPartyMessageYouzanRepository thirdPartyMessageYouzanRepository;

    @Override
    public ThirdPartyAuthType getAuthType() {
        return YOUZAN;
    }

    @Override
    public String buildSource(Long orgId, YouzanConfig config) {
        return buildSource(config.getKdtId());
    }

    private String buildSource(String kdtId) {
        return getAuthType().getSourcePrefix() + kdtId;
    }

    @Override
    public ThirdPartyAuth getOrCreateSingleEntity(String app) {
        throw new BadRequestException();
    }

    @Override
    public YouzanAuthDto getOrCreateSingle(String app) {
        throw new BadRequestException();
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, YouzanAuthDto dto) {
        allAuthDto.setYouzan(dto);
    }

    @Override
    public YouzanAuthDto emptyDto(String app) {
        return null;
    }


    private YouzanProperty youzanProperty() {
        return authProperties.getYouzan();
    }

    private String accessTokenCacheKey(String kdtId) {
        return String.format(CACHE_ACCESS_TOKEN, kdtId);
    }

    private HashOperations<String, String, String> hashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    public List<YouzanSupportEventDto> supportEvents() {
        List<YouzanSupportEventDto> list = new ArrayList<>();
        youzanProperty().getSupportEvents().forEach((k, v) -> {
            list.add(new YouzanSupportEventDto(k, v));
        });
        return list;
    }

    public <T extends Response<?>> T httpGet(String url, String queryString, Class<T> tClass) {
        try {
            if (StringUtils.isNotEmpty(queryString)) {
                url += "?" + queryString;
            }
            String response = Request.Get(url).connectTimeout(30000).socketTimeout(30000)
                    .execute().returnContent().toString();
            log.info("youzan http get, url={}, response={}", url, response);
            T formatResponse = JsonHelper.toObject(response, tClass);
            if (formatResponse != null) {
                formatResponse.setOriginResponse(response);
                return formatResponse;
            }
        } catch (IOException e) {
            log.error("youzan get({}) http error ", url, e);
        }
        return null;
    }

    public <T extends Response<?>> T httpPost(String url, Map<String, Object> body, Class<T> tClass) {
        try {
            String bodyString = JsonHelper.toJson(body);
            String response = Request.Post(url).connectTimeout(30000).socketTimeout(30000)
                    .bodyString(bodyString, ContentType.APPLICATION_JSON)
                    .execute().returnContent().toString();
            log.info("youzan http post, url={}, body={}, response={}", url, bodyString, response);
            T formatResponse = JsonHelper.toObject(response, tClass);
            if (formatResponse != null) {
                formatResponse.setOriginResponse(response);
                return formatResponse;
            }
        } catch (IOException e) {
            log.error("youzan post({}) http error ", url, e);
        }
        return null;
    }

    /**
     * aes解密
     */
    public String decryptAes(String urlEncodeContent) {
        try {
            String keyString = youzanProperty().getClientSecret();
            String ivKeyString = "0102030405060708";
            String aesModePadding = "AES/CBC/PKCS5Padding";
            Security.addProvider(new BouncyCastleProvider());
            byte[] byteKey = Arrays.copyOf(keyString.getBytes(StandardCharsets.UTF_8), 16);
            SecretKey key = new SecretKeySpec(byteKey, "AES");
            Cipher cipher = Cipher.getInstance(aesModePadding);
            IvParameterSpec iv = new IvParameterSpec(ivKeyString.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.DECRYPT_MODE, key, iv);
            byte[] byteContent = URLDecoder.decode(urlEncodeContent, Charset.forName("GBK")).getBytes(Charset.forName("GBK"));
            byte[] byteDecode = cipher.doFinal(byteContent);
            return new String(byteDecode, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES解密失败,密文：{}", urlEncodeContent, e);
            return null;
        }
    }


    /**
     * 保存消息
     */
    private ThirdPartyMessageYouzan saveYouzanMessage(String msgId, String kdtId, String type, String message, String tradeId, String wxOpenId, String status) {
        return saveYouzanMessage(msgId, kdtId, type, message, tradeId, wxOpenId, null, status);
    }

    /**
     * 保存消息
     */
    private ThirdPartyMessageYouzan saveYouzanMessage(String msgId, String kdtId, String type, String message, String tradeId, String wxOpenId, Map<String, String> parsedFields, String status) {
        ThirdPartyMessageYouzan youzan = new ThirdPartyMessageYouzan();
        youzan.setMsgId(msgId);
        youzan.setKdtId(kdtId);
        youzan.setTradeId(tradeId);
        youzan.setWxOpenId(wxOpenId);
        youzan.setType(type);
        youzan.setMessage(message);
        youzan.setStatus(status);
        youzan.setClientId(youzanProperty().getClientId());
        if (parsedFields != null) {
            youzan.setParsedFields(JsonHelper.toJson(parsedFields));
        }
        thirdPartyMessageYouzanRepository.save(youzan);
        return youzan;
    }

    private void saveYouzanMessage(String kdtId, String type, String message, String status) {
        saveYouzanMessage(null, kdtId, type, message, null, null, status);
    }

    /**
     * 接收到授权消息 message
     */
    public void receiveAuthMessage(String message, String app) {
        // aes decrypt
        try {
            String decrypted = decryptAes(message);
            if (decrypted != null) {
                AuthMessage authMessage = JsonHelper.toObject(decrypted, AuthMessage.class);
                if (authMessage != null) {
                    saveYouzanMessage(authMessage.getKdtId(), authMessage.getType(), decrypted, STATUS_IGNORE);
                }
            }
        } catch (Throwable e) {
            log.error("receiveAuthMessage", e);
        }
    }

    /**
     * 接收到授权消息 code
     */
    public void receiveAuthCode(String code) {
        AccessToken accessToken = resetAccessToken(code);
        if (accessToken != null) {
            saveYouzanMessage(accessToken.getKdtId(), TYPE_AUTH_CODE, code, STATUS_WAIT_BINDING);
        }
    }

    /**
     * 接收到交易消息
     */
    public void receiveTradeMessage(String message, String eventSign, String eventType, String clientId, String app) {
        if (message == null || eventSign == null || eventType == null || clientId == null) {
            return;
        }
        String md5 = DigestUtils.md5Hex(youzanProperty().getClientId() + message + youzanProperty().getClientSecret());
        if (!eventSign.equals(md5)) {
            log.error("签名验证失败：md5={}, sign={}", md5, eventSign);
            return;
        }
        TradeMessage tradeMessage = JsonHelper.toObject(message, TradeMessage.class);
        if (tradeMessage == null
                || StringUtils.isEmpty(tradeMessage.getKdtId())
                || StringUtils.isEmpty(tradeMessage.getMsgId())
        ) {
            return;
        }
        // msgId 是否已存在
        if (thirdPartyMessageYouzanRepository.existsByMsgIdAndKdtId(tradeMessage.getMsgId(), tradeMessage.getKdtId())) {
            return;
        }
        // 店铺是否有绑定企业
        // 企业配置是否开启触发旅程场景
        // 消息类型是否满足触发规则
        // 是否有wxOpenId，只有交易支付消息才有，同一个交易都找到这个wxOpenId
        Map<String, String> parsedFields = parseFields(tradeMessage);
        String wxOpenId = parsedFields.get("wxOpenId");
        ThirdPartyAuth thirdPartyAuth = getByKdtId(tradeMessage.getKdtId(), app);
        if (thirdPartyAuth != null) {
            YouzanConfig config = getConfig(thirdPartyAuth);
            if (config != null
                    && config.isValid()
                    && config.isEnable()
                    && CollectionUtils.isNotEmpty(config.getRules())
                    && config.getRules().stream().anyMatch(i -> eventType.equals(i.getEvent()))
            ) {
                ThirdPartyMessageYouzan youzanMessage = saveYouzanMessage(tradeMessage.getMsgId(), tradeMessage.getKdtId(), eventType, message, tradeMessage.getId(), wxOpenId, parsedFields, STATUS_TRIGGER_JOURNEY);
                Long orgId = thirdPartyAuth.getOrgId();
                Long userId = Optional.ofNullable(userService.getAdminUser(orgId)).map(SimpleUser::getId).orElse(0L);
                config.getRules().forEach(rule -> {
                    if (eventType.equals(rule.getEvent()) && rule.getSendManageId() != null && rule.getSendManageId() > 0) {
                        authTaskTrigger.youzanMessageTriggerSendManage(orgId, userId, rule.getSendManageId(), "youzan", youzanMessage.getId());
                    }
                });
                return;
            }
        }
        saveYouzanMessage(tradeMessage.getMsgId(), tradeMessage.getKdtId(), eventType, message, tradeMessage.getId(), wxOpenId, STATUS_IGNORE);
    }

    private Map<String, String> parseFields(TradeMessage tradeMessage) {
        Map<String, String> parsedFields = new HashMap<>();
        // orderNo：订单编号 [id]
        parsedFields.put("orderNo", tradeMessage.getId());
        tradeMessage.formatMsg();
        // 解析出是否为微信公众号用户  [msg/full_order_info/source_info/source/platform]
        // 解析出是否为微信公众号用户  [msg/full_order_info/source_info/source/wx_entrance]
        // 解析出wxOpenId          [msg/full_order_info/buyer_info/outer_user_id]
        // platform 平台 wx:微信; merchant_3rd:商家自有app; buyer_v:买家版; browser:系统浏览器; alipay:支付宝;qq:腾讯QQ; wb:微博; other:其他
        // wx_entrance 微信平台细分 wx_gzh:微信公众号; yzdh:有赞大号; merchant_xcx:商家小程序; yzdh_xcx:有赞大号小程序; direct_buy:直接购买
        // outer_user_id 微信H5和微信小程序（有赞小程序和小程序插件）的订单会返回微信weixin_openid，三方App（有赞APP开店）的订单会返回open_user_id，2019年1月30号后的订单支持返回该参数
        String platform = getValueByPaths(tradeMessage.getParsedMsg(), List.of("full_order_info", "source_info", "source", "platform"));
        String wxEntrance = getValueByPaths(tradeMessage.getParsedMsg(), List.of("full_order_info", "source_info", "source", "wx_entrance"));
        String wxOpenId = getValueByPaths(tradeMessage.getParsedMsg(), List.of("full_order_info", "buyer_info", "outer_user_id"));
        if ("wx".equals(platform) && List.of("wx_gzh","direct_buy").contains(wxEntrance)) {
            parsedFields.put("wxOpenId", wxOpenId);
        } else if ("merchant_xcx".equals(wxEntrance)) {
            // wxOpenId 小程序 openId，调用有赞客户信息接口，查询 公众号 openId
            parsedFields.put("wxMiniProgramOpenId", wxOpenId);
            transformOpenId(tradeMessage.getKdtId(), wxOpenId, parsedFields);
        }
        // payment：支付金额 [msg/full_order_info/pay_info/payment]
        String payment = getValueByPaths(tradeMessage.getParsedMsg(), List.of("full_order_info", "pay_info", "payment"));
        if (StringUtils.isNotEmpty(payment)) {
            parsedFields.put("payment", payment);
        }
        // title：商品名称   [msg/full_order_info/orders[*]/title]
        String title = getValueByPaths(tradeMessage.getParsedMsg(), List.of("full_order_info", "orders", "title"));
        if (StringUtils.isNotEmpty(title)) {
            parsedFields.put("title", title);
        }
        return parsedFields;

    }

    /**
     * 小程序用户转换为公众号用户
     */
    private void transformOpenId(String kdtId, String miniProgramOpenId, Map<String, String> parsedFields) {
        Map<String, Object> body = new HashMap<>();
        body.put("open_id_type", 2);
        body.put("weixin_open_id", miniProgramOpenId);
        body.put("result_type_list", List.of(1));
        AccessToken accessToken = getAccessToken(kdtId);
        ResponseUserInfo response = httpPost(URL_POST_USE_INFO + "?access_token=" + accessToken.getAccessToken(), body, ResponseUserInfo.class);
        if (response != null && response.isSuccess()) {
            UserInfo userInfo = response.getData();
            if (userInfo != null) {
                List<UserInfoDetail> userList = userInfo.getUserList();
                if (CollectionUtils.isNotEmpty(userList)) {
                    UserInfoDetail detail = userList.get(0);
                    UserInfoWechatInfo wechatInfo = detail.getWechatInfo();
                    if (wechatInfo != null) {
                        parsedFields.put("wxUnionId", wechatInfo.getUnionId());
                        parsedFields.put("wxType", wechatInfo.getWechatType() == null ? null : wechatInfo.getWechatType().toString());
                    }
                    UserInfoPlatformInfo platformInfo = detail.getPlatformInfo();
                    if (platformInfo != null) {
                        parsedFields.put("wxOpenId", platformInfo.getWeixinOpenId());
                    }
                }
            }
        }
    }

    private String getValueByPaths(Map<String, Object> map, List<String> paths) {
        if (map == null || paths == null) {
            return null;
        }
        int i = 0;
        Object j = null;
        Map<String, Object> k;
        while (i < paths.size()) {
            if (i == 0) {
                k = map;
            } else if (j instanceof Map) {
                k = (Map<String, Object>) j;
            } else if (j instanceof List) {
                return getValueByPaths((List<Object>) j, paths.subList(i, paths.size()));
            } else {
                return null;
            }
            String path = paths.get(i);
            j = k.get(path);
            i++;
        }
        if (j instanceof String) {
            return (String) j;
        }
        return null;
    }

    private String getValueByPaths(List<Object> list, List<String> paths) {
        if (list == null || paths == null) {
            return null;
        }
        List<String> values = new ArrayList<>();
        list.forEach(o -> {
            if (o instanceof Map) {
                String value = getValueByPaths((Map<String, Object>) o, paths);
                if (StringUtils.isNotEmpty(value)) {
                    values.add(value);
                }
            }
        });
        return String.join(",", values);
    }

    /**
     * 接收到新的授权消息（auth_code）时，立即重置accessToken
     */
    public AccessToken resetAccessToken(String code) {
        if (youzanProperty().isEnableMock() && code.startsWith(MOCK_CODE_PREFIX)) {
            return getAccessTokenFromMock(code);
        }
        Map<String, Object> body = new HashMap<>();
        body.put("client_id", youzanProperty().getClientId());
        body.put("client_secret", youzanProperty().getClientSecret());
        body.put("authorize_type", "authorization_code");
        body.put("code", code);
        return getAccessTokenFromServer(body);
    }

    public AccessToken refreshAccessToken(String refreshToken) {
        if (youzanProperty().isEnableMock() && refreshToken.startsWith(MOCK_CODE_PREFIX)) {
            return getAccessTokenFromMock(refreshToken);
        }
        Map<String, Object> body = new HashMap<>();
        body.put("client_id", youzanProperty().getClientId());
        body.put("client_secret", youzanProperty().getClientSecret());
        body.put("authorize_type", "refresh_token");
        body.put("refresh_token", refreshToken);
        return getAccessTokenFromServer(body);
    }

    private AccessToken getAccessTokenFromMock(String mock) {
        String kdtId = mock.replace(MOCK_CODE_PREFIX, "");
        long expires = System.currentTimeMillis() + Duration.ofDays(7).toMillis();
        AccessToken accessToken = new AccessToken(kdtId, mock, kdtId, expires);
        cacheAccessToken(accessToken);
        return accessToken;
    }

    private AccessToken getAccessTokenFromServer(Map<String, Object> body) {
        ResponseAccessToken response = httpPost(URL_POST_ACCESS_TOKEN, body, ResponseAccessToken.class);
        AccessToken accessToken;
        if (response != null && response.isSuccess() && (accessToken = response.getData()) != null) {
            cacheAccessToken(accessToken);
            return accessToken;
        }
        return null;
    }

    private void cacheAccessToken(AccessToken accessToken) {
        Map<String, String> cache = new HashMap<>();
        cache.put("accessToken", accessToken.getAccessToken());
        cache.put("refreshToken", accessToken.getRefreshToken());
        cache.put("expires", String.valueOf(accessToken.getExpires()));
        String key = accessTokenCacheKey(accessToken.getKdtId());
        hashOpt().putAll(key, cache);
        stringRedisTemplate.expire(key, Duration.ofDays(30));
    }

    public AccessToken getAccessToken(String kdtId) {
        return getAccessToken(kdtId, false);
    }

    public AccessToken getAccessToken(String kdtId, boolean forceRefresh) {
        Map<String, String> cache = hashOpt().entries(accessTokenCacheKey(kdtId));
        if (MapUtils.isNotEmpty(cache)) {
            String accessToken = cache.get("accessToken");
            String refreshToken = cache.get("refreshToken");
            String expires = cache.get("expires");
            if (forceRefresh || NumberUtils.isDigits(expires)) {
                long expires1 = Long.parseLong(expires);
                if (forceRefresh || expires1 < System.currentTimeMillis()) {
                    return refreshAccessToken(refreshToken);
                } else {
                    return new AccessToken(accessToken, refreshToken, kdtId, expires1);
                }
            }
        }
        return null;
    }


    /**
     * 校验 newUserToken 和 accessToken 对应的店铺是否匹配
     */
    private boolean testUserToken(AccessToken accessToken, YouzanBindDto bindDto) {
        if (accessToken == null || StringUtils.isEmpty(accessToken.getAccessToken())) {
            return false;
        }
        if ((accessToken.getRefreshToken() != null && accessToken.getRefreshToken().startsWith(MOCK_CODE_PREFIX))
                || bindDto.getNewUserToken().startsWith(MOCK_CODE_PREFIX)) {
            // is mock enable
            if (youzanProperty().isEnableMock()) {
                String kdtId = bindDto.getNewUserToken().replace(MOCK_USER_TOKEN_PREFIX, "");
                bindDto.setKdtId(kdtId);
                bindDto.setShopName(kdtId);
                return accessToken.getAccessToken().equals(kdtId);
            } else {
                return false;
            }
        }
        String queryString = "user_token=" + bindDto.getNewUserToken() + "&access_token=" + accessToken.getAccessToken();
        ResponseUserToken response = httpGet(URL_GET_USER_TOKEN, queryString, ResponseUserToken.class);
        UserToken userToken;
        if (response != null && response.isSuccess() && (userToken = response.getData()) != null) {
            bindDto.setKdtId(userToken.getKdtId());
            bindDto.setShopName(userToken.getShopName());
            return true;
        }
        return false;
    }

    /**
     * 通过 newUserToken 找到对应已授权的店铺
     */
    public boolean parseUserToken(YouzanBindDto bindDto) {
        List<ThirdPartyMessageYouzan> codeMessages = thirdPartyMessageYouzanRepository.findByTypeAndStatusOrderByIdDesc(TYPE_AUTH_CODE, STATUS_WAIT_BINDING);
        if (CollectionUtils.isNotEmpty(codeMessages)) {
            for (ThirdPartyMessageYouzan code : codeMessages) {
                if (testUserToken(getAccessToken(code.getKdtId()), bindDto)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 解析出店铺id
     */
    private String parseKdtId(ThirdPartyAuth thirdPartyAuth) {
        String source = thirdPartyAuth.getSource();
        if (StringUtils.isNotEmpty(source) && source.startsWith(getAuthType().getSourcePrefix())) {
            return source.replace(getAuthType().getSourcePrefix(), "");
        }
        return null;
    }

    private List<ThirdPartyAuth> getAllBindingByKdtId(String kdtId, String app) {
        if (StringUtils.isEmpty(kdtId)) {
            return null;
        }
        return thirdPartyAuthService.getListBySource(buildSource(kdtId), getAuthType(), app);
    }

    /**
     * 判断授权关系是否有效
     */
    private boolean isValid(ThirdPartyAuth thirdPartyAuth) {
        YouzanConfig config = getConfig(thirdPartyAuth);
        if (config != null) {
            return config.isValid();
        }
        return false;
    }

    /**
     * 找到已绑定的指定店铺，valid=true
     */
    private ThirdPartyAuth getByKdtId(String kdtId, String app) {
        List<ThirdPartyAuth> list = getAllBindingByKdtId(kdtId, app);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(this::isValid).findFirst().orElse(null);
        }
        return null;
    }

    /**
     * 授权关系已取消，设置为无效
     */
    private void setInvalid(ThirdPartyAuth thirdPartyAuth) {
        YouzanConfig config = getConfig(thirdPartyAuth);
        if (config != null && config.isValid()) {
            config.setValid(false);
            updateConfig(thirdPartyAuth, config);
        }
    }

    /**
     * 重新绑定店铺，之前绑定的关系都设置为无效
     */
    private void setInvalidByKdtId(String kdtId, String app) {
        List<ThirdPartyAuth> list = getAllBindingByKdtId(kdtId, app);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(this::setInvalid);
        }
    }

    /**
     * 通过 newUserToken 找到对应已授权的店铺，并绑定到当前企业
     * 必须是超管
     * 1 如果当前企业有绑定店铺
     * 1.1 但是绑定关系已无效，或者 newUserToken 和当前绑定的店铺不匹配，则抛出错误信息 该企业已有有赞商城授权，如需更改请先取消当前授权内容
     * 1.2 把当前绑定的店铺所有待绑定（STATUS_WAIT_BINDING）的消息都设置为 STATUS_BOUND
     * 2 如果当前企业无绑定店铺
     * 2.1 解析 newUserToken 对应的店铺，如果没有找到，则抛出错误 未接收到授权信息，请重试，或者重新授权
     * 2.2 如果存在这个店铺的有效绑定关系，全部设置为无效
     * 2.3 新增绑定关系，并把当前绑定的店铺所有待绑定（STATUS_WAIT_BINDING）的消息都设置为 STATUS_BOUND
     */
    @Transactional
    public YouzanAuthDto bind(String app, YouzanBindDto bindDto) {
        Long orgId = TenantContext.requireCurrentTenant();
        // 必须是超管
        if (!TenantContext.getCurrentIsAdmin()) {
            throw new BusinessException(60001, "无权限，请登录管理员账号完成授权");
        }
        // 1 当前企业有绑定店铺
        ThirdPartyAuth exist = getSingleEntity(app);
        if (exist != null) {
            String kdtId = parseKdtId(exist);
            if (StringUtils.isEmpty(kdtId)
                    || !isValid(exist)
                    || !testUserToken(getAccessToken(kdtId), bindDto)
                    || !kdtId.equals(bindDto.getKdtId())) {
                // 1.1 绑定关系已无效，或者 newUserToken 和当前绑定的店铺不匹配
                throw new BusinessException(60002, "该企业已有有赞商城授权，如需更改请先取消当前授权内容");
            } else {
                thirdPartyMessageYouzanRepository.updateStatusByKdtIdAndTypeAndStatus(STATUS_BOUND, kdtId, TYPE_AUTH_CODE, STATUS_WAIT_BINDING);
                return mapToDto(exist, app);
            }
        }
        // 2 当前企业无绑定店铺
        // 2.1 解析有赞店铺信息
        String kdtId;
        if (!parseUserToken(bindDto) || StringUtils.isEmpty((kdtId = bindDto.getKdtId()))) {
            throw new BadRequestException("未接收到授权信息，请重试，或者重新授权");
        }
        // 2.2 如果存在这个店铺的有效绑定关系，全部设置为无效
        setInvalidByKdtId(kdtId, app);
        // 2.3 新增绑定关系，并把当前绑定的店铺所有待绑定（STATUS_WAIT_BINDING）的消息都设置为 STATUS_BOUND
        YouzanConfig config = new YouzanConfig();
        config.setKdtId(kdtId);
        ThirdPartyAuth bind = add(orgId, app, config, bindDto.getShopName());
        thirdPartyMessageYouzanRepository.updateStatusByKdtIdAndTypeAndStatus(STATUS_BOUND, kdtId, TYPE_AUTH_CODE, STATUS_WAIT_BINDING);
        return mapToDto(bind, app);
    }


    @Override
    public YouzanAuthDto saveSingle(YouzanAuthDto dto) {
        ThirdPartyAuth auth = getSingleEntity(dto.getApp());
        if (auth == null) {
            throw new BadRequestException("不支持的操作");
        } else {
            YouzanConfig oldConfig = getConfig(auth);
            if (oldConfig != null && !oldConfig.isValid()) {
                throw new BadRequestException("授权关系已无效，请取消当前授权内容");
            }
            mapToUpdateEntity(auth, dto);
            thirdPartyAuthService.save(auth);
        }
        return mapToDto(auth, dto.getApp());
    }

    @Override
    @Transactional
    public boolean deleteSingle(String app) {
        ThirdPartyAuth entity = getSingleEntity(app);
        if (entity == null) {
            throw new BadRequestException("有赞商城未绑定");
        }
        // 删除 third party auth
        thirdPartyAuthService.delete(entity);
        return true;
    }


    @Getter
    @Setter
    public static class Response<DATA> {
        private Integer code;
        private String message;
        private Boolean success;
        private DATA data;
        private String originResponse;

        public boolean isSuccess() {
            return success != null && success && code != null && code == 200;
        }
    }

    @Getter
    @Setter
    public static class ResponseUserToken extends Response<UserToken> {
    }

    @Getter
    @Setter
    public static class ResponseAccessToken extends Response<AccessToken> {
    }

    @Getter
    @Setter
    public static class ResponseUserInfo extends Response<UserInfo> {
    }


    @Getter
    @Setter
    public static class UserToken {
        @JsonProperty("shop_name")
        private String shopName;
        @JsonProperty("app_id")
        private String appId;
        @JsonProperty("kdt_id")
        private String kdtId;
        private String code;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccessToken {
        @JsonProperty("access_token")
        private String accessToken;
        @JsonProperty("refresh_token")
        private String refreshToken;
        @JsonProperty("authority_id")
        private String kdtId;
        @JsonProperty("expires")
        private Long expires;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        @JsonProperty("user_list")
        private List<UserInfoDetail> userList;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfoDetail {
        @JsonProperty("wechat_info")
        private UserInfoWechatInfo wechatInfo;
        @JsonProperty("platform_info")
        private UserInfoPlatformInfo platformInfo;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfoWechatInfo {
        @JsonProperty("wechat_type")
        private Integer wechatType;
        @JsonProperty("union_id")
        private String unionId;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfoPlatformInfo {
        @JsonProperty("weixin_open_id")
        private String weixinOpenId;
    }

    @Getter
    @Setter
    public static class AuthMessage {
        private String type;
        private String kdtId;
        private Long effectTime;
    }

    @Getter
    @Setter
    public static class TradeMessage {
        @JsonProperty("id")
        private String id;
        @JsonProperty("msg_id")
        private String msgId;
        @JsonProperty("kdt_id")
        private String kdtId;
        @JsonProperty("client_id")
        private String clientId;
        private String msg;

        private Map<String, Object> parsedMsg;

        public void formatMsg() {
            if (StringUtils.isNotEmpty(msg)) {
                if (msg.startsWith("%7B%22")) {
                    String decoderMsg = URLDecoder.decode(msg, StandardCharsets.UTF_8);
                    parsedMsg = JsonHelper.toMap(decoderMsg);
                } else {
                    parsedMsg = JsonHelper.toMap(msg);
                }
            }
        }
    }

}
