package org.befun.auth.service;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.DepartmentTreeType;
import org.befun.auth.dto.DepartmentRequestDto;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.DepartmentDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleDepartment;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.auth.workertrigger.IAuthTaskTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.service.IDepartmentCacheService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.auth.constant.DepartmentTreeType.*;

@Service
public class DepartmentService extends BaseService<Department, DepartmentDto, DepartmentRepository> implements IDepartmentCacheService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private TreeConvertService treeConvertService;
    @Lazy
    @Autowired
    private UserService userService;
    @Autowired
    private IAuthEventTrigger authEventTrigger;
    @Autowired
    private IAuthTaskTrigger authTaskTrigger;

    private String departmentCacheKey(Long orgId) {
        return String.format("department.tree:%d", orgId);
    }

    public void clearCache(Long orgId) {
        stringRedisTemplate.delete(departmentCacheKey(orgId));
    }

    /**
     * 部门树结构 转换为列表
     */
    public <R> List<R> treeToList(DepartmentTreeDto tree, Function<DepartmentTreeDto, R> map) {
        if (tree == null) {
            return null;
        }
        return treeConvertService.treeToList(List.of(tree), map);
    }

    /**
     * 部门树结构 转换为列表
     */
    public <R> List<R> treeToList2(DepartmentTreeDto tree, Function<DepartmentTreeDto, R> map) {
        if (tree == null) {
            return null;
        }
        return treeConvertService.treeToList2(List.of(tree), map);
    }

    /**
     * 部门树结构 转换为id列表
     */
    public List<Long> treeIds(DepartmentTreeDto tree) {
        return treeToList(tree, DepartmentTreeDto::getId);
    }

    /**
     * 企业的 全部部门树结构
     */
    public DepartmentTreeDto treeByOrg(Long orgId) {
        return tree(ROOT, orgId, null);
    }

    /**
     * 用户部门树 （当前部门+所有子部门+所有直接上级部门）
     * @since v1.9.3 用户支持多部门
     */
    public DepartmentTreeDto fullTreeByUser(Long orgId, Long userId) {
        User user = userService.get(userId);
        if (user == null) {
            return null;
        }
        if (userService.hasSuperAdminRole(user)) {
            return treeByOrg(orgId);
        }
        Set<Long> departmentIds = user.parseDepartmentIds();
        return fullTreeByDepartments(orgId, departmentIds);
    }

    /**
     * 用户部门树 （当前部门+所有子部门）
     * @since v1.9.3 用户支持多部门
     */
    public List<DepartmentTreeDto> childrenTreeByUser(Long orgId, Long userId) {
        User user = userService.get(userId);
        if (user == null) {
            return null;
        }
        if (userService.hasSuperAdminRole(user)) {
            DepartmentTreeDto tree = treeByOrg(orgId);
            if (tree != null) {
                List<DepartmentTreeDto> trees = new ArrayList<>();
                trees.add(tree);
                return trees;
            }
            return null;
        }
        Set<Long> departmentIds = user.parseDepartmentIds();
        return childrenTreeByDepartments(orgId, departmentIds);
    }


    /**
     * 用户部门树 （当前部门+上级部门）
     */
    public List<DepartmentTreeDto> parentTreeByUser(Long orgId, Long userId) {
        User user = userService.get(userId);
        if (user == null) {
            return null;
        }
        if (userService.hasSuperAdminRole(user)) {
            DepartmentTreeDto tree = treeByOrg(orgId);
            if (tree != null) {
                List<DepartmentTreeDto> trees = new ArrayList<>();
                trees.add(tree);
                return trees;
            }
            return null;
        }
        Set<Long> departmentIds = user.parseDepartmentIds();

        return treeToList(parentTreeByDepartments(orgId, departmentIds), i -> i);
    }

    /**
     * 指定部门的 部门树结构
     */
    public DepartmentTreeDto treeByDepartment(Long orgId, Long departmentId, boolean onlyChildren) {
        return tree(onlyChildren ? CHILDREN : FULL, orgId, departmentId);
    }

    /**
     * 查询指定多个部门的树结构（当前部门+所有子部门）  {@link DepartmentTreeType#CHILDREN}
     */
    public List<DepartmentTreeDto> childrenTreeByDepartments(Long orgId, Set<Long> departmentIds) {
        if (orgId == null || CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        return departmentIds.stream()
                .map(departmentId -> cacheTree(orgId, CHILDREN, Set.of(departmentId), () -> {
                    List<DepartmentTreeDto> trees = getSelectDepartments(CHILDREN, orgId, i -> departmentId.equals(i.getId()));
                    if (CollectionUtils.isNotEmpty(trees)) {
                        return trees.get(0);
                    }
                    return null;
                }))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 查询指定多个部门的树结构（当前部门+所有子部门+所有直接上级部门）  {@link DepartmentTreeType#FULL}
     */
    public DepartmentTreeDto fullTreeByDepartments(Long orgId, Set<Long> departmentIds) {
        if (orgId == null || CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        return cacheTree(orgId, FULL, departmentIds, () -> {
            List<DepartmentTreeDto> trees = getSelectDepartments(FULL, orgId, i -> departmentIds.contains(i.getId()));
            if (CollectionUtils.isNotEmpty(trees)) {
                return trees.get(0);
            }
            return null;
        });
    }

    /**
     * 查询指定多个部门的树结构（当前部门+所有直接上级部门）  {@link DepartmentTreeType#PARENT}
     */
    public DepartmentTreeDto parentTreeByDepartments(Long orgId, Set<Long> departmentIds) {
        if (orgId == null || CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        return cacheTree(orgId, PARENT, departmentIds, () -> {
            List<DepartmentTreeDto> trees = getSelectDepartments(PARENT, orgId, i -> departmentIds.contains(i.getId()));
            if (CollectionUtils.isNotEmpty(trees)) {
                return trees.get(0);
            }
            return null;
        });
    }

    /**
     * 部门 树结构有 3 种类型
     * 1 指定部门所有直接上级部门 + 指定部门和所有的子部门 {@link DepartmentTreeType#FULL}
     * 2 指定部门和所有的子部门 {@link DepartmentTreeType#CHILDREN}
     * 3 完整的树结构 {@link DepartmentTreeType#ROOT}
     * 4 指定部门所有直接上级部门 {@link DepartmentTreeType#PARENT}
     */
    public DepartmentTreeDto tree(DepartmentTreeType type, Long orgId, Long departmentId) {
        if (orgId == null) {
            return null;
        }
        return cacheTree(orgId, type, departmentId == null ? null : Set.of(departmentId), () -> {
            List<DepartmentTreeDto> trees = null;
            if (type == ROOT) {
                trees = getSelectDepartments(ROOT, orgId, i -> i.getPid() == 0);
                if (CollectionUtils.isNotEmpty(trees)) {
                    calcLevel(1, trees);
                }
            } else {
                if (departmentId == null) {
                    return null;
                }
                if (type == CHILDREN) {
                    trees = getSelectDepartments(CHILDREN, orgId, i -> i.getId().equals(departmentId));
                } else if (type == FULL) {
                    trees = getSelectDepartments(FULL, orgId, i -> i.getId().equals(departmentId));
                }
            }
            if (CollectionUtils.isNotEmpty(trees)) {
                return trees.get(0);
            }
            return null;
        });
    }

    private DepartmentTreeDto cacheTree(long orgId, DepartmentTreeType type, Set<Long> departmentIds,
                                        Supplier<DepartmentTreeDto> getTree) {
        String key = departmentCacheKey(orgId);
        String ids;
        if (CollectionUtils.isEmpty(departmentIds)) {
            ids = "0";
        } else {
            ids = departmentIds.stream().filter(Objects::nonNull).sorted().map(Objects::toString).collect(Collectors.joining(","));
        }
        String hashKey = String.format("%s:%s", type, ids);
        Object cache = stringRedisTemplate.opsForHash().get(key, hashKey);
        DepartmentTreeDto tree = null;
        if (cache != null) {
            tree = JsonHelper.toObject(cache.toString(), DepartmentTreeDto.class);
        }
        if (tree != null) {
            return tree;
        }
        tree = getTree.get();
        if (tree != null) {
            stringRedisTemplate.opsForHash().put(key, hashKey, JsonHelper.toJson(tree));
        }
        return tree;
    }

    /**
     * 获得指定的企业部门
     */
    private List<DepartmentTreeDto> getSelectDepartments(DepartmentTreeType type, Long orgId, Predicate<DepartmentTreeDto> test) {
        List<SimpleDepartment> all = repository.findSimpleByOrgId(orgId);
        if (CollectionUtils.isEmpty(all)) {
            return null;
        }
        TreeMap<Long, DepartmentTreeDto> map = new TreeMap<>();
        List<DepartmentTreeDto> selects = new ArrayList<>();
        DepartmentTreeDto root = null;
        for (SimpleDepartment department : all) {
            DepartmentTreeDto tree = DepartmentTreeDto.mapFrom(department);
            if (tree != null) {
                if (root == null && tree.getPid() != null && tree.getPid() == 0) {
                    root = tree;
                }
                if (test.test(tree)) {
                    selects.add(tree);
                }
                map.put(tree.getId(), tree);
            }
        }
        if (root == null || selects.isEmpty()) {
            return null;
        }

        if (type != PARENT) {
            map.forEach((id, i) -> {
                DepartmentTreeDto parent = map.get(i.getPid());
                if (parent != null) {
                    parent.getSubDepartments().add(i);
                }
            });
        }

        if (type == CHILDREN || type == ROOT) {
            return selects;
        }
        // 找到每个选择的节点的所有直接上级
        List<DepartmentTreeDto> allNodes = new ArrayList<>();
        selects.forEach(select -> {
            DepartmentTreeDto findRoot = select;
            // FULL
            while (true) {
                if (findRoot.getPid() != null && findRoot.getPid() > 0) {
                    // 如果当前不是根部门，有上级部门
                    DepartmentTreeDto parent = map.get(findRoot.getPid());
                    if (parent == null) {
                        // 上级部门不存在，当前部门无效
                        findRoot = null;
                        break;
                    } else {
                        allNodes.add(parent);
                        findRoot = parent;
                    }
                } else {
                    // 如果当前是根部门，结束查找
                    break;
                }
            }
            if (findRoot != null) {
                allNodes.addAll(treeToList(select, Function.identity()));
            }
        });
        return treeConvertService.listToTree(allNodes, i -> {
            i.getSubDepartments().clear();
            return i;
        });
    }

    public void calcLevel(int level, List<DepartmentTreeDto> departments) {
        if (CollectionUtils.isNotEmpty(departments)) {
            departments.forEach(d -> {
                d.setLevel(level);
                calcLevel(level + 1, d.getSubDepartments());
            });
        }
    }

    @Override
    public List<Long> getCacheSubDepartmentIds(Long orgId, Long userId, boolean isAdmin) {
        return getSubDepartmentIdList(orgId, userId, isAdmin);
    }

    public boolean addUsers(Long id, List<Long> userIds) {
        Department department = require(id);
        checkIsOrgDepartment(TenantContext.getCurrentTenant(), department);
        userService.updateUserDepartment(userIds, id);
        return true;
    }

    /**
     * 保存部门（新增和修改）
     */
    public DepartmentDto saveDepartment(DepartmentRequestDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        if (orgId == null || dto == null) {
            throw new BadRequestException();
        }
        Department department;
        if (dto.getId() != null) {
            department = updateDepartment(orgId, dto.getId(), dto.getTitle(), dto.getPid(), dto.getCode(), dto.getEquivalentCode());
        } else {
            department = addDepartment(orgId, dto.getTitle(), dto.getPid(), dto.getCode(), dto.getEquivalentCode());
        }
        clearCache(orgId);
        updateParentList(orgId);
        return mapToDto(department);
    }

    /**
     * 校验部门属于指定的企业
     */
    private void checkIsOrgDepartment(Long orgId, Department department) {
        if (orgId != null && department != null && orgId.equals(department.getOrgId())) {
            return;
        }
        throw new EntityNotFoundException();
    }

    /**
     * 校验部门编号在企业内唯一
     */
    private void checkUniqueCode(Long currentId, Long orgId, String code) {
        if (StringUtils.isEmpty(code)) {
            return;
        }
        Department department = repository.findFirstByOrgIdAndCode(orgId, code);
        if (department == null) {
            return;
        }
        if (currentId != null && currentId.equals(department.getId())) {
            return;
        }
        throw new BadRequestException(String.format("部门编号已存在： %s 已设置此编号", department.getTitle()));
    }

    /**
     * 修改部门
     */
    private void checkLevel(Long orgId, Long oldPId, Long newPId) {
        // 新的父部门的level必须小于或等于旧的父部门的level
        // 新的父部门的level不能>=5
        DepartmentTreeDto root = treeByOrg(orgId);
        List<DepartmentTreeDto> list = treeToList(root, Function.identity());
        Integer oldLevel = null;
        Integer newLevel = null;
        if (CollectionUtils.isNotEmpty(list)) {
            if (list.get(0).getLevel() == null) {
                calcLevel(1, List.of(root));
            }
            for (DepartmentTreeDto d : list) {
                if (d.getId().equals(oldPId)) {
                    oldLevel = d.getLevel();
                }
                if (d.getId().equals(newPId)) {
                    newLevel = d.getLevel();
                }
            }
        }
        if (oldLevel == null) {
            oldLevel = 10;// 新增时，没有旧的父部门层级
        }
        if (newLevel == null) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.DEPARTMENT_PARENT_NOT_FOUND);
        }
        // 产品那边也没查到为什么不能下移  我也不知道为什么不能下移
//        if (newLevel > oldLevel) {
//            throw ToastMessageHelper.badRequestException(AuthToastMessage.DEPARTMENT_PARENT_IS_CHILD);
//        }
        if (newLevel >= 5) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.DEPARTMENT_LEVEL_LIMIT);
        }
    }

    private void checkUniqueTitleAtSameLevel(Long pId, Long id, String title) {
        // 最顶层不需要判断
        if (pId == null || pId == 0) {
            return;
        }
        if (repository.count((root, query, builder) -> {
            javax.persistence.criteria.Predicate pidPredication = builder.equal(root.get("pid"), pId);
            javax.persistence.criteria.Predicate titlePredication = builder.equal(root.get("title"), title);
            if (id == null) {
                return builder.and(pidPredication, titlePredication);
            } else {
                javax.persistence.criteria.Predicate idPredication = builder.notEqual(root.get("id"), id);
                return builder.and(pidPredication, titlePredication, idPredication);
            }
        }) > 0) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.DEPARTMENT_SAME_NAME);
        }
    }

    private Department updateDepartment(Long orgId, Long id, String title, Long pId, List<String> code, List<String> equivalentCode) {
        for (String c : code) {
            checkUniqueCode(id, orgId, c);
        }
        Department department = require(id);
        checkIsOrgDepartment(orgId, department);
        if (pId != null && pId > 0) {
            checkLevel(orgId, department.getPid(), pId);
            Department parent = require(pId);
            checkIsOrgDepartment(orgId, parent);
            pId = parent.getId();
        } else {
            pId = department.getPid();
        }
        checkUniqueTitleAtSameLevel(pId, id, title);
        department.setTitle(title);
        department.setPid(pId);
        department.setCode(code);
        department.setEquivalentCode(equivalentCode);
        repository.save(department);
        authEventTrigger.departmentUpdate(orgId, TenantContext.getCurrentUserId(), department.getId());
        return department;
    }

    /**
     * 添加部门
     */
    private Department addDepartment(Long orgId, String title, Long pId, List<String> code, List<String> equivalentCode) {
        for (String c : code) {
            checkUniqueCode(null, orgId, c);
        }
        if (pId != null && pId > 0) {
            checkLevel(orgId, 0L, pId);
            Department parent = require(pId);
            checkIsOrgDepartment(orgId, parent);
            pId = parent.getId();
        } else {
            pId = 0L;
        }
        checkUniqueTitleAtSameLevel(pId, null, title);
        Department department = new Department();
        department.setOrgId(orgId);
        department.setTitle(title);
        department.setPid(pId);
        department.setCode(code);
        department.setEquivalentCode(equivalentCode);
        repository.save(department);
        authEventTrigger.departmentCreate(orgId, TenantContext.getCurrentUserId(), department.getId());
        return department;
    }

    public Department addDepartmentNoCheck(Long orgId, String title, Long pId, List<String> code) {
        Department department = new Department();
        department.setOrgId(orgId);
        department.setTitle(title);
        department.setPid(pId);
        department.setCode(code);
        repository.save(department);
        authEventTrigger.departmentCreate(orgId, TenantContext.getCurrentUserId(), department.getId());
        return department;
    }

    public Department updateDepartmentNoCheck(Long id, String title, Long pId, List<String> code) {
        Department department = require(id);
        checkIsCurrentOrg(department);
        Optional.ofNullable(title).ifPresent(department::setTitle);
        Optional.ofNullable(code).ifPresent(department::setCode);
        Optional.ofNullable(pId).ifPresent(department::setPid);
        repository.save(department);
        authEventTrigger.departmentUpdate(department.orgId, TenantContext.getCurrentUserId(), department.getId());
        return department;
    }

    /**
     * 获得子部门的id列表
     */
    public List<Long> getSubDepartmentIdList(Long orgId, Long departmentId, boolean isAdmin) {
        DepartmentTreeDto tree = null;
        if (isAdmin) {
            tree = treeByOrg(orgId);
        } else if (departmentId != null && departmentId > 0L) {
            tree = treeByDepartment(orgId, departmentId, true);
        }
        return treeIds(tree);
    }

    /**
     * 获取指定用户的子部门id列表
     */
    public List<Long> getSubDepartmentIdList(Long orgId, Long userId) {
        List<DepartmentTreeDto> trees = childrenTreeByUser(orgId, userId);
        return mergeDepartmentIds(trees);
    }

    public List<Long> mergeDepartmentIds(List<DepartmentTreeDto> trees) {
        if (CollectionUtils.isEmpty(trees)) {
            return new ArrayList<>();
        }
        Set<Long> ids = new HashSet<>();
        trees.forEach(tree -> {
            Optional.ofNullable(treeIds(tree)).ifPresent(ids::addAll);
        });
        return new ArrayList<>(ids);
    }

    public String formatDepartmentIds(List<Long> departmentIds) {
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            return departmentIds.stream().map(Objects::toString).collect(Collectors.joining(",", "[", "]"));
        } else {
            return "[]";
        }
    }

    /**
     * 获得子部门的id列表(格式化为json数组)
     */
    public String getSubDepartmentIds(Long orgId, Long departmentId, boolean isAdmin) {
        List<Long> ids = getSubDepartmentIdList(orgId, departmentId, isAdmin);
        return formatDepartmentIds(ids);
    }

    /**
     * 获取指定用户的所有可见部门id列表
     */
    public List<Long> getFullDepartmentIdList(Long orgId, Long userId) {
        DepartmentTreeDto tree = fullTreeByUser(orgId, userId);
        return treeIds(tree);
    }

    /**
     * 获得指定id部门，或者指定部门编号的部门，或者根部门
     */
    public Department getByIdOrCodeOrRoot(Long orgId, Long id, String code, boolean defaultGetRoot) {
        Department department = getByOrgIdAndId(orgId,id);
        if (department == null && StringUtils.isNotEmpty(code)) {
            department = getByCode(orgId, code);
        }
        if (department == null && defaultGetRoot) {
            department = getRoot(orgId);
        }
        return department;
    }

    /**
     * 获得指定id的部门
     */
    public Department getByOrgIdAndId(Long orgId, Long id) {
        Department department = get(id);
        if (department != null) {
            if (department.getOrgId() != null && department.getOrgId().equals(orgId)) {
                return department;
            }
        }
        return null;
    }

    /**
     * 获得指定部门编号的部门
     */
    public Department getByCode(Long orgId, String code) {
        if (StringUtils.isNotEmpty(code)) {
            return repository.findFirstByOrgIdAndCode(orgId, code);
        }
        return null;
    }

    public List<Department> getByEquivalentCode(Long orgId, String code) {
        if (StringUtils.isNotEmpty(code)) {
            return repository.findFirstByOrgIdAndEquivalentCode(orgId, code);
        }
        return null;
    }

    /**
     * 获得指定部门编号的部门，或者根部门
     */
    public Department getByCodeOrRoot(Long orgId, String code) {
        Department department = getByCode(orgId, code);
        if (department == null) {
            department = getRoot(orgId);
        }
        return department;
    }

    /**
     * 获得根部门
     */
    public Department getRoot(Long orgId) {
        return repository.findFirstByOrgIdAndPid(orgId, 0L);
    }

    /**
     * 初始化企业部门
     */
    public Department initDepartment(Organization organization) {
        Department root = getRoot(organization.getId());
        if (root != null) {
            return root;
        }
        Department department = addDepartment(organization.getId(), organization.getName(), 0L, null, null);
        updateParentList(organization.getId());
        return department;
    }

    @Override
    public Boolean deleteOne(long departmentId) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        Department department = require(departmentId);
        checkIsOrgDepartment(orgId, department);
        List<Long> ids = treeIds(tree(DepartmentTreeType.CHILDREN, orgId, departmentId));
        if (CollectionUtils.isNotEmpty(ids)) {
            // 这些部门的用户都禁用
            userService.disableUserByDepartmentIds(orgId, ids);
            List<Department> delete = repository.findAllById(ids);
            if (CollectionUtils.isNotEmpty(delete)) {
                repository.deleteAll(delete);
                updateParentList(orgId);
                if (delete.size() == 1) {
                    authEventTrigger.departmentDelete(orgId, userId, departmentId);
                } else {
                    authEventTrigger.departmentRefresh(orgId, userId);
                }
            }
        }
        clearCache(TenantContext.getCurrentTenant());
        return true;
    }

    /**
     * 更新企业的所有部门的上级部门
     */
    public void updateParentList(Long orgId) {
        List<DepartmentTreeDto> list = treeToList(treeByOrg(orgId), Function.identity());
        if (list != null) {
            Map<Long/*id*/, DepartmentTreeDto> map = list.stream().collect(Collectors.toMap(DepartmentTreeDto::getId, i -> i));
            list.forEach(i -> {
                Department department = get(i.getId());
                if (department != null) {
                    List<Long> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    parseFullPath(ids, names, map, i);
                    department.setParentIds(ids.stream().map(Objects::toString).collect(Collectors.joining("/")));
                    department.setParentNames(String.join("/", names));
                    repository.save(department);
                }
            });
        }
    }

    public void parseFullPath(List<Long> ids, List<String> names, Map<Long/*id*/, DepartmentTreeDto> map, DepartmentTreeDto current) {
        DepartmentTreeDto p = current;
        do {
            if (ids != null) {
                ids.add(0, p.getId());
            }
            if (names != null) {
                names.add(0, p.getTitle());
            }
            p = map.get(p.getPid());
        } while (p != null);
    }

    public List<Department> getByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return repository.findAllById(ids);
    }

    public Boolean relatedResponse() {
        authTaskTrigger.departmentRelatedResponse(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), null);
        return true;
    }
}