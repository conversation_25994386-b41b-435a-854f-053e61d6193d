package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.dto.AppVersionDto;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.Industry;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationDto;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.utils.GeneratorHelper;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class OrganizationService extends BaseService<Organization, OrganizationDto, OrganizationRepository> {

    public Organization getCurrent() {
        Long orgId = TenantContext.getCurrentTenant();
        Organization current = null;
        if (orgId != null) {
            current = repository.findById(orgId).orElse(null);
        }
        return current;
    }

    public Organization requireCurrent() {
        Long orgId = TenantContext.getCurrentTenant();
        Organization current = null;
        if (orgId != null) {
            current = repository.findById(orgId).orElse(null);
        }
        if (current == null) {
            throw new BadRequestException("企业不存在");
        }
        return current;
    }

    public Optional<Organization> getById(Long id) {
        return repository.findById(id);
    }

    public Organization requireById(Long orgId) {
        Organization current = null;
        if (orgId != null) {
            current = getById(orgId).orElse(null);
        }
        if (current == null) {
            throw new BadRequestException("企业不存在");
        }
        return current;
    }

    public List<AppType> parseOrgAppTypes(Organization org) {
        List<AppType> activeAppTypes = null;
        if (org != null) {
            String appTypes = org.getAppTypes();
            if (StringUtils.isNotEmpty(appTypes)) {
                activeAppTypes = JsonHelper.toList(appTypes, AppType.class);
            }
        }
        if (activeAppTypes == null) {
            activeAppTypes = new ArrayList<>();
        }
        if (activeAppTypes.isEmpty()) {
            activeAppTypes.add(AppType.cem);
        }
        return activeAppTypes;
    }

    public AppVersion parseOrgVersion(Long orgId) {
        Organization org = get(orgId);
        if (org != null) {
            return parseOrgVersion2(org);
        }
        return AppVersion.EMPTY;
    }

    public AppVersionDto parseOrgVersion(Organization org) {
        if (org != null) {
            String version = org.getVersion();
            if (StringUtils.isNotEmpty(version)) {
                return JsonHelper.toObject(version, AppVersionDto.class);
            }
        }
        return null;
    }

    /**
     * 如果没有版本，则返回 {@link AppVersion#EMPTY}
     */
    public AppVersion parseOrgVersion(AppVersionDto version) {
        if (version != null) {
            return AppVersion.parseByText(version.getCem_version());
        }
        return AppVersion.EMPTY;
    }

    /**
     * 如果没有版本，则返回 {@link AppVersion#EMPTY}
     */
    public AppVersion parseOrgVersion2(Organization org) {
        AppVersionDto version = parseOrgVersion(org);
        if (version != null) {
            return AppVersion.parseByText(version.getCem_version());
        } else {
            return AppVersion.EMPTY;
        }
    }

    /**
     * ctm 调用了此方法
     */
    public OrganizationOptionalLimitDto parseOrgOptionalLimit(Organization org) {
        OrganizationOptionalLimitDto optionalLimit = null;
        if (org != null && StringUtils.isNotEmpty(org.getOptionalLimit())) {
            optionalLimit = JsonHelper.toObject(org.getOptionalLimit(), OrganizationOptionalLimitDto.class);
        }
        if (optionalLimit == null) {
            optionalLimit = parseOrgVersion2(org).getOptionalLimit();
        } else {
            optionalLimit.defaultIfNull(() -> parseOrgVersion2(org));
        }
        return optionalLimit;
    }

    public boolean enableAutoSyncCustomer(Long orgId) {
        Organization org = requireById(orgId);
        return org != null && org.getAutoSyncCustomer() != null && org.getAutoSyncCustomer() == 1;
    }

    /**
     * 初始化企业
     */
    public Organization initOrganization(
            Industry industry,
            String companyName,
            List<AppType> appTypes,
            AppVersion cemVersion,
            AppVersion surveyplusVersion,
            OrganizationOptionalLimitDto optionalLimitDto,
            int maxUsers,
            Date availableDateEnd
    ) {

        LocalDate now = LocalDate.now();
        Date availableDateBegin = DateHelper.toDate(now);
        if (availableDateEnd == null) {
            availableDateEnd = DateHelper.toDate(now.plusYears(1));
        }
        Organization organization = Organization.builder()
                .code(GeneratorHelper.generatorMD5Timestamp16Or32(16))
                .name(companyName)
                .maxUsers(maxUsers)
                .availableDateBegin(availableDateBegin)
                .availableDateEnd(availableDateEnd)
                .appTypes(JsonHelper.toJson(appTypes))
                .optionalLimit(JsonHelper.toJson(optionalLimitDto))
                .version(JsonHelper.toJson(new AppVersionDto(cemVersion.getText(), surveyplusVersion.getText())))
                .industryId(industry.getId())
                .isBlock(0)
                .isTemplate(0)
                .build();

        return repository.save(organization);
    }

    public Organization save(Organization organization) {
        return repository.save(organization);
    }

    public Organization getOrgCode(Long id) {
        return repository.findById(id).orElse(null);
    }

    public Organization getByOrgCode(String orgCode) {
        if (StringUtils.isEmpty(orgCode)) {
            return null;
        }
        return repository.findFirstByCode(orgCode);
    }

    /**
     * 修改企业版本
     * 1 修改版本信息，修改额度限制
     * 2 修改企业超管角色权限
     */
    @Transactional
    public boolean updateVersion(Long orgId, AppVersion targetVersion, Date startDate, Date endDate) {
        if (orgId == null || orgId <= 0 || targetVersion == null || startDate == null || endDate == null) {
            return false;
        }
        Organization org = get(orgId);
        if (org == null) {
            return false;
        }
        // 开始时间不用修改，一般保持为注册时间，购买版本的时候，如果是续费，则开始时间会大于今天，如果修改了，会导致登录时，校验有效时间失败，
        // org.setAvailableDateBegin(startDate);
        // 不论版本是否是否变化，结束时间都需要修改
        org.setAvailableDateEnd(endDate);
        boolean changeVersion = true;
        AppVersionDto versionDto = parseOrgVersion(org);
        if (versionDto == null) {
            versionDto = new AppVersionDto();
            versionDto.setCem_version(AppVersion.EMPTY.getText());
            versionDto.setSurveyplus_version(AppVersion.EMPTY.getText());
        }
        if (targetVersion.getText().equals(versionDto.getCem_version())) {
            // 版本相同
            changeVersion = false;
        } else {
            // 版本变化了，修改版本信息
            versionDto.setCem_version(targetVersion.getText());
            org.setVersion(JsonHelper.toJson(versionDto));
            // 版本变化了，修改额度限制
            org.setOptionalLimit(JsonHelper.toJson(targetVersion.getOptionalLimit()));
        }
        repository.save(org);
        // 1.10.6 超管的权限直接用版本的权限，不再单独配置
//        if (changeVersion) {
//            // 版本变化了，需要修改超管的权限
//            roleService.updateSuperAdminPermissions(orgId, targetVersion);
//        }
        return true;
    }

}
