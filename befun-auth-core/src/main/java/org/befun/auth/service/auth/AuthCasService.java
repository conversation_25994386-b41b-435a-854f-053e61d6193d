package org.befun.auth.service.auth;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.CasAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.CasConfig;
import org.springframework.stereotype.Service;

import static org.befun.auth.constant.ThirdPartyAuthType.CAS;

@Service
public class AuthCasService extends BaseAuthService<CasConfig, CasAuthDto> {

    @Override
    public ThirdPartyAuthType getAuthType() {
        return CAS;
    }

    @Override
    public void afterMapToDto(ThirdPartyAuth auth, CasAuthDto dto) {
        dto.setEnableWhiteList(auth == null ? 0 : auth.getEnableWhiteList());
    }

    @Override
    protected void afterMapToEntity(ThirdPartyAuth auth, CasAuthDto dto) {
        auth.setEnableWhiteList(dto.getEnableWhiteList());
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, CasAuthDto dto) {
        allAuthDto.setCas(dto);
    }
}
