package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.IOpenResourceType;
import org.befun.auth.constant.OpenResourceBelong;
import org.befun.auth.dto.OpenResourceInfo;
import org.befun.auth.entity.OpenResource;
import org.befun.auth.repository.OpenResourceRepository;
import org.befun.auth.service.openresource.IOpenResourceProvider;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.generator.SysConvert;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
public class OpenResourceService {

    private String aesKey;

    @Autowired
    private OpenResourceRepository openResourceRepository;
    @Autowired(required = false)
    private final List<IOpenResourceProvider<?, ?, ?>> openResourceProviderList = new ArrayList<>();
    private final Map<String, IOpenResourceProvider<?, ?, ?>> openResourceProviderMap = new HashMap<>();

    @PostConstruct
    public void init() {
        aesKey = Base64.getEncoder().encodeToString("1qs1iky0deptvmj2".getBytes(StandardCharsets.UTF_8));
        openResourceProviderList.forEach(i -> {
            if (openResourceProviderMap.containsKey(i.getType().name())) {
                throw new RuntimeException("OpenResource type exist: " + i.getType().name());
            }
            openResourceProviderMap.put(i.getType().name(), i);
        });
    }

    /**
     * url https://dev.xmplus.cn/cem/event/operateData/share/{aes(id-md5(params))[.{md5(password)}]}
     */
    @Transactional
    public <P, T extends IOpenResourceType<P>> OpenResourceInfo create(T type, String md5Password, Date expireTime, P params) {
        IOpenResourceProvider<P, T, ?> provider = requireProvider(type.name());
        String formatParams = provider.formatParams(params);
        int secret = 1;
        if (StringUtils.isEmpty(md5Password)) {
            secret = 0;
            md5Password = UUID.randomUUID().toString().replaceAll("-", "");
        }
        OpenResource entity = new OpenResource();
        entity.setOrgId(TenantContext.requireCurrentTenant());
        entity.setUserId(TenantContext.requireCurrentUserId());
        entity.setType(type.name());
        entity.setExpireTime(expireTime);
        entity.setParams(formatParams);
        entity.setPassword(md5Password);
        entity.setSecret(secret);
        openResourceRepository.save(entity);
        String withId = SysConvert.toX(entity.getId());
        String token = secret == 0 ? (withId + "." + entity.getPassword()) : withId;
        return provider.build(entity, URLEncoder.encode(token, StandardCharsets.UTF_8));
    }

    public <P, T extends IOpenResourceType<P>, R> R get(String token) {
        OpenResource entity = requireByToken(token);
        IOpenResourceProvider<P, T, R> provider = requireProvider(entity.getType());
        TenantContext.setCurrentTenant(entity.getOrgId());
        if (provider.getType().getBelong() == OpenResourceBelong.USER) {
            TenantContext.setCurrentUserId(entity.getUserId());
        }
        try {
            provider.checkEnabled();
            P params = provider.parseParams(entity.getParams());
            if (params == null) {
                throw new BusinessException("链接已失效，请联系管理员开启");
            }
            return provider.get(entity.getOrgId(), entity.getUserId(), params);
        } catch (EntityNotFoundException e) {
            throw new BusinessException(provider.getType().label() + "已删除，此链接无效");
        } catch (BadRequestException e) {
            throw new BusinessException(e.getMessage());
        } catch (Throwable e) {
            log.error("open resource get error, token={} ", token, e);
            throw new BusinessException("链接已失效，请联系管理员开启");
        } finally {
            TenantContext.clear();
        }

    }

    private <P, T extends IOpenResourceType<P>, R> IOpenResourceProvider<P, T, R> requireProvider(String type) {
        IOpenResourceProvider<?, ?, ?> provider = openResourceProviderMap.get(type);
        if (provider == null) {
            throw new BusinessException("链接无效");
        }
        return (IOpenResourceProvider<P, T, R>) provider;
    }

    private OpenResource requireByToken(String token) {
        String[] ss = token.split("\\.");
        if (ss.length == 1) {
            throw new BusinessException("请输入密码");
        } else if (ss.length == 2) {
            String withId = ss[0];
            if (withId != null) {
                long id = SysConvert.toDecimal(withId);
                if (id > 0) {
                    OpenResource openResource = openResourceRepository.findById(id).orElse(null);
                    if (openResource != null) {
                        if (openResource.getExpireTime() != null && openResource.getExpireTime().before(new Date())) {
                            throw new BusinessException("链接已过期");
                        }
                        if (!ss[1].equals(openResource.getPassword())) {
                            throw new BusinessException("密码错误");
                        }
                        return openResource;
                    }
                }
            }
        }
        throw new BusinessException("链接无效");
    }
}
