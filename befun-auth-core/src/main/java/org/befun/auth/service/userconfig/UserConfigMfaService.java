package org.befun.auth.service.userconfig;

import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.userconfig.UserConfigBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigDto;
import org.befun.auth.entity.UserConfig;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserConfigMfaService implements BaseUserConfigService {

    @Override
    public UserConfigType type() {
        return UserConfigType.mfa;
    }

    @Override
    public UserConfigBuilderDto getConfigBuilder() {
        return null;
    }

    @Override
    public UserConfigDto getConfig(List<UserConfig> configs, boolean useDefault) {
        UserConfigDto dto = new UserConfigDto();
        if (CollectionUtils.isNotEmpty(configs)) {
            dto.setMfa(configs.get(0).getConfig());
        }
        return dto;
    }

    @Override
    public void checkConfig(UserConfigDto data) {
    }

    @Override
    public String formatConfig(UserConfigDto data) {
        return data.getMfa();
    }
}
