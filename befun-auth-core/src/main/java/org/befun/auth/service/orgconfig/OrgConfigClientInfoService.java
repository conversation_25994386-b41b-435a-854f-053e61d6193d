package org.befun.auth.service.orgconfig;

import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigClientInfoDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrgConfigClientInfoService implements BaseOrgConfigService {

    @Autowired
    private AuthProperties authProperties;


    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.clientInfo;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        config.setClientInfo(new OrgConfigClientInfoDto());
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {

    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        return null;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setClientInfo(source.getClientInfo());
    }

}
