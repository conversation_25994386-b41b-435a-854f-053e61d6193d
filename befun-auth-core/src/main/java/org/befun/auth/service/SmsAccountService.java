package org.befun.auth.service;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.projection.SimpleWalletSms;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.core.template.TemplateEngine;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

@Service("xPackSmsAccountService")
@ConditionalOnProperty(name = "befun.extension.sms.enable-limit", havingValue = "true", matchIfMissing = true)
public class SmsAccountService implements ISmsAccountService {

    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;
    @Autowired
    private OrganizationWalletService organizationWalletService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private OrganizationSmsRecordService organizationSmsRecordService;

    @Override
    @Transactional
    public int recharge(Long orgId, int amount) {
        balance(orgId);
        Long balance = getValueOpt().increment(balanceKey(orgId), amount);
        if (balance != null) {
            syncToDb(orgId);
            return balance.intValue();
        }
        return 0;
    }

    public String formatSmsTemplate(String content) {
        return StringUtils.isNotEmpty(content) ? TemplateEngine.renderTextTemplate(content, Map.of("customer", Map.of("username", "xxxx"), "url", Map.of("code", "xxxxxx"))) : null;
    }

    @Override
    public int calcNumberByText(String content) {
        if (StringUtils.isEmpty(content)) {
            return 0;
        }
        return calcNumberByLength(content.length());
    }

    @Override
    public int calcNumberByLength(int length) {
        if (length <= 70) {
            return 1;
        }
        return length % 67 == 0 ? length / 67 : (length / 67 + 1);
    }

    /**
     * 查询真实余额
     */
    public int balance(Long orgId) {
        String cache = getValueOpt().get(balanceKey(orgId));
        if (NumberUtils.isDigits(cache)) {
            return Integer.parseInt(cache);
        }
        SimpleWalletSms sms = organizationWalletRepository.findFirstWalletSmsByOrgId(orgId);
        int balance;
        if (sms == null || sms.getSms() == null) {
            balance = 0;
        } else {
            balance = sms.getSms();
        }
        getValueOpt().setIfAbsent(balanceKey(orgId), Integer.toString(balance));
        return balance;
    }

    /**
     * 是否有额度
     */
    public boolean hasBalance(Long orgId, Integer cost) {
        int balance = balance(orgId);
        return balance - cost >= 0;
    }

    /**
     * 使用额度，可能会导致余额小于0，
     */
    @Override
    @Transactional
    public int consumer(Long orgId, Integer cost) {
        int balance = balance(orgId);
        if (cost <= 0) {
            return balance;
        }
        Long i = getValueOpt().decrement(balanceKey(orgId), cost);
        syncToDb(orgId);
        return i == null ? balance(orgId) : i.intValue();
    }


    @Transactional
    public int syncToDb(Long orgId) {
        int balance = balance(orgId);
        organizationWalletRepository.updateWalletSms(orgId, balance);
        return balance;
    }

    private HashOperations<String, String, String> getHashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    private ValueOperations<String, String> getValueOpt() {
        return stringRedisTemplate.opsForValue();
    }

    private ZSetOperations<String, String> getZSetOpt() {
        return stringRedisTemplate.opsForZSet();
    }

    private String smsTaskKey(Long orgId, Long taskProgressId) {
        return String.format("sms.task.%d.%d", orgId, taskProgressId);
    }

    private String smsTaskCompletedLockKey(Long orgId, Long taskProgressId) {
        return String.format("sms.task.%d.%d.completed-lock", orgId, taskProgressId);
    }

    private String balanceKey(Long orgId) {
        return String.format("limiter.smsbalance.%d", orgId);
    }

    private static final String JOB_STATUS = "-1";
    private static final String JOB_PLAN_COST = "-2";
    private static final String JOB_REAL_COST = "-3";
    private static final int JOB_COST_INIT = 0;
    private static final int JOB_STATUS_INIT = JOB_COST_INIT + 1;
    private static final int JOB_STATUS_PROCESSING = JOB_STATUS_INIT + 1;
    private static final int JOB_STATUS_COMPLETED = JOB_STATUS_PROCESSING + 1;
    private static final int JOB_STATUS_CANCELED = JOB_STATUS_COMPLETED + 1;

    @Getter
    @AllArgsConstructor
    public enum SmsTaskStatus {
        init(10000),
        success(20000),
        failure(30000),
        cancel(40000);

        private final int i;

        public static SmsTaskStatus parseByI(int i) {
            int ii = i / 10000 * 10000;
            return Arrays.stream(values()).filter((o) -> o.i == ii).findFirst().orElse(null);
        }
    }

    /**
     * 发送短信前再次检查一次，如果预扣的条数用完了，在判断余额是不是足够
     */
    @Override
    public boolean hasBalance(Long orgId, Long taskProgressId, int cost) {
        if (!isProcessing(orgId, taskProgressId)) {
            return false;
        }
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        int planCost = Optional.ofNullable(getZSetOpt().score(smsTaskKey, JOB_PLAN_COST)).map(Double::intValue).orElse(0);
        int realCost = Optional.ofNullable(getZSetOpt().score(smsTaskKey, JOB_REAL_COST)).map(Double::intValue).orElse(0);
        int i = planCost - realCost - cost;
        if (i >= 0) {
            // 预扣的条数足够，可以发送
            return true;
        } else if (balance(orgId) >= i) {
            // 预扣的条数不够，余额足够
            return true;
        } else {
            // 余额不足
            return false;
        }
    }

    @Override
    @Transactional
    public void buildSmsTask(Long orgId, Long userId, Long taskProgressId, String templateContent, Consumer<Integer> progress) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        getZSetOpt().add(smsTaskKey, JOB_STATUS, JOB_STATUS_INIT);
        getZSetOpt().add(smsTaskKey, JOB_PLAN_COST, JOB_COST_INIT); // plan sum cost
        getZSetOpt().add(smsTaskKey, JOB_REAL_COST, JOB_COST_INIT); // real sum cost
        int planCost = calcNumberByText(formatSmsTemplate(templateContent));
        boolean exception = false;
        try {
            progress.accept(planCost);
        } catch (Throwable e) {
            exception = true;
            throw e;
        } finally {
            if (exception) {
                // cancel
                stringRedisTemplate.delete(smsTaskKey);
            } else {
                getZSetOpt().add(smsTaskKey, JOB_STATUS, JOB_STATUS_PROCESSING);
                int totalPlanCost = sumSmsTaskPlanCost(orgId, taskProgressId);
                if (totalPlanCost > 0) {
                    int balance = consumer(orgId, totalPlanCost);
                    organizationSmsRecordService.addBySmsTaskSend(orgId, userId, taskProgressId, templateContent, totalPlanCost, balance);
                } else {
                    // 没有发送任务，直接标记结束
                    smsTaskCompleted(orgId, userId, taskProgressId);
                }
            }
        }
    }

    /**
     * 添加短信任务
     *
     * @param planSmsCost 预计消费的短信数量，通过模板计算出来的
     */
    @Override
    public void addSmsTask(Long orgId, Long taskProgressId, Long jobId, int planSmsCost) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        getZSetOpt().add(smsTaskKey, jobId.toString(), SmsTaskStatus.init.getI());
        getZSetOpt().incrementScore(smsTaskKey, JOB_PLAN_COST, planSmsCost); // plan sum cost
    }

    /**
     * 短信发送成功，修改状态和真实消费短信数量
     *
     * @param realSmsCost 真实消费的短信数量，发送时计算出来的
     */
    @Override
    @Transactional
    public void successSmsTask(Long orgId, Long userId, Long taskProgressId, Long jobId, int realSmsCost) {
        if (isFinished(orgId, taskProgressId)) {
            return;
        }
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        updateSmsTaskStatus(orgId, taskProgressId, jobId, SmsTaskStatus.success);
        getZSetOpt().incrementScore(smsTaskKey, JOB_REAL_COST, realSmsCost); // real sum cost
        if (smsTaskAllProcessed(orgId, taskProgressId)) {
            smsTaskCompleted(orgId, userId, taskProgressId);
        }
    }

    /**
     * 短信发送失败，修改状态和真实消费短信数量0
     */
    @Override
    @Transactional
    public void failureSmsTask(Long orgId, Long userId, Long taskProgressId, Long jobId) {
        if (isFinished(orgId, taskProgressId)) {
            return;
        }
        updateSmsTaskStatus(orgId, taskProgressId, jobId, SmsTaskStatus.failure);
        if (smsTaskAllProcessed(orgId, taskProgressId)) {
            smsTaskCompleted(orgId, userId, taskProgressId);
        }
    }

    /**
     * 标记任务已完成
     */
    private void smsTaskCompleted(Long orgId, Long userId, Long taskProgressId) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        getZSetOpt().add(smsTaskKey, JOB_STATUS, JOB_STATUS_COMPLETED);
        taskFinished(orgId, userId, taskProgressId);
    }

    @Override
    @Transactional
    public void cancelSmsTask(Long orgId, Long userId, Long taskProgressId) {
        if (isProcessing(orgId, taskProgressId)) {
            String smsTaskKey = smsTaskKey(orgId, taskProgressId);
            getZSetOpt().add(smsTaskKey, JOB_STATUS, JOB_STATUS_CANCELED);
            taskFinished(orgId, userId, taskProgressId);
        }
    }

    /**
     * 更新短信任务状态
     */
    private void updateSmsTaskStatus(Long orgId, Long taskProgressId, Long jobId, SmsTaskStatus status) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        String value = jobId.toString();
        getZSetOpt().add(smsTaskKey, value, status.getI());
    }

    private int taskStatus(Long orgId, Long taskProgressId) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        return Optional.ofNullable(getZSetOpt().score(smsTaskKey, JOB_STATUS)).map(Double::intValue).orElse(0);
    }

    private boolean isProcessing(Long orgId, Long taskProgressId) {
        int taskStatus = taskStatus(orgId, taskProgressId);
        return taskStatus == JOB_STATUS_PROCESSING;
    }

    private boolean isFinished(Long orgId, Long taskProgressId) {
        int taskStatus = taskStatus(orgId, taskProgressId);
        return taskStatus == 0 || taskStatus == JOB_STATUS_CANCELED || taskStatus == JOB_STATUS_COMPLETED;
    }

    /**
     * 查询短信任务是否全部执行过，所有的状态都不是 init
     */
    private boolean smsTaskAllProcessed(Long orgId, Long taskProgressId) {
        if (isFinished(orgId, taskProgressId)) {
            return false;
        }
        Long count = getZSetOpt().count(smsTaskKey(orgId, taskProgressId), SmsTaskStatus.init.getI(), SmsTaskStatus.init.getI());
        return count != null && count == 0;
    }

    /**
     * 短信任务预计消费的总条数
     */
    private int sumSmsTaskPlanCost(Long orgId, Long taskProgressId) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        return Optional.ofNullable(getZSetOpt().score(smsTaskKey, JOB_PLAN_COST)).map(Double::intValue).orElse(0);
    }

    /**
     * 结算任务
     */
    private void taskFinished(Long orgId, Long userId, Long taskProgressId) {
        String lockKey = smsTaskCompletedLockKey(orgId, taskProgressId);
        Boolean isLocked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofSeconds(5));
        if (isLocked != null && isLocked) {
            int back = compareSmsTaskCost(orgId, taskProgressId);
            if (back > 0) {
                int balance = consumer(orgId, back);
                organizationSmsRecordService.addBySmsTaskSendAppend(orgId, userId, taskProgressId, back, balance);
            } else if (back < 0) {
                back = Math.abs(back);
                int balance = recharge(orgId, back);
                organizationSmsRecordService.addBySmsTaskSendBack(orgId, userId, taskProgressId, back, balance);
            }
            String smsTaskKey = smsTaskKey(orgId, taskProgressId);
            stringRedisTemplate.expire(smsTaskKey, Duration.ofHours(2));
        }
    }

    private int compareSmsTaskCost(Long orgId, Long taskProgressId) {
        String smsTaskKey = smsTaskKey(orgId, taskProgressId);
        int planCost = Optional.ofNullable(getZSetOpt().score(smsTaskKey, JOB_PLAN_COST)).map(Double::intValue).orElse(0);
        int realCost = Optional.ofNullable(getZSetOpt().score(smsTaskKey, JOB_REAL_COST)).map(Double::intValue).orElse(0);
        return realCost - planCost;
    }

    @Override
    public void addSmsRecordBySource(Long orgId, Long userId, String source, String content, Integer cost, Integer balance) {
        organizationSmsRecordService.addBySmsTaskSendBySource(orgId, userId, source, content, cost, balance);
    }
}
