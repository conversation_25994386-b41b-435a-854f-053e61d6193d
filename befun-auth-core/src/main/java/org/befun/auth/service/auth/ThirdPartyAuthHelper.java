package org.befun.auth.service.auth;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.AuthCountDto;
import org.befun.auth.dto.auth.ThirdPartyAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.AbstractConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ThirdPartyAuthHelper {

    @Autowired
    private List<BaseAuthService<?, ?>> authServiceList;

    private final Map<ThirdPartyAuthType, BaseAuthService<?, ? extends ThirdPartyAuthDto<?>>> authServiceMap = new HashMap<>();

    @PostConstruct
    public void initService() {
        authServiceList.forEach(i -> authServiceMap.put(i.getAuthType(), i));
    }

    @SuppressWarnings("unchecked")
    private <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> S getService(ThirdPartyAuthType type) {
        return (S) authServiceMap.get(type);
    }

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> AllAuthDto getConfigByTypes(String types, String app) {
        AllAuthDto allAuthDto = new AllAuthDto();
        List<ThirdPartyAuthType> allTypes = ThirdPartyAuthType.parseByNames(types);
        allTypes.forEach(i -> {
            S service = getService(i);
            if (service.isSingleType()) {
                service.fillAllAuthDto(allAuthDto, service.getSingle(app));
            } else {
                service.fillAllAuthDto(allAuthDto, service.getList(app));
            }
        });
        return allAuthDto;
    }

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> AuthCountDto countByType(ThirdPartyAuthType type, String app) {
        return new AuthCountDto(getService(type).count(app));
    }

    //****************************************************************************************************************************
    //**************************************************** single ****************************************************************
    //****************************************************************************************************************************

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> ThirdPartyAuth getOrCreateSingleAuth(ThirdPartyAuthType type, String app) {
        S service = getService(type);
        service.requireSingleType();
        return service.getOrCreateSingleEntity(app);
    }

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> D getSingleConfigByType(ThirdPartyAuthType type, String app) {
        S service = getService(type);
        service.requireSingleType();
        return service.getSingle(app);
    }

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> D saveSingleConfigByType(ThirdPartyAuthType type, D dto,String app) {
        S service = getService(type);
        service.requireSingleType();
        dto.setApp(app);
        dto.getConfig().setConfigId(null);
        return service.saveSingle(dto);
    }

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> boolean deleteSingleByType(ThirdPartyAuthType type, String app) {
        S service = getService(type);
        service.requireSingleType();
        return service.deleteSingle(app);
    }

    //****************************************************************************************************************************
    //**************************************************** list ****************************************************************
    //****************************************************************************************************************************

    public <C extends AbstractConfig, D extends ThirdPartyAuthDto<C>, S extends BaseAuthService<C, D>> boolean deleteByConfigId(ThirdPartyAuthType type, Long configId, Map<String, Object> extParams) {
        S service = getService(type);
        service.requireListType();
        return service.deleteByConfigId(configId, extParams);
    }
}
