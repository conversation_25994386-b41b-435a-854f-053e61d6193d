package org.befun.auth.service.orgconfig;

import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrgConfigEventShareService implements BaseOrgConfigService {

    @Autowired
    private AuthProperties authProperties;


    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.eventShare;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        Boolean defaultConfig = Boolean.valueOf(authProperties.getOrgDefaultConfig().getOrDefault(type().name(), "false"));
        config.setEventShare(defaultConfig);
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {

    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        return null;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setEventShare(source.getEventShare());
    }

}
