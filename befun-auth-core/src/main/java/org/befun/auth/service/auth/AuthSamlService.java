package org.befun.auth.service.auth;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.SamlAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.SamlConfig;
import org.springframework.stereotype.Service;

import static org.befun.auth.constant.ThirdPartyAuthType.SAML;

@Service
public class AuthSamlService extends BaseAuthService<SamlConfig, SamlAuthDto> {

    @Override
    public ThirdPartyAuthType getAuthType() {
        return SAML;
    }

    @Override
    public void afterMapToDto(ThirdPartyAuth auth, SamlAuthDto dto) {
        dto.setEnableWhiteList(auth == null ? 0 : auth.getEnableWhiteList());
    }

    @Override
    protected void afterMapToEntity(ThirdPartyAuth auth, SamlAuthDto dto) {
        auth.setEnableWhiteList(dto.getEnableWhiteList());
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, SamlAuthDto dto) {
        allAuthDto.setSaml(dto);
    }
}
