package org.befun.auth.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AiPointRecordStatus;
import org.befun.auth.constant.AiPointRecordType;
import org.befun.auth.constant.LockKeys;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.auth.entity.OrganizationAiPointRecordDto;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.OrganizationAiPointRecordRepository;
import org.befun.core.dto.fillvalue.FillValue;
import org.befun.core.service.BaseService;
import org.befun.extension.service.LockRunnableHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrganizationAiPointRecordService extends BaseService<OrganizationAiPointRecord, OrganizationAiPointRecordDto, OrganizationAiPointRecordRepository> {

    @Autowired
    private UserService userService;
    @Autowired
    private LockRunnableHelper lockRunnableHelper;
    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    public void afterMapToDto(List<OrganizationAiPointRecord> entity, List<OrganizationAiPointRecordDto> dto) {
        userService.fillValueById(dto, SimpleUser::fromUser, FillValue.create(OrganizationAiPointRecordDto::getCreateUserId, OrganizationAiPointRecordDto::setCreateUser));
        Set<Long> warningIds = new HashSet<>();
        Set<Long> textAnalysisIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(entity)) {
            entity.forEach(i -> {
                if (i.getType() == AiPointRecordType.warning) {
                    if (i.getSourceId() != null && i.getSourceId() > 0) {
                        warningIds.add(i.getSourceId());
                    }
                } else if (i.getType() == AiPointRecordType.text_analysis) {
                    if (i.getSourceId() != null && i.getSourceId() > 0) {
                        textAnalysisIds.add(i.getSourceId());
                    }
                }
            });
        }
        Map<Long, String> warningProjectMap = findWarningProjectMap(warningIds);
        Map<Long, String> textAnalysisProjectMap = findTextAnalysisProjectMap(textAnalysisIds);
        dto.forEach(i -> {
            if (i.getType() == AiPointRecordType.warning) {
                i.setProjectName(warningProjectMap.get(i.getSourceId()));
            } else if (i.getType() == AiPointRecordType.text_analysis) {
                i.setProjectName(textAnalysisProjectMap.get(i.getSourceId()));
            }
        });
    }

    private Map<Long, String> findWarningProjectMap(Set<Long> ids) {
        String sql = null;
        if (!ids.isEmpty()) {
            sql = "select id, title name from event_monitor_rules where id in(" + ids.stream().map(Objects::toString).collect(Collectors.joining(",")) + ")";
        }
        return findProjectMap(sql);
    }

    private Map<Long, String> findTextAnalysisProjectMap(Set<Long> ids) {
        String sql = null;
        if (!ids.isEmpty()) {
            sql = "select id, name from bi_text_project where id in(" + ids.stream().map(Objects::toString).collect(Collectors.joining(",")) + ")";
        }
        return findProjectMap(sql);
    }

    private Map<Long, String> findProjectMap(String sql) {
        Map<Long, String> map = new HashMap<>();
        if (StringUtils.isNotEmpty(sql)) {
            jdbcTemplate.query(sql, (rs) -> {
                map.put(rs.getLong("id"), rs.getString("name"));
            });
        }
        return map;
    }

    /**
     * 添加记录-充值AI点数
     */
    public OrganizationAiPointRecord addByRecharge(Long orgId, Long userId, Integer aiPoint) {
        OrganizationAiPointRecord entity = new OrganizationAiPointRecord();
        entity.setOrgId(orgId);
        entity.setCreateUserId(userId);
        entity.setType(AiPointRecordType.recharge);
        entity.setAmount(aiPoint);
        entity.setStatus(AiPointRecordStatus.init);
        repository.save(entity);
        return entity;
    }

    @Transactional
    public void updateStatus(Long recordId, AiPointRecordStatus status) {
        repository.updateStatusById(status, recordId);
    }

    @Transactional
    public void rechargeSuccess(Long recordId) {
        updateStatus(recordId, AiPointRecordStatus.success);
    }

    @Transactional
    public void rechargeFailure(Long recordId) {
        updateStatus(recordId, AiPointRecordStatus.failure);
    }

    @Transactional
    public void rechargeCancel(Long recordId) {
        updateStatus(recordId, AiPointRecordStatus.cancel);
    }

    @Transactional
    public OrganizationAiPointRecord getOrAddRecord(Long orgId, Long userId, AiPointRecordType type, Long sourceId) {
        return getOrAddRecord(orgId, userId, type, sourceId, AiPointRecordStatus.success);
    }

    @Transactional
    public OrganizationAiPointRecord getOrAddRecord(Long orgId, Long userId, AiPointRecordType type, Long sourceId, AiPointRecordStatus status) {
        OrganizationAiPointRecord find = repository.findFirstByOrgIdAndTypeAndSourceId(orgId, type, sourceId);
        if (find == null) {
            return lockRunnableHelper.runOrThrow(LockKeys.ai_point_record, List.of(type, sourceId), () -> {
                OrganizationAiPointRecord entity = new OrganizationAiPointRecord();
                entity.setOrgId(orgId);
                entity.setCreateUserId(userId);
                entity.setType(type);
                entity.setSourceId(sourceId);
                entity.setAmount(0);
                entity.setStatus(status);
                repository.save(entity);
                return entity;
            });
        }
        return find;
    }

    @Transactional
    public void appendAmount(Long recordId, Integer amount) {
        repository.updateAmountById(amount, recordId);
    }

}
