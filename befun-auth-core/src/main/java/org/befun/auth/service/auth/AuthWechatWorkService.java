package org.befun.auth.service.auth;

import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.WechatWorkAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.ThirdPartyUserService;
import org.befun.auth.service.auth.config.WechatWorkConfig;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static org.befun.auth.constant.ThirdPartyAuthType.WECHAT_WORK;

@Service
public class AuthWechatWorkService extends BaseAuthService<WechatWorkConfig, WechatWorkAuthDto> {

    @Autowired
    private ThirdPartyUserService thirdPartyUserService;

    @Override
    public ThirdPartyAuthType getAuthType() {
        return WECHAT_WORK;
    }

    @Override
    public String buildSource(Long orgId, WechatWorkConfig config) {
        return buildSource(config.getCompanyId());
    }

    private String buildSource(String companyId) {
        return WECHAT_WORK.getSourcePrefix() + companyId;
    }

    @Override
    public ThirdPartyAuth getOrCreateSingleEntity(String app) {
        throw new BadRequestException();
    }

    @Override
    public WechatWorkAuthDto getOrCreateSingle(String app) {
        throw new BadRequestException();
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, WechatWorkAuthDto dto) {
        allAuthDto.setWechatWork(dto);
    }

    @Override
    @Transactional
    public boolean deleteSingle(String app) {
        ThirdPartyAuth entity = getSingleEntity(app);
        delete(entity);
        return true;
    }

    private void delete(ThirdPartyAuth entity) {
        if (entity == null) {
            throw new BadRequestException("企业微信未绑定");
        }
        // 删除 third party user
        thirdPartyUserService.deleteByAuth(entity.orgId, entity.getSource(), entity.getApp());
        // 删除 third party auth
        thirdPartyAuthService.delete(entity);
    }

    @Transactional
    public Long delete(String app, String companyId) {
        ThirdPartyAuth entity = thirdPartyAuthService.getBySource(WECHAT_WORK.getSourcePrefix() + companyId, WECHAT_WORK, app);
        if (entity != null) {
            delete(entity);
            return entity.getOrgId();
        }
        return null;
    }
}
