package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.dto.userconfig.UserConfigBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigDto;
import org.befun.auth.entity.UserConfig;
import org.befun.auth.repository.UserConfigRepository;
import org.befun.auth.service.userconfig.BaseUserConfigService;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

@Slf4j
@Service
public class UserConfigService {

    @Autowired
    private UserConfigRepository userConfigRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ConversionService conversionService;
    @Autowired
    private List<BaseUserConfigService> userConfigServices;
    private final Map<UserConfigType, BaseUserConfigService> userConfigServiceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        userConfigServices.forEach(i -> userConfigServiceMap.put(i.type(), i));
    }

    private BaseUserConfigService getService(UserConfigType type) {
        return Optional.ofNullable(userConfigServiceMap.get(type)).orElseThrow(() -> new UnsupportedOperationException("不支持的配置类型"));
    }

    private long checkExistsCurrentOrg() {
        long orgId = TenantContext.requireCurrentTenant();
        organizationService.checkExistsEntity(orgId);
        return orgId;
    }

    private long checkExistsCurrentUser() {
        long userId = TenantContext.requireCurrentUserId();
        userService.checkExistsEntity(userId);
        return userId;
    }

    private List<UserConfig> getByType(long orgId, long userId, UserConfigType type) {
        return userConfigRepository.findByOrgIdAndUserIdAndType(orgId, userId, type.name());
    }

    private UserConfig getBySingleType(long orgId, long userId, UserConfigType type) {
        List<UserConfig> configs = getByType(orgId, userId, type);
        if (CollectionUtils.isNotEmpty(configs)) {
            return configs.get(0);
        } else {
            return null;
        }
    }

    public UserConfigBuilderDto getConfigBuilder(UserConfigType type) {
        return getService(type).getConfigBuilder();
    }

    public UserConfigDto getConfig(long orgId, long userId, UserConfigType type) {
        return getService(type).getConfig(getByType(orgId, userId, type), false);
    }

    public UserConfigDto getConfig(UserConfigType type) {
        long orgId = checkExistsCurrentOrg();
        long userId = checkExistsCurrentUser();
        return getConfig(orgId, userId, type);
    }

    public <C> C getConfigInfo(long orgId, long userId, UserConfigType type, Function<UserConfigDto, C> getInfo, C defaultInfo) {
        UserConfigDto config = getConfig(orgId, userId, type);
        if (config == null) {
            return defaultInfo;
        } else {
            return getInfo.apply(config);
        }
    }

    public UserConfigDto saveConfig(UserConfigType type, UserConfigDto config) {
        getService(type).checkConfig(config);
        long orgId = checkExistsCurrentOrg();
        long userId = checkExistsCurrentUser();
        return saveConfig(orgId, userId, type, config);
    }


    private UserConfigDto saveConfig(long orgId, long userId, @NotNull UserConfigType type, UserConfigDto config) {
        UserConfig entity = new UserConfig();
        entity.setOrgId(orgId);
        entity.setUserId(userId);
        entity.setType(type.name());
        entity.setConfig(getService(type).formatConfig(config));
        userConfigRepository.save(entity);
        return getConfig(orgId, userId, type);
    }

    /**
     * 保存或者更新单条用户配置
     */
    public UserConfigDto saveOrUpdateSingleConfig(long orgId, long userId, @NotNull UserConfigType type, UserConfigDto config) {
        UserConfig entity = getBySingleType(orgId, userId, type);
        if (entity == null) {
            entity = new UserConfig();
            entity.setOrgId(orgId);
            entity.setUserId(userId);
            entity.setType(type.name());
        }
        entity.setConfig(getService(type).formatConfig(config));
        userConfigRepository.save(entity);
        return getConfig(orgId, userId, type);
    }

    public boolean deleteConfig(UserConfigType type, Long configId) {
        UserConfig entity = userConfigRepository.findById(configId).orElse(null);
        if (entity != null && entity.getType() != null && type.name().equals(entity.getType())) {
            userConfigRepository.delete(entity);
        }
        return true;
    }
}
