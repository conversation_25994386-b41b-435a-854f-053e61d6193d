package org.befun.auth.service;

import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.FeatureAction;
import org.befun.core.utils.BeanUtility;
import org.befun.core.utils.EnumHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.service.XpackConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FeatureActionService {

    @Autowired
    private XpackConfigService xpackConfigService;
    @Autowired
    private OrganizationService organizationService;

    public static boolean canRun(long orgId, FeatureAction action, boolean defaultValue) {
        FeatureActionService service = BeanUtility.getBean(FeatureActionService.class);
        if (service == null) {
            return defaultValue;
        }
        return service.canRun(orgId, action);
    }

    public boolean canRun(long orgId, FeatureAction action) {
        AppVersion minVersion = action.getDefaultMinVersion();
        XpackConfig config = xpackConfigService.getConfigByTypeAndSubType(XPackAppType.FEATURE_ACTION, action.name());
        if (config != null) {
            AppVersion parsed = EnumHelper.parse(AppVersion.values(), config.getConfig());
            if (parsed != null) {
                minVersion = parsed;
            }
        }
        AppVersion appVersion = organizationService.parseOrgVersion(orgId);
        return appVersion.ordinal() >= minVersion.ordinal();
    }
}
