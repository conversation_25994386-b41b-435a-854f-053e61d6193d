package org.befun.auth.service.auth;

import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.auth.dto.auth.ThirdPartyAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.auth.config.AbstractConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@SuppressWarnings("unchecked")
public abstract class BaseAuthService<C extends AbstractConfig, D extends ThirdPartyAuthDto<C>> {

    @Autowired
    protected ThirdPartyAuthService thirdPartyAuthService;

    public abstract ThirdPartyAuthType getAuthType();

    public void fillAllAuthDto(AllAuthDto allAuthDto, D dto) {
        // single type
    }

    public void fillAllAuthDto(AllAuthDto allAuthDto, List<D> dto) {
        // list type
    }

    public boolean isSingleType() {
        return getAuthType().isSingleType();
    }

    public void requireSingleType() {
        if (!isSingleType()) {
            throw new BadRequestException();
        }
    }

    public void requireListType() {
        if (isSingleType()) {
            throw new BadRequestException();
        }
    }

    /**
     * 默认的source是由类型前缀和企业id组成
     */
    public String buildSource(ThirdPartyAuth entity, D dto) {
        return getAuthType().buildSource(entity.getOrgId());
    }

    /**
     * 默认的source是由类型前缀和企业id组成
     */
    public String buildSource(Long orgId, C config) {
        return getAuthType().buildSource(orgId);
    }

    public void checkConfig(C config) {
    }

    public C getConfig(String app) {
        requireSingleType();
        return getConfig(getSingleEntity(app));
    }

    public C getConfig(Long id) {
        return getConfig(thirdPartyAuthService.get(id));
    }

    public C getConfig(ThirdPartyAuth entity) {
        if (entity != null) {
            C config = (C) JsonHelper.toObject(entity.getConfig(), getAuthType().getConfigClass());
            if (config != null) {
                config.setConfigId(entity.getId());
                return config;
            }
        }
        return null;
    }

    public List<C> getListConfig(String app) {
        requireListType();
        return getListConfig(getListEntity(app));
    }

    public List<C> getListConfig(List<ThirdPartyAuth> list) {
        List<C> configs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> {
                Optional.ofNullable(getConfig(i)).ifPresent(configs::add);
            });
        }
        return configs;
    }

    public String formatConfig(D dto) {
        if (dto != null) {
            return formatConfig(dto.getConfig());
        }
        return null;
    }

    public String formatConfig(C config) {
        if (config != null) {
            config.setConfigId(null);
            return JsonHelper.toJson(config);
        }
        return null;
    }

    public long count(String app) {
        long count = thirdPartyAuthService.countAuth(getAuthType(), app);
        if (isSingleType()) {
            return Math.max(count, 1);
        } else {
            return count;
        }
    }

    public List<ThirdPartyAuth> getListEntity(String app) {
        requireListType();
        return thirdPartyAuthService.getListAuth(getAuthType(), app);
    }

    public ThirdPartyAuth getSingleEntity(String app) {
        requireSingleType();
        return thirdPartyAuthService.getSingleAuth(getAuthType(), app);
    }

    public ThirdPartyAuth getOrCreateSingleEntity(String app) {
        requireSingleType();
        ThirdPartyAuth auth = getSingleEntity(app);
        if (auth == null) {
            auth = add(emptyDto(app));
        }
        return auth;
    }

    public List<D> getList(String app) {
        requireListType();
        return mapToDto(getListEntity(app), app);
    }

    public D getListFirst(String app) {
        requireListType();
        ThirdPartyAuth entity = thirdPartyAuthService.getSingleAuth(getAuthType(), app);
        return mapToDto(entity, app);
    }

    public D getSingle(String app) {
        requireSingleType();
        return mapToDto(getSingleEntity(app), app);
    }

    public D getOrCreateSingle(String app) {
        requireSingleType();
        return mapToDto(getOrCreateSingleEntity(app), app);
    }

    public D saveSingle(D dto) {
        requireSingleType();
        ThirdPartyAuth auth = getSingleEntity(dto.getApp());
        checkConfig(dto.getConfig());
        if (auth == null) {
            auth = add(dto);
        } else {
            update(dto, auth);
        }
        return mapToDto(auth, dto.getApp());
    }

    public List<D> mapToDto(List<ThirdPartyAuth> entity, String app) {
        if (CollectionUtils.isEmpty(entity)) {
            return null;
        }
        return entity.stream().map(i -> mapToDto(i, app)).collect(Collectors.toList());
    }

    public D mapToDto(ThirdPartyAuth entity, String app) {
        if (entity == null) {
            return emptyDto(app);
        }
        D dto = newDto();
        dto.setApp(entity.getApp());
        dto.setAuthType(getAuthType());
        dto.setConfig(getConfig(entity));
        afterMapToDto(entity, dto);
        if (dto.getConfig() != null && dto.getConfig() instanceof IClearConfigSecret) {
            ((IClearConfigSecret) dto.getConfig()).clearSecret();
        }
        return dto;
    }

    public void afterMapToDto(ThirdPartyAuth auth, D dto) {
    }

    private ThirdPartyAuth add(D dto) {
        ThirdPartyAuth auth = mapToAddEntity(TenantContext.getCurrentTenant(), dto);
        thirdPartyAuthService.save(auth);
        return auth;
    }

    public ThirdPartyAuth mapToAddEntity(long orgId, D dto) {
        ThirdPartyAuth auth = newEntity(orgId, dto.getApp(), dto.getConfig(), dto.getRemark());
        afterMapToEntity(auth, dto);
        return auth;
    }

    public ThirdPartyAuth add(long orgId, String app, C config, String remark) {
        ThirdPartyAuth auth = newEntity(orgId, app, config, remark);
        thirdPartyAuthService.save(auth);
        return auth;
    }

    private ThirdPartyAuth newEntity(long orgId, String app, C config, String remark) {
        ThirdPartyAuth auth = new ThirdPartyAuth();
        auth.setApp(app);
        auth.setAuthType(getAuthType());
        auth.setOrgId(orgId);
        auth.setSource(buildSource(orgId, config));
        auth.setRemark(remark);
        auth.setEnableWhiteList(0);
        auth.setConfig(formatConfig(config));
        return auth;
    }

    protected void afterMapToEntity(ThirdPartyAuth auth, D dto) {
    }

    private void update(D dto, ThirdPartyAuth auth) {
        mapToUpdateEntity(auth, dto);
        thirdPartyAuthService.save(auth);
    }

    public void mapToUpdateEntity(ThirdPartyAuth auth, D dto) {
        auth.setConfig(formatConfig(dto));
        afterMapToEntity(auth, dto);
    }

    public ThirdPartyAuth updateConfig(ThirdPartyAuth auth, C config) {
        auth.setModifyTime(new Date());
        auth.setConfig(formatConfig(config));
        thirdPartyAuthService.save(auth);
        return auth;
    }

    public ThirdPartyAuth updateConfig(ThirdPartyAuth auth, Supplier<C> createIfNull, Function<C, Boolean> update) {
        C config = getConfig(auth);
        boolean save = false;
        if (config == null && createIfNull != null) {
            config = createIfNull.get();
            save = true;
        }
        if (config != null && (update.apply(config) || save)) {
            updateConfig(auth, config);
        }
        return auth;
    }

    private D newDto() {
        try {
            return (D) getAuthType().getDtoClass().getConstructor().newInstance();
        } catch (InvocationTargetException | NoSuchMethodException | InstantiationException |
                 IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    private C newConfig() {
        try {
            return (C) getAuthType().getConfigClass().getConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    public D emptyDto(String app) {
        D dto = newDto();
        dto.setApp(app);
        dto.setAuthType(getAuthType());
        dto.setConfig(newConfig());
        afterMapToDto(null, dto);
        return dto;
    }

    public boolean deleteSingle(String app) {
        return false;
    }

    public boolean deleteByConfigId(Long configId, Map<String, Object> extParams) {
        return false;
    }

}
