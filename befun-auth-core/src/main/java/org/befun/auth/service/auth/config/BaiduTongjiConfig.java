package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class BaiduTongjiConfig extends AbstractConfig implements IClearConfigSecret {

    // 接口配置参数
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "apiKey", required = true)
    private String apiKey;

    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "apiSecret", required = true)
    private String apiSecret;

    @NotNull
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "站点id", required = true)
    private Integer siteId;

    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "站点域名", required = true)
    private String siteDomain;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "配置状态：0 未配置 1 正常 2 异常")
    private Integer status = 0;

    // 百度统计授权返回参数
    @Schema(hidden = true)
    private String refreshToken;
    @Schema(hidden = true)
    private String accessToken;
    @Schema(hidden = true)
    private Integer expiresIn;
    @Schema(hidden = true)
    private String sessionKey;
    @Schema(hidden = true)
    private String sessionSecret;
    @Schema(hidden = true)
    private String scope;
    @Schema(hidden = true)
    private String code;

    @Override
    public void clearSecret() {
        refreshToken = null;
        accessToken = null;
        expiresIn = null;
        sessionKey = null;
        sessionSecret = null;
        scope = null;
        code = null;
    }

}

