package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class CasConfig extends AbstractConfig {

    @JsonView(ResourceViews.Basic.class)
    @NotEmpty
    @Schema(description = "cas服务器地址", required = true)
    private String casUrl = "";

    @JsonView(ResourceViews.Basic.class)
    @NotEmpty
    @Schema(description = "cas版本:v1,v2,v3", required = true)
    private String casVersion = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "重定向地址", required = false)
    private String logoutRedirectUrl = "";

    @JsonView(ResourceViews.Basic.class)
    @NotEmpty
    @Schema(description = "参数名：id", required = true)
    private String casParamId = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：姓名")
    private String casParamName = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：部门")
    private String casParamDepartment = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：手机")
    private String casParamMobile = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：邮箱")
    private String casParamEmail = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：员工号")
    private String casParamEmployeeNo = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：角色编号")
    private String paramRoleCode = "";

}
