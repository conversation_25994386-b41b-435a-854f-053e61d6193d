package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.SimpleOrganization;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationConfig;
import org.befun.auth.repository.OrganizationConfigRepository;
import org.befun.auth.service.orgconfig.BaseOrgConfigService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.ConversionService;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;

@Slf4j
@Service
public class OrganizationConfigService {

    @Autowired
    private OrganizationConfigRepository organizationConfigRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ConversionService conversionService;
    @Autowired
    private List<BaseOrgConfigService> orgConfigServices;
    private final Map<OrganizationConfigType, BaseOrgConfigService> orgConfigServiceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        orgConfigServices.forEach(i -> orgConfigServiceMap.put(i.type(), i));
    }

    private BaseOrgConfigService getService(OrganizationConfigType type) {
        return Optional.ofNullable(orgConfigServiceMap.get(type)).orElseThrow(() -> new UnsupportedOperationException("不支持的配置类型"));
    }

    public SimpleOrganization getSimpleOrgInfo(Organization org) {
        SimpleOrganization simpleOrganization = new SimpleOrganization();
        simpleOrganization.setId(org.getId());
        simpleOrganization.setCode(org.getCode());
        simpleOrganization.setName(org.getName());
        simpleOrganization.setOptionalLimit(org.getOptionalLimit());
        Optional.ofNullable(getConfig(org.getId(), OrganizationConfigType.baseInfo))
                .flatMap(config -> Optional.ofNullable(config.getBaseInfo()))
                .ifPresent(baseInfo -> {
                    simpleOrganization.setLogo(baseInfo.getLogo());
                });
        return simpleOrganization;
    }

    private long checkExistsCurrentOrg() {
        long orgId = TenantContext.requireCurrentTenant();
        organizationService.checkExistsEntity(orgId);
        return orgId;
    }

    private OrganizationConfig getByType(long orgId, OrganizationConfigType type) {
        return organizationConfigRepository.findFirstByOrgIdAndType(orgId, type.name());
    }

    public OrgConfigDto getConfig(long orgId, OrganizationConfigType type) {
        OrganizationConfig entity = getByType(orgId, type);
        OrgConfigDto config = entity == null ? null : JsonHelper.toObject(entity.getConfig(), OrgConfigDto.class);
        if (config != null) {
            getService(type).correction(config);
        }
        return config;
    }

    public OrgConfigDto getConfig(String types) {
        List<OrganizationConfigType> parsedTypes = EnumHelper.parseList(OrganizationConfigType.values(), types);
        if (parsedTypes.isEmpty()) {
            return null;
        }
        long orgId = checkExistsCurrentOrg();
        OrgConfigDto config = new OrgConfigDto();
        parsedTypes.forEach(type -> {
            OrgConfigDto source = getConfig(orgId, type);
            if (source != null) {
                getService(type).copyConfig(source, config);
            }
        });
        return config;
    }

    public OrgConfigDto getConfig(OrganizationConfigType type) {
        return getConfig(checkExistsCurrentOrg(), type);
    }

    public OrgConfigDto getOrDefaultConfig(String types) {
        List<OrganizationConfigType> parsedTypes = EnumHelper.parseList(OrganizationConfigType.values(), types);
        if (parsedTypes.isEmpty()) {
            return null;
        }
        long orgId = checkExistsCurrentOrg();
        OrgConfigDto config = new OrgConfigDto();
        parsedTypes.forEach(type -> {
            OrgConfigDto source = getConfig(orgId, type);
            BaseOrgConfigService service = getService(type);
            if (source == null) {
                source = service.getDefaultConfig();
            }
            if (source != null) {
                service.copyConfig(source, config);
            }
        });
        return config;
    }

    public OrgConfigDto getOrDefaultConfig(OrganizationConfigType type) {
        return Optional.ofNullable(getConfig(type)).orElse(getService(type).getDefaultConfig());
    }

    public <C> C getConfigInfo(long orgId, OrganizationConfigType type, Function<OrgConfigDto, C> getInfo, C defaultInfo) {
        OrgConfigDto config = getConfig(orgId, type);
        if (config == null) {
            return defaultInfo;
        } else {
            return getInfo.apply(config);
        }
    }

    public OrgConfigBuilderDto getConfigBuilder(OrganizationConfigType type, boolean defaultConfig) {
        OrgConfigDto config = defaultConfig ? getService(type).getDefaultConfig() : getOrDefaultConfig(type);
        return getService(type).getConfigBuilder(config);
    }

    public OrgConfigBuilderDto resetConfig(OrganizationConfigType type) {
        long orgId = checkExistsCurrentOrg();
        OrganizationConfig entity = getByType(orgId, type);
        OrgConfigDto config = saveConfig(orgId, type, entity, null);
        return getService(type).getConfigBuilder(config);
    }

    public OrgConfigDto saveConfig(Set<OrganizationConfigType> types, OrgConfigDto config) {
        if (CollectionUtils.isNotEmpty(types)) {
            OrgConfigDto target = new OrgConfigDto();
            types.forEach(type -> {
                OrgConfigDto source = saveConfig(type, config);
                getService(type).copyConfig(source, target);
            });
            return target;
        }
        return null;
    }

    public OrgConfigDto saveConfig(OrganizationConfigType type, OrgConfigDto config) {
        getService(type).checkConfig(config);
        long orgId = checkExistsCurrentOrg();
        OrganizationConfig entity = getByType(orgId, type);
        return saveConfig(orgId, type, entity, config);
    }

    private OrgConfigDto saveConfig(long orgId, @NotNull OrganizationConfigType type, @Nullable OrganizationConfig entity, OrgConfigDto config) {
        if (entity == null) {
            entity = new OrganizationConfig();
            entity.setOrgId(orgId);
            entity.setType(type.name());
        }
        if (config == null) {
            config = getService(type).getDefaultConfig();
        }
        entity.setConfig(JsonHelper.toJson(true, config));
        organizationConfigRepository.save(entity);
        return config;
    }
}
