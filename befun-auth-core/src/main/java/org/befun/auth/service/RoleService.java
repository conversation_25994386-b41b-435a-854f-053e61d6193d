package org.befun.auth.service;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.AppVersionPermissions;
import org.befun.auth.constant.Permissions;
import org.befun.auth.constant.RoleType;
import org.befun.auth.dto.RoleRequestDto;
import org.befun.auth.dto.RoleResponseDto;
import org.befun.auth.dto.RoleTemplateResponseDto;
import org.befun.auth.dto.RoleUserRequestDto;
import org.befun.auth.dto.role.MenuTreeDto;
import org.befun.auth.dto.role.RoleSimpleDto;
import org.befun.auth.entity.*;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.utils.ListHelper;
import org.befun.auth.utils.StringHelper;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.befun.auth.constant.AuthToastMessage.ROLE_CODE_EXISTS;
import static org.befun.auth.constant.AuthToastMessage.ROLE_NAME_EXISTS;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;

@Service
@Validated
public class RoleService extends BaseService<Role, RoleDto, RoleRepository> {

    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private MenuService menuService;
    @Lazy
    @Autowired
    private UserService userService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private TreeConvertService treeConvertService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    public Boolean enable(Long id) {
        Role role = require(id);
        role.setEnable(true);
        save(role);
        return true;
    }

    @Override
    public void afterMapToDto(List<Role> entityList, List<RoleDto> dtoList) {
        if (CollectionUtils.isEmpty(entityList) || CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        List<Long> ids = entityList.stream().map(BaseEntity::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            Map<Long/*roleId*/, Set<Long>> roleMap = userRoleService.getGroupMapListByPropertyIds(
                    "role",
                    ids,
                    i -> Optional.ofNullable(i.getRole()).map(BaseEntity::getId).orElse(null),
                    i -> Optional.ofNullable(i.getUser()).map(BaseEntity::getId).orElse(null),
                    HashSet::new
            );
            dtoList.forEach(i -> {
                Optional.ofNullable(roleMap.get(i.getId())).ifPresent(users -> {
                    i.setCountUser(users.size());
                });
            });
        }
    }

    /**
     * 获得超管菜单的模板
     */
    public RoleTemplateResponseDto template() {
        return new RoleTemplateResponseDto(superAdminTree(true));
    }

    /**
     * 超管的权限 等于 版本的权限
     */
    public List<MenuTreeDto> superAdminTree(boolean allSelect) {
        Long orgId = TenantContext.getCurrentTenant();
        Role superAdminRole = getSuperAdminByOrg(orgId);
        if (superAdminRole == null) {
            return new ArrayList<>();
        }
        List<Menu> menus = menuService.getAllMenus();
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        List<String> permissions = getSuperAdminPermissions(orgId);
        Set<String> fullActions = new HashSet<>(); // 超管拥有的所有菜单
        permissions.forEach(i -> {
            fullActions.add(i);
            String[] split = i.split("/");
            if (split.length == 4) {
                fullActions.add(String.format("/%s", split[1]));
                fullActions.add(String.format("/%s/%s", split[1], split[2]));
            }
        });
        menus = menus.stream()
                .filter(
                        i -> i.getDisplay() == 1
                                && StringUtils.isNotEmpty(i.getFullPath())
                                && fullActions.contains(i.getFullPath())
                )
                .collect(Collectors.toList());
        return treeConvertService.listToTree(menus, i -> MenuTreeDto.formMenu(i, allSelect));
    }

    /**
     * 获得角色详情
     */
    public RoleResponseDto detail(Long roleId) {
        Role role = require(roleId);
        if (role.getEditable() == null) {
            role.setEditable(0);
        }
        List<MenuTreeDto> menuTree = superAdminTree(false);
        Map<String, MenuTreeDto> menuMap = treeConvertService.treeToMap(menuTree, MenuTreeDto::getFullPath, Function.identity());
        List<String> permissions = null;
        if (isSuperAdmin(role)) {
            permissions = getSuperAdminPermissions(role.getOrgId());
        } else {
            List<Permission> rolePermissions = permissionRepository.findByRoleId(role.getId());
            if (CollectionUtils.isNotEmpty(rolePermissions)) {
                permissions = rolePermissions.stream().map(Permission::getPermission).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(permissions)) {
            permissions.forEach(i -> {
                Optional.ofNullable(menuMap.get(i)).ifPresent(j -> j.setSelected(true));
            });
        }
        RoleDto roleDto = mapperService.map(role, RoleDto.class);
        afterMapToDto(List.of(role), List.of(roleDto));
        return new RoleResponseDto(roleDto, menuTree);
    }

    public boolean isSuperAdmin(Role role) {
        return role != null && role.getType() != null && RoleType.SUPER_ADMIN.getType() == role.getType();
    }

    private void checkRoleUniqueProperty(Long orgId, RoleSimpleDto role) {
        Role existRole = new Role();
        existRole.setId(role.getId());
        if (!isUniqueInOrg(orgId, "name", role.getName(), existRole)) {
            throw badRequestException(ROLE_NAME_EXISTS);
        }
        if (StringUtils.isNotEmpty(role.getCode())) {
            if (!isUniqueInOrg(orgId, "code", role.getCode(), existRole)) {
                throw badRequestException(ROLE_CODE_EXISTS);
            }
        }
    }

    /**
     * 新增或者更新角色，包括角色的权限
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(RoleRequestDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        RoleSimpleDto role = dto.getRole();
        if (role == null) {
            throw new BadRequestException("无效角色");
        }
        checkRoleUniqueProperty(orgId, role);
        Role save;
        boolean enable = false;
        if (role.getId() != null) {
            // 编辑，先通过id查出数据库中的数据，判断是否是当前企业，并且可编辑
            save = require(role.getId());
            checkIsCurrentOrg(save);
            if (!RoleType.canUpdate(save)) {
                throw new BadRequestException("此角色不能编辑");
            }

            if (CollectionUtils.isEmpty(dto.getMenus())) {
                enable = save.getEnable();
            }
            if (StringUtils.isNotEmpty(role.getCode()) && !Objects.equals(role.getCode(), save.getCode())) {
                enable = false;
            }
            // 旧数据可能这些有null,添加一些额外的处理
            if (save.getEditable() == null || save.getType() == null) {
                RoleType roleType = RoleType.parseRoleType(save);
                save.setEditable(roleType.getEditable());
                save.setType(roleType.getType());
                save.setPlatform(roleType.getPlatform());
            }
            save.setCode(role.getCode());
            save.setName(role.getName());
            save.setDescription(role.getDescription());
        } else {
            // 新增
            save = new Role();
            save.setCode(role.getCode());
            save.setName(role.getName());
            save.setDescription(role.getDescription());
            RoleType roleType = RoleType.OTHER;
            save.setEditable(roleType.getEditable());
            save.setType(roleType.getType());
            save.setPlatform(roleType.getPlatform());
        }

        save.setEnable(enable);
        repository.save(save);
        savePermissions(save.getId(), dto.getMenus());
        return true;
    }

    // 保存权限
    private void savePermissions(Long roleId, List<MenuTreeDto> menus) {
        Optional.ofNullable(menus).ifPresent(tree -> {
            List<MenuTreeDto> list = treeConvertService.treeToList(tree, Function.identity());
            List<String> newList = null;
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> paths = list.stream().filter(MenuTreeDto::isSelected).map(MenuTreeDto::getFullPath).collect(Collectors.toList());
                newList = menuService.getAllMenus()
                        .stream()
                        .filter(i -> paths.contains(i.getFullPath()) && i.getType() != null && i.getType() == 3)
                        .map(Menu::getFullPath)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            List<String> oldList = getPermissionsByRole(roleId);
            savePermissions(roleId, oldList, newList);
        });
    }

    private List<String> getSuperAdminPermissions(Long orgId) {
        AppVersion appVersion = organizationService.parseOrgVersion(orgId);
        return Permissions.permissions(appVersion).stream().map(Permissions::getPath).collect(Collectors.toList());
    }

    /**
     * 不能通过这个方法查询超管的权限，使用 {@link RoleService#getSuperAdminPermissions(Long)} 这个方法
     */
    private List<String> getPermissionsByRole(Long roleId) {
        return Optional.ofNullable(roleId)
                .map(i -> permissionRepository.findByRoleId(roleId))
                .map(l -> l.stream()
                        .map(Permission::getPermission)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    // 保存权限
    private void savePermissions(Long roleId, List<String> oldList, List<String> newList) {
        oldList = oldList == null ? List.of() : oldList;
        newList = newList == null ? List.of() : newList;
        List<String> add = ListHelper.minus(newList, oldList, List::contains);   // 待新增的权限
        List<String> del = ListHelper.minus(oldList, newList, List::contains);   // 待删除的权限

        // 保存新增
        if (CollectionUtils.isNotEmpty(add)) {
            List<Permission> l = add.stream().map(i -> new Permission(roleId, "Action", i)).collect(Collectors.toList());
            permissionRepository.saveAll(l);
        }
        // 删除
        if (CollectionUtils.isNotEmpty(del)) {
            Map<String, Menu> allMenuMap = menuService.getMenuMap();
            // 只能删除本地菜单列表中存在的
            del = del.stream().filter(allMenuMap::containsKey).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(del)) {
                List<Permission> l = permissionRepository.findByRoleIdAndPermissionIn(roleId, del);
                if (CollectionUtils.isNotEmpty(l)) {
                    permissionRepository.deleteAll(l);
                }
            }
        }
    }

    /**
     * 超管直接使用 版本的权限
     */
    @Deprecated(since = "1.10.6")
    public void updateSuperAdminPermissions(Long orgId, AppVersion appVersion) {
        Role role = getSuperAdminByOrg(orgId);
        if (role == null) {
            return;
        }
        Long roleId = role.getId();
        List<String> oldList = getPermissionsByRole(roleId);
        savePermissions(roleId, oldList, appVersion.getPermissions().mapToString());
    }

    /**
     * 保存数据看板权限(ctm-调用)
     */
    @Deprecated
    public void saveDashboardPermission(Long roleId, List<Long> dashboardIds) {
        if (CollectionUtils.isEmpty(dashboardIds)) {
            return;
        }
        List<Permission> permissions = dashboardIds.stream().filter(Objects::nonNull).map(id -> {
            Permission permission = new Permission();
            permission.setRoleId(roleId);
            permission.setModule("Dashboard");
            permission.setPermission(String.valueOf(id));
            return permission;
        }).collect(Collectors.toList());
        if (!permissions.isEmpty()) {
            this.permissionRepository.saveAll(permissions);
        }
    }

    /**
     * 角色中添加用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addUser(Long roleId, RoleUserRequestDto dto) {
        Role role = require(roleId);
        List<Long> userIds = StringHelper.toLongList(dto.getUserIds());
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIds.forEach(userId -> {
                if (!userService.exists(userId)) {
                    throw new BadRequestException("未知的用户id");
                }
            });
            userIds.forEach(userId -> userRoleService.addUserRole(userId, role.getId()));
        }
        return true;
    }


    /**
     * 角色中移除用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUser(Long roleId, RoleUserRequestDto dto) {
        Role role = require(roleId);
        List<Long> userIds = StringHelper.toLongList(dto.getUserIds());
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIds.forEach(userId -> userRoleService.deleteUserRole(userId, role.getId()));
        }
        return true;
    }

    /**
     * 获得用户的角色id列表
     */
    public String getRoleIdsByUserId(Long userId) {
        List<Role> roles = userRoleService.getByUser(userId);
        return getRoleIdsByRoles(roles);
    }

    public String getRoleIdsByRoles(List<Role> roles) {
        if (CollectionUtils.isNotEmpty(roles)) {
            return roles.stream().map(Role::getId).map(Objects::toString).collect(Collectors.joining(","));
        }
        return null;
    }

    /**
     * 获得用户的角色名称列表
     */
    public String getRoleNamesByUserId(Long userId) {
        List<Role> roles = userRoleService.getByUser(userId);
        if (CollectionUtils.isNotEmpty(roles)) {
            return roles.stream().map(Role::getName).collect(Collectors.joining(","));
        }
        return null;
    }

    /**
     * 获得用户的权限
     */
    public Map<String, Set<String>> getMenusByUserId(Long userId) {
        Map<String, Set<String>> map = new HashMap<>();
        final Set<String> action = new HashSet<>();
        final Set<String> dashboard = new HashSet<>();
        if (userId != null && userId > 0) {
            User user = userService.get(userId);
            if (user != null) {
                Role superAdmin = getSuperAdminByOrg(user.getOrgId());
                if (superAdmin != null) {
                    List<String> superAdminPermissions = getSuperAdminPermissions(superAdmin.getOrgId());
                    if (hasSuperAdminRole(userId)) {
                        action.addAll(superAdminPermissions);
                    } else {
                        List<Permission> permissions = permissionRepository.getByUserId(userId);
                        if (CollectionUtils.isNotEmpty(permissions)) {
                            permissions.forEach(i -> {
                                if (i != null && StringUtils.isNotEmpty(i.getModule())) {
                                    if (i.getModule().equals("Action")) {
                                        if (superAdminPermissions.contains(i.getPermission())) {
                                            action.add(i.getPermission());
                                        }
                                    } else if (i.getModule().equals("Dashboard")) {
                                        dashboard.add(i.getPermission());
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }
        map.put("Action", action);
        map.put("Dashboard", dashboard);
        return map;
    }

    /**
     * 获得角色的权限
     */
    public Map<String, Set<String>> getMenusByRoles(List<Role> roles) {
        Map<String, Set<String>> map = new HashMap<>();
        final Set<String> action = new HashSet<>();
        final Set<String> dashboard = new HashSet<>();
        if (CollectionUtils.isEmpty(roles)) {
            return map;
        }
        boolean superAdmin = false;
        List<Long> roleIds = new ArrayList<>();
        for (Role role : roles) {
            if (role.getType() != null && role.getType() == RoleType.SUPER_ADMIN.getType()) {
                superAdmin = true;
                break;
            } else {
                roleIds.add(role.getId());
            }
        }
        List<String> superAdminPermissions = getSuperAdminPermissions(roles.get(0).getOrgId());
        if (superAdmin) {
            action.addAll(superAdminPermissions);
        } else {
            List<Permission> permissions = permissionRepository.findByRoleIdIn(roleIds);
            if (CollectionUtils.isNotEmpty(permissions)) {
                permissions.forEach(i -> {
                    if (i != null && StringUtils.isNotEmpty(i.getModule())) {
                        if (i.getModule().equals("Action")) {
                            if (superAdminPermissions.contains(i.getPermission())) {
                                action.add(i.getPermission());
                            }
                        } else if (i.getModule().equals("Dashboard")) {
                            dashboard.add(i.getPermission());
                        }
                    }
                });
            }
        }
        map.put("Action", action);
        map.put("Dashboard", dashboard);
        return map;
    }

    /**
     * 判断是否有超级管理员的角色
     */
    public boolean hasSuperAdminRole(Long userId) {
        if (userId == null || userId <= 0) {
            return false;
        }
        return repository.countByUserIdAndRoleType(userId, RoleType.SUPER_ADMIN.getType()) > 0;
    }

    /**
     * 获得企业内的 {@link RoleType#SUPER_ADMIN} 的角色
     */
    public Role getSuperAdminByOrg(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return null;
        }
        return repository.findFirstByOrgIdAndType(orgId, RoleType.SUPER_ADMIN.getType());
    }

    /**
     * 添加企业的 {@link RoleType#SUPER_ADMIN} 角色，
     * 新增之前会校验是否已存在
     */
    public Role addSuperAdmin(Long orgId, AppVersion orgVersion) {
        Role role = getSuperAdminByOrg(orgId);
        if (role != null) {
            return role;
        }
        role = RoleType.SUPER_ADMIN.createRole(orgId);
        repository.save(role);
//        savePermissions(role.getId(), null, orgVersion.getPermissions().mapToString());
        return role;
    }

    /**
     * 获得企业内的 {@link RoleType#MEMBER} 的角色
     */
    public Role getMemberRole(Long orgId) {
        return repository.findFirstByOrgIdAndType(orgId, RoleType.MEMBER.getType());
    }

    /**
     * 获得企业内的 {@link RoleType#MEMBER} 的角色
     * 如果不存在则创建一个
     */
    public Role getOrCreateMemberRole(Long orgId) {
        Role role = getMemberRole(orgId);
        if (role == null) {
            return addMemberRole(orgId);
        }
        return role;
    }

    /**
     * 添加企业的 {@link RoleType#MEMBER} 角色，
     */
    public Role addMemberRole(Long orgId) {
        Role role = RoleType.MEMBER.createRole(orgId);
        repository.save(role);
        savePermissions(role.getId(), null, AppVersionPermissions.MEMBER.mapToString());
        return role;
    }

    /**
     * 添加企业的 {@link RoleType#OTHER} IT对接 角色，
     */
    public Role addItRole(Long orgId) {
        Role role = RoleType.OTHER.createRole(orgId);
        role.setName("IT对接");
        repository.save(role);
        savePermissions(role.getId(), null, AppVersionPermissions.IT.mapToString());
        return role;
    }

    /**
     * 添加企业的 {@link RoleType#OTHER} 体验研究员 角色，
     */
    public Role addResearcherRole(Long orgId) {
        Role role = RoleType.OTHER.createRole(orgId);
        role.setName("体验研究员");
        repository.save(role);
        savePermissions(role.getId(), null, AppVersionPermissions.RESEARCHER.mapToString());
        return role;
    }

    public List<Role> getByOrgAndPermission(Long orgId, List<String> permission) {
        if (orgId == null || orgId <= 0 || CollectionUtils.isEmpty(permission)) {
            return null;
        }
        List<Role> roles = new ArrayList<>();
        List<String> permissions = getSuperAdminPermissions(orgId);
        if (permission.stream().anyMatch(permissions::contains)) {
            // add super admin
            Role superAdmin = getSuperAdminByOrg(orgId);
            if (superAdmin != null) {
                roles.add(superAdmin);
            }
        }
        List<Role> otherRoles = repository.getByOrgIdAndPermissionIn(orgId, permission);
        if (CollectionUtils.isNotEmpty(otherRoles)) {
            roles.addAll(otherRoles);
        }
        return roles;
    }

    public List<Long> toIds(List<Role> roles) {
        return Optional.ofNullable(roles)
                .map(l -> l.stream()
                        .map(Role::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    @Override
    public Boolean deleteOne(long id) {
        Role role = get(id);
        if (role == null) {
            throw new EntityNotFoundException(Role.class);
        }
        if (RoleType.canDelete(role)) {
            repository.delete(role);
        } else {
            throw new BadRequestException("默认角色不能删除");
        }
        return true;
    }

    public List<RoleDto> getByUserId(Long userId) {
        return Optional.ofNullable(getByUserIds(List.of(userId))).map(i -> i.get(userId)).orElse(new ArrayList<>());
    }

    public Map<Long, List<RoleDto>> getByUserIds(List<Long> userIds) {
        Set<Long> ids = Optional.ofNullable(userIds).map(i -> i.stream().filter(Objects::nonNull).collect(Collectors.toSet())).orElse(null);
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        String sql = "SELECT" +
                " r.*," +
                " ur.user_id" +
                " FROM user_role ur" +
                " inner join role r on ur.role_id=r.id" +
                " where ur.user_id in " + ids.stream().map(Object::toString).collect(Collectors.joining(",", "(", ")"));
        return nativeSqlHelper.queryListObject(sql, RoleDto.class).stream().collect(Collectors.groupingBy(RoleDto::getUserId, Collectors.toList()));
    }

    public List<Role> getByIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        return repository.findAllById(roleIds);
    }

    public List<Role> getByOrgId(Long orgId) {
        if (orgId == null || orgId == 0) {
            return null;
        }
        return repository.findByOrgId(orgId);
    }

    public List<Role> getByOrgIdAndCodes(Long orgId, List<String> codes) {
        if (orgId == null || orgId == 0 || CollectionUtils.isEmpty(codes)) {
            return null;
        }
        return repository.findByOrgIdAndCodeInAndEnable(orgId, codes, true);
    }

    public Map<Long/*userId*/, Set<Long/*roleId*/>> getUserRolesByRoleIds(Long orgId, Collection<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new HashMap<>();
        }
        String sql = "select u.id userId, r.id roleId from user_role ur " +
                " inner join `user` u on ur.user_id=u.id and u.org_id=:orgId" +
                " inner join `role` r on ur.role_id=r.id and r.org_id=:orgId and r.id in (:roleIds) ";
        Map<String, Object> params = new HashMap<>();
        params.put("orgId", orgId);
        params.put("roleIds", roleIds);
        List<Pair<Long, Long>> list = jdbcTemplate.query(sql, params,
                (rs, rowNum) -> Pair.create(rs.getLong("userId"), rs.getLong("roleId")));
        Map<Long/*userId*/, Set<Long/*roleId*/>> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> {
                if (i.getFirst() != null && i.getSecond() != null) {
                    result.computeIfAbsent(i.getFirst(), k -> new HashSet<>()).add(i.getSecond());
                }
            });
        }
        return result;
    }
}