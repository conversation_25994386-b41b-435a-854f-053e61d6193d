package org.befun.auth.service.orgconfig;

import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.*;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrgConfigCustomerVisibleService implements BaseOrgConfigService {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private OrgConfigExtendCustomerFieldService orgConfigExtendCustomerFieldService;

    public Set<String> itemNames() {
        List<OrgConfigCustomerVisibleItemDto> items = getAllItems();
        if (items == null) {
            return new HashSet<>();
        }
        return items.stream().map(OrgConfigCustomerVisibleItemDto::getProp).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    private List<OrgConfigCustomerVisibleItemDto> getAllItems() {
        String defaultConfig = authProperties.getOrgDefaultConfig().getOrDefault(type().name(), "[]");
        return JsonHelper.toList(defaultConfig, OrgConfigCustomerVisibleItemDto.class);
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setCustomerVisible(source.getCustomerVisible());
    }

    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.customerVisible;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        List<OrgConfigCustomerVisibleItemDto> selected = getAllItems().stream().filter(OrgConfigCustomerVisibleItemDto::getVisible).collect(Collectors.toList());
        OrgConfigCustomerVisibleDto customerVisible = new OrgConfigCustomerVisibleDto(selected);
        config.setCustomerVisible(customerVisible);
        return config;
    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        OrgConfigBuilderDto builder = new OrgConfigBuilderDto();
        List<OrgConfigCustomerVisibleItemDto> selectedColumns = Optional.ofNullable(config)
                .map(OrgConfigDto::getCustomerVisible)
                .map(OrgConfigCustomerVisibleDto::getSelectedColumns)
                .orElse(new ArrayList<>());
        Map<String, OrgConfigCustomerVisibleItemDto> selectedColumnMap = selectedColumns.stream().collect(Collectors.toMap(OrgConfigCustomerVisibleItemDto::getProp, Function.identity()));

        List<OrgConfigCustomerVisibleItemDto> fixedColumns = new ArrayList<>();
        List<OrgConfigCustomerVisibleItemDto> editableColumns = new ArrayList<>();
        List<OrgConfigCustomerVisibleItemDto> extendColumns = new ArrayList<>();
        getAllItems().forEach(i -> {
            if (i.getEditable()) {
                i.setVisible(selectedColumnMap.containsKey(i.getProp()));
                editableColumns.add(i);
            } else {
                fixedColumns.add(i);
            }
        });
        orgConfigExtendCustomerFieldService.extendFields().forEach(f -> {
            OrgConfigCustomerVisibleItemDto i = new OrgConfigCustomerVisibleItemDto();
            i.setProp(f.getProp());
            i.setLabel(f.getLabel());
            i.setEditable(true);
            i.setVisible(selectedColumnMap.containsKey(i.getProp()));
            extendColumns.add(i);
        });
        OrgConfigCustomerVisibleBuilderDto customerVisible = new OrgConfigCustomerVisibleBuilderDto(selectedColumns, fixedColumns, editableColumns, extendColumns);
        builder.setCustomerVisible(customerVisible);
        return builder;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {
        List<OrgConfigCustomerVisibleItemDto> selectedColumns;
        if (data == null || data.getCustomerVisible() == null || CollectionUtils.isEmpty(selectedColumns = data.getCustomerVisible().getSelectedColumns())) {
            throw new BadRequestException();
        }
        Map<String, OrgConfigCustomerVisibleItemDto> fixedColumnMap = new HashMap<>();
        Map<String, OrgConfigCustomerVisibleItemDto> allColumnMap = new HashMap<>();
        Map<String, OrgConfigExtendCustomerFieldDto.ExtendField> extendColumnMap = new HashMap<>();
        getAllItems().forEach(i -> {
            if (!i.getEditable()) {
                fixedColumnMap.put(i.getProp(), i);
            }
            allColumnMap.put(i.getProp(), i);
        });
        orgConfigExtendCustomerFieldService.extendFields().forEach(f -> {
            extendColumnMap.put(f.getProp(), f);
        });
        List<String> selectedFixed = new ArrayList<>();    // 已选择的固定列
        List<String> notExists = new ArrayList<>();        // 不存在的列

        selectedColumns.forEach(i -> {
            i.setVisible(true);
            OrgConfigCustomerVisibleItemDto item = fixedColumnMap.get(i.getProp());
            if (item != null) {
                // 固定列
                i.setEditable(false);
                i.setLabel(item.getLabel());
                selectedFixed.add(i.getProp());
            } else {
                item = allColumnMap.get(i.getProp());
                if (item != null) {
                    i.setEditable(true);
                    i.setLabel(item.getLabel());
                } else {
                    // 自定义字段
                    OrgConfigExtendCustomerFieldDto.ExtendField extendField = extendColumnMap.get(i.getProp());
                    if (extendField != null) {
                        i.setEditable(true);
                        i.setLabel(extendField.getLabel());
                    } else {
                        notExists.add(i.getLabel());       // 不存在的列
                    }
                }
            }
        });
        List<String> lossFixed = fixedColumnMap.values().stream().filter(i -> !selectedFixed.contains(i.getProp())).map(OrgConfigCustomerVisibleItemDto::getLabel).collect(Collectors.toList());

        List<String> s = new ArrayList<>();
        if (!lossFixed.isEmpty()) {
            s.add("缺少固定列：" + String.join(",", lossFixed));
        }
        if (!notExists.isEmpty()) {
            s.add("不存在的列：" + String.join(",", notExists));
        }
        if (!s.isEmpty()) {
            throw new BadRequestException(String.join("；", s));
        }
    }

    @Override
    public void correction(OrgConfigDto data) {
        List<OrgConfigCustomerVisibleItemDto> selectedColumns;
        if (data == null || data.getCustomerVisible() == null || CollectionUtils.isEmpty(selectedColumns = data.getCustomerVisible().getSelectedColumns())) {
            return;
        }
        Set<String> itemNames = itemNames();
        Set<String> extendFieldNames = orgConfigExtendCustomerFieldService.extendFieldNames();
        selectedColumns.removeIf(next -> next != null && !itemNames.contains(next.getProp()) && !extendFieldNames.contains(next.getProp()));
    }
}
