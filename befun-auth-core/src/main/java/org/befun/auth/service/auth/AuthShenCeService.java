package org.befun.auth.service.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.auth.constant.AuthToastMessage;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.ShenCeAuthDto;
import org.befun.auth.dto.linker.LinkerResponseDataDto;
import org.befun.auth.dto.linker.LinkerShenCeParamDto;
import org.befun.auth.service.auth.config.ShenCeConfig;
import org.befun.auth.service.linker.ILinkerData;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.toast.ToastMessageHelper;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.befun.auth.constant.ThirdPartyAuthType.SHEN_CE;

@Slf4j
@Service
public class AuthShenCeService extends BaseAuthService<ShenCeConfig, ShenCeAuthDto> implements ILinkerData<LinkerShenCeParamDto> {

    @Override
    public ThirdPartyAuthType getAuthType() {
        return SHEN_CE;
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, ShenCeAuthDto dto) {
        allAuthDto.setShenCe(dto);
    }

    @Override
    public boolean supportLinker(String linkerType) {
        return getAuthType().getCamelName().equals(linkerType);
    }

    @Override
    public void checkConfig(ShenCeConfig config) {
        if (!config.checkConfig()) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.SHEN_CE_BIND_ERROR);
        }
    }

    @Override
    public LinkerResponseDataDto getData(String app, LinkerShenCeParamDto param) {
        ShenCeData data = null;
        if (StringUtils.isNotEmpty(app) && param != null) {
            param.buildParams();
            data = getData(app, param.getReportPath(), param.getRequestBody());
        }
        return transformData(param, data);
    }

    private ShenCeData getData(String app, String reportPath, String requestBody) {
        ShenCeConfig config = getConfig(app);
        if (config == null || config.getStatus() == null || config.getStatus() == 0) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.SHEN_CE_BIND_NOT_FOUND);
        } else if (config.getStatus() != 1 || !config.checkConfig()) {
            throw ToastMessageHelper.badRequestException(AuthToastMessage.SHEN_CE_BIND_ERROR);
        }
        Map<String, String> headers = new HashMap<>();
        String url = buildUrl(config, reportPath, headers);
        ShenCeData response = httpPost(url, headers, requestBody, ShenCeData.class);
        if (response != null) {
            if (response.success()) {
                return response;
            } else {
                // todo 如果是配置信息错误，修改状态为异常
            }
        }
        return null;
    }

    private String buildUrl(ShenCeConfig config, String reportPath, Map<String, String> headers) {
        StringBuilder url = new StringBuilder();
        url.append(config.getUrl());
        if (!config.getUrl().endsWith("/")) {
            url.append("/");
        }
        url.append("api");
        if (!reportPath.startsWith("/")) {
            url.append("/");
        }
        url.append(reportPath);
        if ("apiSecret".equals(config.getAuthMode())) {
            url.append("?token=").append(config.getApiSecret()).append("&project=").append(config.getProjectName());
        } else if ("accessToken".equals(config.getAuthMode())) {
            headers.put("sensorsdata-token", config.getAccessToken());
        }
        return url.toString();
    }

    private LinkerResponseDataDto transformData(LinkerShenCeParamDto param, ShenCeData data) {
        LinkerResponseDataDto dto = new LinkerResponseDataDto(getAuthType().getCamelName());
        dto.setLinkerType(getAuthType().getCamelName());
        if (param == null || data == null
                || data.series == null || data.series.isEmpty()
                || data.rows == null || data.rows.isEmpty()) {
            return dto;
        }
        try {
            AtomicInteger currentValue = new AtomicInteger();

            List<String> measures = param.getEventsReportMeasures();
            List<String> groups = Optional.ofNullable(data.byFields).orElse(List.of());
            List<String> dates = data.series;
            List<String> recentLabel = new ArrayList<>();
            recentLabel.add(Stream.concat(measures.stream(), groups.stream()).collect(Collectors.joining("@")));
            recentLabel.addAll(dates);

            List<List<Object>> recentValue = new ArrayList<>();

            data.rows.forEach(row -> {
                if (row.values.size() != dates.size() || groups.size() != row.byValues.size()) {
                    return;
                }
                List<String> byValues = row.byValues;
                for (int i = 0; i < measures.size(); i++) {
                    int index = i;
                    String measure = measures.get(i);
                    List<Object> rowValue = new ArrayList<>();
                    rowValue.add(Stream.concat(Stream.of(measure), byValues.stream()).collect(Collectors.joining("@")));
                    row.values.forEach(values -> {
                        Integer value = values.get(index);
                        if (value != null && value != 0) {
                            currentValue.addAndGet(value);
                        }
                        rowValue.add(value);
                    });
                    recentValue.add(rowValue);
                }
            });
            dto.setCurrentValue(currentValue.doubleValue());
            dto.setRecentLabel(recentLabel);
            dto.setRecentValue(recentValue);
        } catch (Throwable e) {
            log.error("解析神策数据失败，params={}", param.buildParams(), e);
        }
        return dto;
    }

    private List<Map<String, String>> buildColumns(List<String> groups, List<String> series,
                                                   List<String> dates) {
        List<Map<String, String>> columns = new ArrayList<>();
        columns.add(columnMap("measure", "指标", "measure"));
        if (CollectionUtils.isNotEmpty(groups)) {
            groups.forEach(i -> {
                columns.add(columnMap(i, i, "group"));
            });
        }
        series.forEach(i -> {
            columns.add(columnMap(i, i, "data"));
            dates.add(i);
        });
        return columns;
    }

    private Map<String, String> columnMap(String id, String name, String type) {
        Map<String, String> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        map.put("type", type);
        return map;
    }


    @Getter
    @Setter
    public static class ShenCeData {

        // 查询所用到的分组属性
        @JsonProperty("by_fields")
        private List<String> byFields = new ArrayList<>();

        // 查询的时间范围，因为是按天查看，所以会展示每天，如果按周聚合的话，展示的是每周的周一的时间。
        @JsonProperty("series")
        private List<String> series = new ArrayList<>();

        // 查询结果的行数，对应的每行的值，按日期倒叙展示每天的值。
        @JsonProperty("rows")
        private List<ShenCeDataRow> rows = new ArrayList<>();

        @JsonProperty("num_rows")
        private Integer numRows;
        @JsonProperty("total_rows")
        private Integer totalRows;
        @JsonProperty("report_update_time")
        private String reportUpdateTime;
        @JsonProperty("data_update_time")
        private String dataUpdateTime;
        @JsonProperty("data_sufficient_update_time")
        private String dataSufficientUpdateTime;
        @JsonProperty("truncated")
        private Boolean truncated;

        private boolean success() {
            return true;
        }
    }

    @Getter
    @Setter
    public static class ShenCeDataRow {
        // 查询结果，第一行的值[0],[0][1][0],对应[2021-07-15 00:00:00][2021-07-14 00:00:00][2021-07-13 00:00:00][2021-07-12 00:00:00]每天的数据
        private List<List<Integer>> values = new ArrayList<>();
        // 查询结果，第一行的属性值"-INF~100","-INF~1000" 表示第一个分组属性的值、第二个分组属性的值。
        @JsonProperty("by_values")
        private List<String> byValues = new ArrayList<>();
    }

    private <T> T httpPost(String url, Map<String, String> headers, String body, Class<T> tClass) {
        try {
            Request request = Request.Post(url)
                    .socketTimeout(30000)
                    .connectTimeout(30000)
                    .bodyString(body, ContentType.APPLICATION_JSON);
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(request::addHeader);
            }
            String response = request.execute().returnContent().toString();

            log.info("shence http post:\n" +
                    "url={}\n" +
                    "body={}\n" +
                    "response={}", url, body, response);
            return JsonHelper.toObject(response, tClass);
        } catch (IOException e) {
            log.error("shence post({}) http error ", url, e);
        }
        return null;
    }

//    private <T> T httpPost(String url, String body, Class<T> tClass) {
//        String response = "{\"by_fields\":[\"event.$Anything.$screen_width\",\"event.$Anything.$screen_height\"],\"series\":[\"2021-07-12 00:00:00\",\"2021-07-13 00:00:00\",\"2021-07-14 00:00:00\",\"2021-07-15 00:00:00\"],\"rows\":[{\"values\":[[0],[0],[1],[0]],\"by_values\":[\"-INF~100\",\"-INF~1000\"]},{\"values\":[[1],[0],[0],[0]],\"by_values\":[\"-INF~100\",\"1000~INF\"]}],\"num_rows\":2,\"total_rows\":2,\"report_update_time\":\"2021-07-15 16:51:08.356\",\"data_update_time\":\"2021-07-15 16:03:32.000\",\"truncated\":false}";
//        log.info("shence http post:\n" +
//                "url={}\n" +
//                "body={}\n" +
//                "response={}", url, body, response);
//        return JsonHelper.toObject(response, tClass);
//
//    }
}
