package org.befun.auth.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.*;
import org.befun.auth.dto.RegisterInfoDto;
import org.befun.auth.dto.auth.authcode.AuthCodeCache;
import org.befun.auth.entity.*;
import org.befun.auth.provider.local.MobileVerifyCodeProvider;
import org.befun.auth.repository.*;
import org.befun.auth.utils.StringHelper;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;
import java.util.Optional;

import static org.befun.auth.constant.AuthToastMessage.*;
import static org.befun.auth.constant.Permissions.*;
import static org.befun.extension.toast.ToastMessageHelper.badRequestException;


/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class RegisterService {

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    @Autowired
    private IndustryRepository industryRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;

    @Autowired
    private SmsService smsService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MobileVerifyCodeProvider mobileVerifyCodeProvider;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private OrganizationWalletService organizationWalletService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserService userService;

    @Autowired
    private IAuthEventTrigger authEventTrigger;

    @Autowired
    private AuthService authService;


    /**
     * 校验验证码
     */
    public String checkVerifyCode(String mobile, String code) {
        String key = mobileVerifyCodeProvider.redisKey(VerifyCodeUseFor.REGISTER, mobileVerifyCodeProvider.getVerifyCodeType(), mobile);
        if (!mobileVerifyCodeProvider.verifyCodeFromRedis(key, code)) {
            throw badRequestException(VERIFY_CODE_CHECK_ERROR);
        }
        return key;
    }

    @Transactional(rollbackFor = Exception.class)
    public User registerByMobile(RegisterInfoDto registerInfoDto) {
        AuthCodeCache authCodeCache = authService.checkAuthCodeByBind(registerInfoDto.getAuthCode());
        String key = checkVerifyCode(registerInfoDto.getMobile(), registerInfoDto.getVerifyCode());
        if (userRepository.existsByMobile(registerInfoDto.getMobile())) {
            throw badRequestException(MOBILE_EXISTS_USE_REGISTER);
        }
        if (StringUtils.isNotEmpty(registerInfoDto.getEmail()) && userRepository.existsByEmail(registerInfoDto.getEmail())) {
            throw badRequestException(EMAIL_EXISTS_USE_REGISTER);
        }

        try {
//            registerInfoDto.setAppVersion(AppVersion.UPDATE);
//            registerInfoDto.setAvailableDateEnd(DateHelper.toDate(LocalDate.now().plusDays(13)));
            User user = initRegisterInfo(registerInfoDto);
            if (authCodeCache != null) {
                authService.authCodeBindUser(user, authCodeCache);
            }
            mobileVerifyCodeProvider.clearVerifyCode(key, registerInfoDto.getVerifyCode());
            return user;
        } catch (Exception ex) {
            log.error("注册失败", ex);
            throw new BadRequestException("注册失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public User registerByApi(RegisterInfoDto registerInfoDto) {
        if (userRepository.existsByMobile(registerInfoDto.getMobile())) {
            throw badRequestException(MOBILE_EXISTS_USE_REGISTER);
        }
        if (userRepository.existsByEmail(registerInfoDto.getEmail())) {
            throw badRequestException(EMAIL_EXISTS_USE_REGISTER);
        }

        try {
            User user = initRegisterInfo(registerInfoDto);
            return user;
        } catch (Exception ex) {
            ex.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new BadRequestException("注册失败");
        }
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public User initRegisterInfo(RegisterInfoDto registerInfoDto) throws BadRequestException {
        AppVersion appVersion = Optional.ofNullable(registerInfoDto.getAppVersion()).orElse(AppVersion.FREE);
        AppVersion cemVersion = Optional.ofNullable(registerInfoDto.getAppVersion()).orElse(AppVersion.FREE);
        AppVersion surveyplusVersion = AppVersion.EMPTY;
        List<AppType> appTypes = List.of(registerInfoDto.getApp());
        int maxUser = 1;
        IndustryType industryType = IndustryType.OTHERS;

        Optional<Industry> industry = industryRepository.findOneByCode(industryType.getCode());

        if (industry.isEmpty()) {
            throw new BadRequestException(String.format("行业code: %s 不存在", industryType.getCode()));
        }


        Organization organization = organizationService.initOrganization(
                industry.get(),
                registerInfoDto.getCompanyName(),
                appTypes,
                cemVersion,
                surveyplusVersion,
                appVersion.getOptionalLimit(),
                maxUser,
                registerInfoDto.getAvailableDateEnd()
        );

        organizationWalletService.initOrganizationWallet(organization);

        Role adminRole = initRolePermissions(organization, appVersion);

        Department department = departmentService.initDepartment(organization);

        User user = userService.initUserWithIp(registerInfoDto, organization.getId(), adminRole.getId(), department, appVersion, true);

        userRoleService.saveUserRole(user.getId(), List.of(adminRole.getId()), true, true);
        roleService.getOrCreateMemberRole(organization.getId());
//        roleService.addItRole(organization.getId());
//        roleService.addResearcherRole(organization.getId());

        organization.setOwnerId(user.getId());
        organizationService.save(organization);

        return user;
    }

    /**
     * 初始化角色的权限
     *
     * @param organization
     * @param cemVersion
     * @return
     */
    private Role initRolePermissions(Organization organization, AppVersion cemVersion) {
        log.info("init role permissions with orgId: {} and appVersion: {}", organization.getId(), cemVersion);
        return roleService.addSuperAdmin(organization.getId(), cemVersion);
    }

    public void userRegisterEvent(User user, RegisterInfoDto dto) {
        var customer = dto.getCustomer();
        customer.setField_ip__c(user.getIp());
        customer.setField_country__c(user.getCountry());
        customer.setField_province__c(user.getProvince());
        customer.setField_city__c(user.getCity());
        authEventTrigger.userRegister(user.getOrgId(), user.getId(), dto.getPassword(), dto.getCompanyName(), JsonHelper.toJson(customer));
    }


    /**
     * 添加角色权限
     *
     * @param roleId
     * @param actionPermissions
     * @param dashBordPermissions
     */
    @Transactional(rollbackFor = Exception.class)
    private void grantPermissions(Long roleId, List<String> actionPermissions, List<String> dashBordPermissions) {
        log.info("grant roleId: {} permissions with actionPermissions size: {} and dashBordPermissions size: {}",
                roleId, actionPermissions.size(), dashBordPermissions.size());

        if (!actionPermissions.isEmpty()) {

            if (actionPermissions.contains(TOUCH_MANAGE_SURVEY_VIEW.getPath()) && !actionPermissions.contains(TOUCH_MANAGE_SURVEY_EDIT.getPath())) {
                actionPermissions.add(SURVEY_MANAGE_ANALYZE_VIEW.getPath());
            }

            actionPermissions.forEach(permissionString -> {
                Permission permission = Permission.builder().roleId(roleId).permission(permissionString).module(PermissionType.ACTION.getName()).build();
                permissionRepository.save(permission);
            });
        }

        if (!dashBordPermissions.isEmpty()) {
            dashBordPermissions.forEach(permissionString -> {
                Permission permission = Permission.builder().roleId(roleId).permission(permissionString).module(PermissionType.DASHBORD.getName()).build();
                permissionRepository.save(permission);
            });
        }
    }

    /**
     * 检测未被删除用户的手机号是否注册
     *
     * @param mobile
     * @return
     */
    private boolean checkMobileRegistered(String mobile) {
        List<User> users = userRepository.findByMobile(mobile);
        return users.isEmpty();
    }


    /**
     * 检测验证码是否有效
     *
     * @param mobile
     * @return
     */
    @SneakyThrows
    public Object verifyCodeEfficient(String mobile, boolean checkExpirationTime, Long expirationTime) {

        String key = StringHelper.toMD5(mobile, 32);
        Long expiration = redisTemplate.getExpire(key);

        if (checkExpirationTime) {
            // 判断验证码是否过期
            return expiration >= expirationTime;
        } else {
            // 获取验证码
            return redisTemplate.opsForValue().get(key);
        }

    }

}