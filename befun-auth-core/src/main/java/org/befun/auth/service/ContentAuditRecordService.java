package org.befun.auth.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.fluent.Request;
import org.apache.http.message.BasicNameValuePair;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.configuration.ContentAuditProperty;
import org.befun.auth.constant.ContentAuditStatus;
import org.befun.auth.constant.ContentAuditType;
import org.befun.auth.entity.ContentAuditRecord;
import org.befun.auth.entity.ContentAuditRecordDto;
import org.befun.auth.projection.SimpleContentAuditRecord;
import org.befun.auth.repository.ContentAuditRecordRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.token.Sha512DigestUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ContentAuditStatus.*;

@Slf4j
@Service
public class ContentAuditRecordService extends BaseService<ContentAuditRecord, ContentAuditRecordDto, ContentAuditRecordRepository> {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    private ContentAuditProperty getContendAuditProperty() {
        return authProperties.getContentAudit();
    }

    public boolean isEnableContendAudit() {
        return getContendAuditProperty().isEnabled();
    }

    public boolean isEnableContendAuditText() {
        return getContendAuditProperty().isEnableAuditText();
    }

    public boolean isEnableContendAuditImage() {
        return getContendAuditProperty().isEnableAuditImage();
    }

    public SimpleContentAuditRecord auditSurvey(long surveyId, List<String> texts) {
        return audit(ContentAuditType.survey, surveyId, texts, true);
    }

    /**
     * @param texts            会把文本内容重构，所有内容拼接在一起，然后分片（每一片长度不超过6000）去审核
     * @param parseImageInText 是否需要解析出 text 中的图片
     */
    public SimpleContentAuditRecord audit(ContentAuditType type, long sourceId, List<String> texts, boolean parseImageInText) {
        if (!isEnableContendAudit() || CollectionUtils.isEmpty(texts)) {
            return passRecord();
        }
        Map<String/*sign*/, String> allTextMap = new HashMap<>();
        // 拼接文本，并分片，调用api查询文本时，有长度限制，
        String allTextSign = splitText(texts, allTextMap);
        if (StringUtils.isEmpty(allTextSign)) {
            return passRecord();
        }
        SimpleContentAuditRecord record = getByCache(type, allTextSign);
        if (record != null) {
            return record;
        }
        // 最终需要审核的内容，text image
        Map<String, SignContent> signMap = new HashMap<>();
        if (MapUtils.isNotEmpty(allTextMap)) {
            Set<String> images = new HashSet<>();
            allTextMap.forEach((sign, text) -> {
                // 文本审核内容
                signMap.put(sign, new SignContent(ContentAuditType.text, sign, text));
                if (parseImageInText) {
                    // 文本中的图片
                    parseImageInText(text).forEach(image -> {
                        if (StringUtils.isNotEmpty(image) && !images.contains(image)) {
                            images.add(image);
                            String imageSign = Sha512DigestUtils.shaHex(image);
                            signMap.put(imageSign, new SignContent(ContentAuditType.image, imageSign, image));
                        }
                    });
                }
            });
        }
        if (signMap.isEmpty()) {
            return passRecord();
        }
        Map<String, SimpleContentAuditRecord> subRecords = new ConcurrentHashMap<>();
        // 开始解析，并行
        String accessToken = getAccessToken();
        parallelQuery(accessToken, signMap, subRecords);
        return buildAllTextResult(type, sourceId, allTextSign, signMap, subRecords);
    }

    private boolean parallelQuery(String accessToken, Map<String, SignContent> signMap, Map<String, SimpleContentAuditRecord> subRecords) {
        try {
            int parallelism = getContendAuditProperty().getParallelism();
            Semaphore semaphore = new Semaphore(parallelism);
            signMap.forEach((sign, content) -> {
                try {
                    semaphore.acquire();
                    executorService.execute(() -> {
                        Optional.ofNullable(auditContent(accessToken, content)).ifPresent(r -> subRecords.put(content.sign, r));
                        semaphore.release();
                    });
                } catch (InterruptedException e) {
                    log.error("parallelQuery foreach error", e);
                }
            });
            return semaphore.tryAcquire(parallelism, getContendAuditProperty().getTimeoutMs(), TimeUnit.MILLISECONDS);
        } catch (Throwable e) {
            log.error("parallelQuery error", e);
        }
        return false;
    }

    private SimpleContentAuditRecord passRecord() {
        return new SimpleContentAuditRecord(null, null, null, null, pass);
    }

    /**
     * 综合结果
     * 如果有 noPass 的，结果为 noPass
     * 如果有 suspected 的，结果为 suspected
     * 如果有 unknown 的，结果为 unknown
     * 否则为 pass
     */
    private SimpleContentAuditRecord buildAllTextResult(ContentAuditType type, long sourceId, String allTextSign, Map<String, SignContent> signMap, Map<String, SimpleContentAuditRecord> subRecords) {
        if (MapUtils.isEmpty(subRecords)) {
            return passRecord();
        }
        AtomicLong countFailure = new AtomicLong();
        Map<Long, String> allContent = new HashMap<>();
        Map<Long, ContentAuditStatus> allResponse = new HashMap<>();
        List<String> allWords = new ArrayList<>();
        AtomicInteger passCount = new AtomicInteger();
        AtomicInteger noPassCount = new AtomicInteger();
        AtomicInteger suspectedCount = new AtomicInteger();
        AtomicInteger unknownCount = new AtomicInteger();
        signMap.forEach((sign, content) -> {
            SimpleContentAuditRecord record = subRecords.get(sign);
            ContentAuditStatus status = unknown;
            if (record != null) {
                status = record.getStatus();
                if (record.getId() != null) {
                    allContent.put(record.getId(), record.getSign());
                    allResponse.put(record.getId(), record.getStatus());
                    List<String> words = JsonHelper.toList(record.getWords(), String.class);
                    if (CollectionUtils.isNotEmpty(words)) {
                        allWords.addAll(words);
                    }
                }
            } else {
                // 这个签名没有返回结果
                allContent.put(countFailure.getAndDecrement(), sign);
            }
            switch (status) {
                case pass:
                    passCount.incrementAndGet();
                    break;
                case noPass:
                    noPassCount.incrementAndGet();
                    break;
                case unknown:
                    unknownCount.incrementAndGet();
                    break;
                case suspected:
                    suspectedCount.incrementAndGet();
                    break;
            }
        });
        ContentAuditStatus status;
        if (noPassCount.get() > 0) {
            status = noPass;
        } else if (suspectedCount.get() > 0) {
            status = suspected;
        } else if (unknownCount.get() > 0) {
            status = unknown;
        } else {
            status = ContentAuditStatus.pass;
        }
        ContentAuditRecord entity = new ContentAuditRecord();
        entity.setSign(allTextSign);
        entity.setType(type);
        entity.setSourceId(sourceId);
        entity.setContent(JsonHelper.toJson(allContent));
        entity.setResponse(JsonHelper.toJson(allResponse));
        entity.setWords(JsonHelper.toJson(allWords));
        entity.setStatus(status);
        repository.save(entity);
        return SimpleContentAuditRecord.fromEntity(entity);
    }

    @Getter
    @Setter
    public static class SignContent {
        private ContentAuditType type;
        private String sign;
        private String content;

        public SignContent(ContentAuditType type, String sign, String content) {
            this.type = type;
            this.sign = sign;
            this.content = content;
        }
    }

    private List<String> parseImageInText(String text) {
        List<String> images = new ArrayList<>();
        if (StringUtils.isEmpty(getContendAuditProperty().getImageUrlPattern())) {
            return images;
        }
        if (StringUtils.isEmpty(getContendAuditProperty().getImageSuffix())) {
            return images;
        }
        Set<String> imageSuffix = Arrays.stream(getContendAuditProperty().getImageSuffix().split(",")).collect(Collectors.toSet());
        Pattern urlPattern = Pattern.compile(getContendAuditProperty().getImageUrlPattern());
        Matcher urlMatcher = urlPattern.matcher(text);
        while (urlMatcher.find()) {
            // .png,.jpg,.jpeg,.bmp,.gif,.webp,.tiff
            String url = urlMatcher.group();
            String matchUrl = url.toLowerCase();
            if (imageSuffix.stream().anyMatch(matchUrl::endsWith)) {
                images.add(url);
            }
        }
        return images;
    }

//    public static void main(String[] args) {
//        String s = "<img width=\"100\" src=\"https://123.com/q.png\"/><img  src=\"https://123.com/q1.png\"/>";
//        Matcher imgMatcher = IMG_PATTERN.matcher(s);
//        while (imgMatcher.find()) {
//            Matcher urlMatcher = URL_PATTERN.matcher(imgMatcher.group());
//            if (urlMatcher.find()) {
//                System.out.println(urlMatcher.group());
//            }
//        }
//    }

    private String splitText(List<String> texts, Map<String/*sign*/, String> allTextMap) {
        if (CollectionUtils.isNotEmpty(texts)) {
            StringBuilder allTextBuilder = new StringBuilder();
            StringBuilder splitTextBuilder = new StringBuilder();
            int maxSize = getContendAuditProperty().getTextSplitSize();
            for (String s : texts) {
                if (StringUtils.isEmpty(s)) {
                    continue;
                }
                allTextBuilder.append(s).append(" ");
                int length = s.length();
                if (length > maxSize) {
                    // 如果此次的长度已超过了阈值，直接拆分此次的字符串
                    int n = length / maxSize; // 可以拆出n个最大长度的字符串
                    for (int i = 0; i < n; i++) {
                        String text = s.substring(i * maxSize, (i + 1) * maxSize);
                        allTextMap.put(Sha512DigestUtils.shaHex(text), text);
                    }
                    int j = length % maxSize;
                    if (j > 0) {
                        // 有余下的
                        s = s.substring(n * maxSize);
                    } else {
                        continue;
                    }
                }
                if (splitTextBuilder.length() + s.length() > maxSize) {
                    // 如果加上此次的长度超过了阈值，则新开一个
                    String text = splitTextBuilder.toString();
                    allTextMap.put(Sha512DigestUtils.shaHex(text), text);
                    splitTextBuilder = new StringBuilder();
                }
                splitTextBuilder.append(s).append(" ");
            }
            String text = splitTextBuilder.toString();
            allTextMap.put(Sha512DigestUtils.shaHex(text), text);
            return Sha512DigestUtils.shaHex(allTextBuilder.toString());
        }
        return null;
    }

    private static final String CACHE_ACCESS_TOKEN = "baidu-audit:accessToken:%s";
    private static final String URL_ACCESS_TOKEN = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s";
    private static final String URL_AUDIT_TEXT = "https://aip.baidubce.com/rest/2.0/solution/v1/text_censor/v2/user_defined?access_token=%s";
    private static final String URL_AUDIT_IMAGE = "https://aip.baidubce.com/rest/2.0/solution/v1/img_censor/v2/user_defined?access_token=%s";

    private SimpleContentAuditRecord auditContent(String accessToken, SignContent content) {
        if (content.getType() == ContentAuditType.text) {
            if (isEnableContendAuditText()) {
                return auditText0(accessToken, content.sign, content.getContent());
            } else {
                return passRecord();
            }
        } else if (content.getType() == ContentAuditType.image) {
            if (isEnableContendAuditImage()) {
                return auditImage0(accessToken, content.sign, content.getContent());
            } else {
                return passRecord();
            }
        } else {
            return null;
        }
    }

    private SimpleContentAuditRecord auditText0(String accessToken, String sign, String text) {
        SimpleContentAuditRecord record = getByCache(ContentAuditType.text, sign);
        if (record != null) {
            return record;
        }
        ContentAuditRecord entity = new ContentAuditRecord();
        entity.setSign(sign);
        entity.setType(ContentAuditType.text);
        entity.setContent(text);
        entity.setStatus(ContentAuditStatus.unknown);
        return auditFormServer(entity, URL_AUDIT_TEXT, accessToken, Map.of("text", text));
    }

    private SimpleContentAuditRecord getByCache(ContentAuditType type, String sign) {
        Date retainDate = DateHelper.toDate(LocalDateTime.now().minusDays(getContendAuditProperty().getTextRetainDays()));
        return repository.findFirstByTypeAndSignAndCreateTimeGreaterThanOrderByCreateTimeDesc(type, sign, retainDate);
    }

    private SimpleContentAuditRecord auditImage0(String accessToken, String sign, String image) {
        SimpleContentAuditRecord record = repository.findFirstByTypeAndSignOrderByCreateTimeDesc(
                ContentAuditType.image, sign
        );
        if (record != null) {
            return record;
        }
        ContentAuditRecord entity = new ContentAuditRecord();
        entity.setSign(sign);
        entity.setType(ContentAuditType.image);
        entity.setContent(image);
        entity.setStatus(ContentAuditStatus.unknown);
        return auditFormServer(entity, URL_AUDIT_IMAGE, accessToken, Map.of("imgUrl", image));
    }

    private SimpleContentAuditRecord auditFormServer(ContentAuditRecord record, String url, String accessToken, Map<String, String> body) {
        try {
            List<NameValuePair> params = new ArrayList<>();
            body.forEach((k, v) -> params.add(new BasicNameValuePair(k, v)));
            String response = Request.Post(String.format(url, accessToken))
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept", "application/json")
                    .bodyForm(params, StandardCharsets.UTF_8)
                    .connectTimeout(getContendAuditProperty().getTimeoutMs())
                    .socketTimeout(getContendAuditProperty().getTimeoutMs())
                    .execute().returnContent().asString();
            record.setResponse(response);
            AuditResponseInfo info = JsonHelper.toObject(response, AuditResponseInfo.class);
            if (info != null) {
                // 1：合规，2：不合规，3：疑似，4：审核失败
                if (info.getConclusionType() == 1) {
                    record.setStatus(ContentAuditStatus.pass);
                } else if (info.getConclusionType() == 2) {
                    record.setStatus(ContentAuditStatus.noPass);
                    record.setWords(JsonHelper.toJson(info.allWords()));
                } else if (info.getConclusionType() == 3) {
                    record.setStatus(ContentAuditStatus.suspected);
                    record.setWords(JsonHelper.toJson(info.allWords()));
                } else if (info.getConclusionType() == 4) {
                    record.setStatus(ContentAuditStatus.unknown);
                }
            }
        } catch (Throwable e) {
            log.error("auditFormServer", e);
        }
        repository.save(record);
        return SimpleContentAuditRecord.fromEntity(record);
    }

    private String getAccessToken() {
        String cacheKey = getAccessTokenCacheKey();
        String lockKey = cacheKey + "-lock";
        int waitTimes = 0;
        String accessToken;
        do {
            // 1 先从缓存获取
            accessToken = getAccessTokenFromCache(cacheKey);
            if (StringUtils.isNotEmpty(accessToken)) {
                return accessToken;
            }
            // 2 没有获取到，检查 apiKey secretKey
            checkBuildAccessToken();
            // 3 try lock
            if (lockBuildAccessToken(lockKey)) {
                try {
                    // 4 build access token
                    accessToken = getAccessTokenFromServer();
                    if (StringUtils.isEmpty(accessToken)) {
                        throwGetAccessTokenException();
                    } else {
                        cacheAccessToken(cacheKey, accessToken);
                        return accessToken;
                    }
                } finally {
                    // 5  release lock
                    unlockBuildAccessToken(lockKey);
                }
            } else {
                // 没有获取到锁，sleep
                waitGetAccessToken();
                waitTimes++;
            }
        } while (waitTimes < getContendAuditProperty().getAccessTokenWaitTimes());
        if (StringUtils.isEmpty(accessToken)) {
            throwGetAccessTokenException();
        }
        return accessToken;
    }

    private void waitGetAccessToken() {
        try {
            Thread.sleep(getContendAuditProperty().getAccessTokenWaitMs());
        } catch (InterruptedException e) {
            throwGetAccessTokenException();
        }
    }

    private void throwGetAccessTokenException() {
        throw new BadRequestException(false, "系统繁忙，请重试");
    }

    private String getAccessTokenCacheKey() {
        String keyPrefix = StringUtils.isEmpty(authProperties.getRedisPrefix()) ? "" : (authProperties.getRedisPrefix() + ":");
        return keyPrefix + String.format(CACHE_ACCESS_TOKEN, getContendAuditProperty().getApiKey());
    }

    private boolean lockBuildAccessToken(String lockKey) {
        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofSeconds(10));
        return Optional.ofNullable(b).orElse(false);
    }

    private void unlockBuildAccessToken(String lockKey) {
        stringRedisTemplate.delete(lockKey);
    }

    private String getAccessTokenFromCache(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    private void cacheAccessToken(String key, String accessToken) {
        stringRedisTemplate.opsForValue().set(key, accessToken, Duration.ofDays(getContendAuditProperty().getAccessTokenExpireDay()));
    }

    private String getAccessTokenFromServer() {
        try {
            String response = Request.Post(String.format(URL_ACCESS_TOKEN, getContendAuditProperty().getApiKey(), getContendAuditProperty().getSecretKey()))
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .connectTimeout(getContendAuditProperty().getTimeoutMs())
                    .socketTimeout(getContendAuditProperty().getTimeoutMs())
                    .execute().returnContent().asString();
            AccessTokenResponseInfo info = JsonHelper.toObject(response, AccessTokenResponseInfo.class);
            if (info != null) {
                return info.getAccessToken();
            }
        } catch (Throwable e) {
            log.error("getAccessTokenFromServer", e);
        }
        return null;
    }

    private void checkBuildAccessToken() {
        if (StringUtils.isEmpty(getContendAuditProperty().getApiKey())
                || StringUtils.isEmpty(getContendAuditProperty().getSecretKey())) {
            throw new BadRequestException("系统错误");
        }
    }

    @Getter
    @Setter
    public static class AccessTokenResponseInfo {
        @JsonProperty("access_token")
        private String accessToken;
    }

    @Getter
    @Setter
    public static class AuditResponseInfo {
        private int conclusionType;
        private List<AuditResponseDataInfo> data;

        public List<String> allWords() {
            List<String> allWords = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(data)) {
                data.forEach(i -> {
                    if (i != null) {
                        allWords.addAll(i.allWords());
                    }
                });
            }
            return allWords;
        }
    }

    @Getter
    @Setter
    public static class AuditResponseDataInfo {
        private int conclusionType;
        private String msg;
        private List<AuditResponseDataHitInfo> hits;

        public List<String> allWords() {
            List<String> allWords = new ArrayList<>();
            if (StringUtils.isNotEmpty(msg)) {
                allWords.add(msg);
            }
            if (CollectionUtils.isNotEmpty(hits)) {
                hits.forEach(i -> {
                    if (i != null) {
                        allWords.addAll(i.allWords());
                    }
                });
            }
            return allWords;
        }
    }

    @Getter
    @Setter
    public static class AuditResponseDataHitInfo {
        private String datasetName;
        private List<String> words;
        private List<String> details;

        public List<String> allWords() {
            List<String> allWords = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(words)) {
                allWords.addAll(words);
            }
            if (CollectionUtils.isNotEmpty(details)) {
                allWords.addAll(details);
            }
            return allWords;
        }
    }
}
