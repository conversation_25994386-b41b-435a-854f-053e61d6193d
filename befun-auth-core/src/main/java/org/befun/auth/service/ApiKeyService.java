package org.befun.auth.service;

import org.befun.auth.entity.ApiKey;
import org.befun.auth.entity.ApiKeyDto;
import org.befun.auth.repository.ApiKeyRepository;
import org.befun.auth.utils.GeneratorHelper;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.service.BaseSingleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class ApiKeyService extends BaseSingleService<ApiKey, ApiKeyDto, ApiKeyRepository> {

    @Autowired
    private IAuthEventTrigger authEventTrigger;

    private ApiKey get() {
        return Optional.of(repository.findAll()).flatMap(l -> l.stream().findFirst()).orElse(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiKeyDto refresh() {
        ApiKey apiKey = get();
        if (apiKey == null) {
            apiKey = new ApiKey();
        }
        apiKey.setApiKey(GeneratorHelper.generatorApiKey());
        apiKey.setApiSecret(GeneratorHelper.generatorApiSecret());
        repository.save(apiKey);
        authEventTrigger.apiKeyRefresh(apiKey.orgId, apiKey.userId, apiKey.getId());
        return mapperService.map(apiKey, ApiKeyDto.class);
    }

    public ApiKey getByUserId(Long userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        return repository.findFirstByUserId(userId);
    }

    public ApiKey getOrgApiKey(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return null;
        }
        return repository.getOrgApiKey(orgId);
    }
}