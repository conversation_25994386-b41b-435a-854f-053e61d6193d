package org.befun.auth.service.auth;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.open.api.WxOpenMpService;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AllAuthDto;
import org.befun.auth.dto.auth.WechatOpenAuthDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.auth.workertrigger.AuthEventTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.extension.Extensions;
import org.befun.extension.property.WeChatOpenProperty;
import org.befun.extension.service.WeChatOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.befun.auth.constant.ThirdPartyAuthType.WECHAT_OPEN;

@Slf4j
@Service
@ConditionalOnBean(value = WeChatOpenService.class)
public class AuthWechatOpenService extends BaseAuthService<WechatOpenConfig, WechatOpenAuthDto> {

    @Autowired
    private WeChatOpenService weChatOpenService;
    @Autowired
    private WeChatOpenProperty weChatOpenProperty;
    @Autowired
    private AuthEventTrigger authEventTrigger;

    @Override
    public ThirdPartyAuthType getAuthType() {
        return WECHAT_OPEN;
    }

    @Override
    public String buildSource(Long orgId, WechatOpenConfig config) {
        return buildSource(config.getAppId());
    }

    public String buildSource(String appId) {
        return WECHAT_OPEN.getSourcePrefix() + appId;
    }

    @Override
    public void fillAllAuthDto(AllAuthDto allAuthDto, List<WechatOpenAuthDto> dto) {
        allAuthDto.setWechatOpen(dto);
    }

    /**
     * 创建授权链接
     */
    public String authorize(String app) {
        try {
            return weChatOpenService.getWxOpenComponentService().getPreAuthUrl(weChatOpenProperty.getAuthorizeCallbackUrl());
        } catch (WxErrorException ex) {
            log.error(ex.getMessage());
            throw new BadRequestException("创建微信公众号授权链接失败");
        }
    }

    /**
     * 授权成功后回调，绑定到企业
     * 如果企业已有授权记录，则修改授权状态为true，否则新建授权记录
     */
    public WechatOpenAuthDto authorizeCallback(String authCode, String app) {
        try {
            long orgId = TenantContext.requireCurrentTenant();
            long userId = TenantContext.requireCurrentUserId();
            WxOpenQueryAuthResult queryAuthResult = weChatOpenService.getWxOpenComponentService().getQueryAuth(authCode);
            String appId = queryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
            WxOpenAuthorizerInfoResult info = weChatOpenService.getWxOpenComponentService().getAuthorizerInfo(appId);
            log.info("authorize appId: {}", appId);
            String source = buildSource(appId);
            ThirdPartyAuth entity = thirdPartyAuthService.getBySourceWithLogin(getAuthType(), source, app);
            if (entity != null) {
                updateConfig(entity, () -> buildConfig(appId, info), config -> resetConfig(config, appId, info));
            } else {
                WechatOpenConfig config = buildConfig(appId, info);
                entity = add(orgId, app, config, null);
            }
            authEventTrigger.wechatOpenAuthorize(orgId, userId, entity.getId(), appId);
            return mapToDto(entity, app);
        } catch (WxErrorException ex) {
            throw new BadRequestException("微信公众号授权绑定失败");
        }
    }

    /**
     * 构造微信公众号配置信息
     */
    private boolean resetConfig(WechatOpenConfig config, String appId, WxOpenAuthorizerInfoResult info) {
        config.setAppId(appId);
        config.setAuthorized(true);
        config.setLogo(info.getAuthorizerInfo().getHeadImg());
        config.setName(info.getAuthorizerInfo().getNickName());
        config.setDescription(info.getAuthorizerInfo().getSignature());
        config.formatFuncScopeCategory(info.getAuthorizationInfo().getFuncInfo());
        return true;
    }

    /**
     * 构造微信公众号配置信息
     */
    private WechatOpenConfig buildConfig(String appId, WxOpenAuthorizerInfoResult info) {
        WechatOpenConfig config = new WechatOpenConfig();
        resetConfig(config, appId, info);
        return config;
    }

    /**
     * ticket, 取消授权，授权变更，
     */
    public String eventCallbackUrl(String app, String timestamp, String nonce, String encryptType, String msgSignature, String body) {
        try {
            WxOpenXmlMessage message = WxOpenXmlMessage.fromEncryptedXml(body, weChatOpenService.getWxOpenConfigStorage(), timestamp, nonce, msgSignature);
            switch (message.getInfoType()) {
                case "unauthorized" -> {
                    log.info("接收到微信公众号取消授权事件，appId={}", message.getAuthorizerAppid());
                    eventUnauthorized(app, message.getAuthorizerAppid());
                }
                case "component_verify_ticket" -> {
                    log.info("接收到微信公众号ticket事件，ticket={}", message.getComponentVerifyTicket());
                    weChatOpenService.getWxOpenConfigStorage().setComponentVerifyTicket(message.getComponentVerifyTicket());
                }
                case "authorized" -> {
                    log.info("接收到微信公众号授权事件，appId={}, preAuthCode={}, authCode={}", message.getAuthorizerAppid(), message.getPreAuthCode(), message.getAuthCode());
                }
                case "updateauthorized" -> {
                    log.info("接收到微信公众号授权变更事件，appId={}, preAuthCode={}, authCode={}", message.getAuthorizerAppid(), message.getPreAuthCode(), message.getAuthCode());
                }
            }
        } catch (Throwable ex) {
            log.error("微信公众号接受事件：处理失败", ex);
        }
        return "success";
    }

    public void eventUnauthorized(String app, String appId) {
        List<ThirdPartyAuth> list = thirdPartyAuthService.getListBySource(buildSource(appId), getAuthType(), app);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(entity -> {
                updateConfig(entity,
                        null,
                        config -> {
                            if (config.isAuthorized()) {
                                config.setAuthorized(false);
                                return true;
                            }
                            return false;
                        });
            });
        }
    }

    public String messageCallbackUrl(String app, String appId, String timestamp, String nonce, String encryptType, String msgSignature, String body) {
        try {
            WxOpenMpService mpService = weChatOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
            WxMpXmlMessage message = WxMpXmlMessage.fromEncryptedXml(body, mpService.getWxMpConfigStorage(), timestamp, nonce, msgSignature);
            String msgType = message.getMsgType();
            if (WxConsts.XmlMsgType.TEXT.equalsIgnoreCase(msgType)) {
                // 全网发布检测
                String reply = wechatOpenPublish(mpService, appId, message);
                if (reply != null) {
                    return reply;
                }
            } else if (WxConsts.XmlMsgType.EVENT.equalsIgnoreCase(msgType)) {
                if (message.getEvent().equals(WxConsts.EventType.SUBSCRIBE)) {
                    String openId = message.getFromUser();
                    authEventTrigger.wechatOpenSubscribe(appId, openId);
                } else if (message.getEvent().equals(WxConsts.EventType.UNSUBSCRIBE)) {
                    String openId = message.getFromUser();
                    authEventTrigger.wechatOpenUnsubscribe(appId, openId);
                }
            }
        } catch (Throwable ex) {
            log.error("微信公众号接受消息：处理失败", ex);
        }
        return "success";
    }

    /**
     * 全网发布检测
     */
    private String wechatOpenPublish(WxOpenMpService mpService, String appId, WxMpXmlMessage message) throws WxErrorException {
        Map<String, String> testAccount = weChatOpenProperty.getPublishTestAccount();
        String content = message.getContent();
        String username;
        if (content != null && testAccount != null && (username = testAccount.get(appId)) != null) {
            if ("TESTCOMPONENT_MSG_TYPE_TEXT".equals(message.getContent())) {
                return WxMpXmlOutMessage.TEXT()
                        .fromUser(message.getToUser())
                        .toUser(message.getFromUser())
                        .content("TESTCOMPONENT_MSG_TYPE_TEXT_callback")
                        .build()
                        .toEncryptedXml(mpService.getWxMpConfigStorage());
            } else if (content.startsWith("QUERY_AUTH_CODE:")) {
                String authCode = content.replace("QUERY_AUTH_CODE:", "");
                weChatOpenService.getWxOpenComponentService().getQueryAuth(authCode);
                String reply = authCode + "_from_api";
                WxMpKefuMessage replyMessage = WxMpKefuMessage.TEXT().content(reply).toUser(message.getFromUser()).build();
                mpService.getKefuService().sendKefuMessage(replyMessage);
                return "";
            }
        }
        return null;
    }

    @Override
    @Transactional
    public boolean deleteByConfigId(Long configId, Map<String, Object> extParams) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        ThirdPartyAuth entity = thirdPartyAuthService.requireWithFilter(configId);
        WechatOpenConfig config = getConfig(entity);
        if (config != null && config.isAuthorized()) {
            throw new BadRequestException("请先解除授权，在删除");
        }
        thirdPartyAuthService.delete(entity);
        Object deleteUserParam;
        boolean deleteUser = false;
        if (extParams != null && (deleteUserParam = extParams.get("deleteUser")) != null && deleteUserParam instanceof Boolean) {
            deleteUser = (boolean) deleteUserParam;
        }
        authEventTrigger.wechatOpenDelete(orgId, userId, entity.getId(), deleteUser);
        return true;
    }
}
