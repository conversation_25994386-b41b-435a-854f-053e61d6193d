package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationWallet;
import org.befun.auth.projection.SimpleWalletMoney;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class OrganizationWalletService {

    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 初始化企业钱包
     */
    public void initOrganizationWallet(Organization organization) {
        log.info("init orgId:{} wallet", organization.getId());
        OrganizationWallet organizationWallet = new OrganizationWallet(0, 0, 0);
        organizationWallet.setOrgId(organization.getId());
        organizationWalletRepository.save(organizationWallet);
    }

    public OrganizationWallet getByOrg(Long orgId) {
        return organizationWalletRepository.findFirstByOrgId(orgId);
    }

    @Transactional
    public int recharge(Long orgId, int amount) {
        balance(orgId);
        Long balance = getValueOpt().increment(balanceKey(orgId), amount);
        if (balance != null) {
            syncToDb(orgId);
            return balance.intValue();
        }
        return 0;
    }

    /**
     * 查询真实余额
     */
    public int balance(Long orgId) {
        String cache = getValueOpt().get(balanceKey(orgId));
        if (NumberUtils.isDigits(cache)) {
            return Integer.parseInt(cache);
        }
        SimpleWalletMoney money = organizationWalletRepository.findFirstWalletMoneyByOrgId(orgId);
        int balance;
        if (money == null || money.getMoney() == null) {
            balance = 0;
        } else {
            balance = money.getMoney();
        }
        getValueOpt().setIfAbsent(balanceKey(orgId), Integer.toString(balance));
        return balance;
    }

    /**
     * 使用钱包金额，如果扣完之后小于0，则需要回退
     */
    public int consumer(Long orgId, Integer cost) {
        if (cost <= 0) {
            throw new BadRequestException("付款金额必须大于0");
        }
        if (!hasBalance(orgId, cost)) {
            throw new BadRequestException("余额不足");
        }
        String key = balanceKey(orgId);
        Long balance = getValueOpt().decrement(key, cost);
        if (balance == null || balance < 0) {
            getValueOpt().increment(key, cost);
            throw new BadRequestException("余额不足");
        }
        log.info("企业({})钱包余额付款成功(-{})，余额：{}", orgId, cost, balance);
        syncToDb(orgId);
        return balance.intValue();
    }

    /**
     * 是否有额度
     */
    public boolean hasBalance(Long orgId, Integer cost) {
        int balance = balance(orgId);
        return balance - cost >= 0;
    }

    @Transactional
    public void syncToDb(Long orgId) {
        int balance = balance(orgId);
        organizationWalletRepository.updateWalletMoney(orgId, balance);
    }

    private ValueOperations<String, String> getValueOpt() {
        return stringRedisTemplate.opsForValue();
    }

    private String balanceKey(Long orgId) {
        return String.format("wallet-balance:%d", orgId);
    }
}
