package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class WechatOpenConfig extends AbstractConfig implements IClearConfigSecret {

    @JsonView(ResourceViews.Basic.class)
    private String name;

    @JsonView(ResourceViews.Basic.class)
    private String logo;

    @JsonView(ResourceViews.Basic.class)
    private String description;

    @Schema(description = "是否授权成功")
    @JsonView(ResourceViews.Basic.class)
    private boolean authorized;

    @Schema(description = "最后同步客户时间")
    @JsonView(ResourceViews.Basic.class)
    private Date lastSyncTimeCustomer;

    @Schema(description = "最后同步模版时间")
    @JsonView(ResourceViews.Basic.class)
    private Date lastSyncTimeTemplate;

    @Schema(hidden = true)
    private String appId;
    @Schema(hidden = true, description = "权限集id列表(,分隔)")
    private String funcScopeCategory;

    @Override
    public void clearSecret() {
        appId = null;
        funcScopeCategory = null;
    }

    public void formatFuncScopeCategory(List<Integer> categoryIds) {
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            funcScopeCategory = categoryIds.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
        }
    }
}
