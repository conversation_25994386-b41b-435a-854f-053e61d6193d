package org.befun.auth.service;

import cn.hanyi.common.ip.resolver.IpResolverService;
import cn.hanyi.common.ip.resolver.RegionInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.*;
import org.befun.auth.dto.*;
import org.befun.auth.dto.orgconfig.OrgConfigBaseInfoDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.entity.*;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.utils.ExcelParserHelper;
import org.befun.auth.utils.PasswordHelper;
import org.befun.auth.utils.StringHelper;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RegHelper;
import org.befun.core.utils.RestUtils;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.befun.auth.constant.Permissions.*;
import static org.befun.auth.constant.UserStatus.DISABLE;
import static org.befun.auth.constant.UserStatus.ENABLE;

@Service
public class UserService extends BaseService<User, UserDto, UserRepository> {
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserInviteService userInviteService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationWalletService organizationWalletService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;

    @Autowired
    private IAuthEventTrigger authEventTrigger;

    @Autowired
    private IpResolverService ipResolverService;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private PasswordHelper passwordHelper;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private OrganizationAIPointService organizationAIPointService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public <S extends ResourceCustomQueryDto> Page<UserDto> findAll(S params) {
        UserQueryDto query = (UserQueryDto) params;
        long orgId = TenantContext.requireCurrentTenant();
        Set<Long> roleIds = query.parseRoleIds();
        Set<Long> departmentIds = query.parseDepartmentIds();
        Set<UserStatus> status = query.parseStatus();

        // 处理子部门逻辑
        if (Boolean.TRUE.equals(query.getWithSubDepartment())) {
            Set<Long> expandedDepartmentIds = new HashSet<>();
            departmentIds.addAll(TenantContext.getCurrentSubDepartmentIds());
        }

        SqlBuilder sqlBuilder = createSqlBuilder(orgId, query.getQ(), roleIds, departmentIds, status, null, query.getPage(), query.getLimit());
        return nativeSqlHelper.queryDtoPage(sqlBuilder, (type, ids) -> {
            List<User> entity = scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findByIdIn(ids));
            List<UserDto> dto = mapToDto(entity);
            afterMapToDto(entity, dto);
            return dto;
        });
    }

    public SqlBuilder createSqlBuilder(long orgId,
                                       String q,
                                       Collection<Long> roleIds,
                                       Collection<Long> departmentIds,
                                       Collection<UserStatus> status,
                                       Collection<Long> excludeIds,
                                       Integer page,
                                       Integer limit) {
        String statusString = CollectionUtils.isEmpty(status) ? null : status.stream().filter(Objects::nonNull).map(UserStatus::getStatus).map(Objects::toString).collect(Collectors.joining(","));
        String roleIdsString = CollectionUtils.isEmpty(roleIds) ? null : roleIds.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
        String excludeIdsString = CollectionUtils.isEmpty(excludeIds) ? null : excludeIds.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
        SqlBuilder sqlBuilder = SqlBuilder.select("select u.id, 1 type from user u")
                .countSelect("select count(u.id) from user u")
                .where("u.org_id = %d", orgId).alwaysTrue()
                .where("u.is_delete=0").alwaysTrue()
                .where("u.id not in (%s)", excludeIdsString).ifTrue(StringUtils.isNotEmpty(excludeIdsString))
                .where("u.status in (%s)", statusString).ifTrue(StringUtils.isNotEmpty(statusString))
                .where("(u.truename like '%1$s' or u.mobile like '%1$s' or u.email like '%1$s')", "%" + q + "%").ifTrue(StringUtils.isNotEmpty(q))
                .where(() -> departmentIds.stream()
                        .map(i -> String.format("u.department_ids like '%s'", "%[" + i + "]%"))
                        .collect(Collectors.joining(" or ", "(", ")"))).ifTrue(CollectionUtils.isNotEmpty(departmentIds))
                .where("exists (select 1 from user_role ur where u.id = ur.user_id and ur.role_id in (%s))", roleIdsString).ifTrue(StringUtils.isNotEmpty(roleIdsString))
                .orderByDesc("u.is_admin")
                .orderByAsc("u.id");
        if (page != null && page > 0 && limit != null && limit > 0) {
            sqlBuilder.limit(page, limit);
        }
        return sqlBuilder;
    }


    @Override
    public void afterMapToDto(List<User> entityList, List<UserDto> dtoList) {
        if (CollectionUtils.isEmpty(entityList) || CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        List<Long> allIds = new ArrayList<>();
        Set<Long> allDepartmentIds = new HashSet<>();
        Map<Long/*userId*/, Set<Long>> userDepartmentIdsMap = new HashMap<>();
        entityList.forEach(i -> {
            Optional.ofNullable(i.getId()).ifPresent(allIds::add);
            Optional.ofNullable(i.parseDepartmentIds()).ifPresent(j -> {
                allDepartmentIds.addAll(j);
                userDepartmentIdsMap.put(i.getId(), j);
            });
        });
        Map<Long/*departmentId*/, DepartmentDto> departmentMap = departmentService.getGroupMapByIds(allDepartmentIds, Department::getId);
        Map<Long/*userId*/, List<UserInvitationDto>> invitationMap = userInviteService.getGroupMapListByPropertyIds("userId", allIds, UserInvitation::getUserId);
        Map<Long/*userId*/, List<RoleDto>> roleMap = roleService.getByUserIds(allIds);
        boolean currentIsAdmin = currentIsAdmin();
        boolean currentHasPermissionUserEdit = currentIsAdmin || currentHasPermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT);
        boolean currentHasPermissionRoleEdit = currentIsAdmin || currentHasPermissions(PermissionPath.SYS_MANAGE_ROLE_MANAGE_EDIT);
        List<Long> currentSubDepartmentIds = TenantContext.getCurrentSubDepartmentIds();
        dtoList.forEach(i -> {
            Optional.ofNullable(i.getEntity()).ifPresent(user -> {
//                i.setUsername(i.getTruename()); 这里不再赋值了，由前端控制显示 truename
                Optional.ofNullable(userDepartmentIdsMap.get(user.getId())).ifPresent(j -> {
                    List<DepartmentDto> departments = new ArrayList<>();
                    List<Long> departmentIds = new ArrayList<>();
                    List<String> departmentNames = new ArrayList<>();
                    j.forEach(k -> {
                        Optional.ofNullable(departmentMap.get(k)).ifPresent(l -> {
                            departments.add(l);
                            departmentIds.add(l.getId());
                            departmentNames.add(l.getTitle());
                        });
                    });
                    i.setDepartments(departments);
                    i.setDepartmentIds(departmentIds);
                    i.setDepartmentNames(String.join(",", departmentNames));
                });
                Optional.ofNullable(roleMap.get(user.getId())).ifPresent(roles -> {
                    List<String> roleNames = new ArrayList<>();
                    List<Long> roleIds = new ArrayList<>();
                    roles.forEach(role -> {
                        roleNames.add(role.getName());
                        roleIds.add(role.getId());
                    });
                    i.setRoles(roles);
                    i.setRoleNames(String.join(",", roleNames));
                    i.setRoleIds(roleIds);
                });
                Optional.ofNullable(invitationMap.get(user.getId())).ifPresent(invitations -> {
                    if (!invitations.isEmpty()) {
                        i.setInvitation(invitations.get(invitations.size() - 1));
                    }
                });
                // v.10.9
                // 当用户没有超级管理员的角色：
                // 如果该用户有成员管理编辑权限时，不能对不是本部门和子部门的用户进行编辑
                // 如果该用户同时具有角色管理编辑权限，可以修改用户角色，但不能为用户分配“超级管理员”角色，复选框禁用。
                // 如果该用户不具有角色管理编辑权限，则不能修改用户角色，所有复选框禁用。
                boolean isAdmin = Optional.ofNullable(user.getIsAdmin()).orElse(false);
                if (!isAdmin) {
                    if (currentIsAdmin) {
                        i.setEnableEditUser(true);
                        i.setEnableEditRole(true);
                        i.setEnableSelectAdminRole(true);
                    } else if (currentHasPermissionUserEdit) {
                        if (user.parseDepartmentIds().stream().anyMatch(currentSubDepartmentIds::contains)) {
                            i.setEnableEditUser(true);
                            if (currentHasPermissionRoleEdit) {
                                i.setEnableEditRole(true);
                            }
                        }
                    }
                }
            });
        });
    }

    /**
     * 获取机构下面所有成员
     */
    public List<UserDto> getAllUsersInOrg(Long orgId) {
        List<User> users = userRepository.findAllByOrgId(orgId);
        return mapToDto(users);
    }

    public List<User> getAllUsersByDepartmentIds(Long orgId, List<Long> departmentIds) {
        if (orgId == null || CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        SqlBuilder sqlBuilder = createSqlBuilder(orgId, null, null, departmentIds, null, null, null, null);
        return nativeSqlHelper.queryList(sqlBuilder, User::getId, (t, ids) -> scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findByIdIn(ids)));
    }

    /**
     * 获取部门所有成员
     */
    public List<UserDto> getAllUsersInDepartment(Long orgId, Long departId) {
        List<User> users = getAllUsersByDepartmentIds(orgId, departId == null ? null : List.of(departId));
        return mapToDto(users);
    }

    /**
     * 获取角色所有成员
     */
    public List<UserDto> getAllUsersInRole(Long orgId, Long roleId) {
        if (orgId == null || roleId == null) {
            return null;
        }
        List<User> users = userRoleService.getByRole(roleId);
        return mapToDto(users);
    }

    public boolean exists(Long userId) {
        if (userId != null && userId > 0) {
            return userRepository.existsById(userId);
        }
        return false;
    }

    public boolean existsByUsername(Long orgId, String username) {
        if (orgId != null && StringUtils.isNotEmpty(username)) {
            return userRepository.existsByOrgIdAndUsername(orgId, username);
        }
        return false;
    }

    public boolean existsByMobile(String mobile) {
        if (StringUtils.isNotEmpty(mobile)) {
            return userRepository.existsByMobile(mobile);
        }
        return false;
    }

    public boolean existsByEmail(String email) {
        if (StringUtils.isNotEmpty(email)) {
            return userRepository.existsByEmail(email);
        }
        return false;
    }


    public boolean existsByEmployeeNo(Long orgId, String employeeNo) {
        if (StringUtils.isNotEmpty(employeeNo)) {
            return userRepository.existsByOrgIdAndEmployeeNo(orgId, employeeNo);
        }
        return false;
    }

    public User getByEmail(String email) {
        if (StringUtils.isNotEmpty(email)) {
            return userRepository.getFirstByEmail(email);
        }
        return null;
    }

    public List<User> getListByMobile(String mobile) {
        if (StringUtils.isNotEmpty(mobile)) {
            return userRepository.findByMobile(mobile);
        }
        return null;
    }

    public User getByOrgEmployeeNo(Long orgId, String employeeNo) {
        if (StringUtils.isNotEmpty(employeeNo)) {
            return userRepository.findFirstByOrgIdAndEmployeeNo(orgId, employeeNo);
        }
        return null;
    }

    public List<User> getListByEmail(String email) {
        if (StringUtils.isNotEmpty(email)) {
            return userRepository.findByEmail(email);
        }
        return null;
    }

    public Optional<SimpleUser> getSimple(Long userId) {
        return Optional.ofNullable(userRepository.findSimpleById(userId));
    }

    public SimpleUser requireSimple(Long userId) {
        Optional<SimpleUser> user;
        if (userId != null && userId > 0 && (user = getSimple(userId)).isPresent()) {
            return user.get();
        }
        throw new EntityNotFoundException(User.class);
    }

    public List<SimpleUser> getSimpleByIds(Set<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        Set<Long> ids = userIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (ids.isEmpty()) {
            return null;
        }
        return userRepository.findSimpleByIdIn(ids);
    }

    public Map<Long, SimpleUser> getSimpleMapByIds(Set<Long> userIds) {
        Map<Long, SimpleUser> r = new HashMap<>();
        List<SimpleUser> users = getSimpleByIds(userIds);
        if (CollectionUtils.isNotEmpty(users)) {
            users.forEach(i -> r.put(i.getId(), i));
        }
        return r;
    }

    /**
     * 获取成员，指定角色
     */
    public Map<Long, List<SimpleUser>> getSimpleMapByRoleIds(Long orgId, List<Long> roleIds) {
        if (orgId == null || CollectionUtils.isEmpty(roleIds)) {
            return new HashMap<>();
        }
        return userRoleService.getByRoles(orgId, roleIds);
    }

    public List<SimpleUser> getSimpleByOrgId(Long orgId) {
        if (orgId == null) {
            return null;
        }
        return userRepository.findSimpleByOrgId(orgId);
    }

    /**
     * 事件协作用户：指当前用户的所在部门和子部门及此部门的直接上级所有部门的所有用户
     * 此方法目前只用作查询事件处理的协作用户，所以还需要这些协作用户有处理事件的权限
     */
    public Page<SimpleUser> cooperationUsers(int page, int limit, String q) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        List<Long> fullDepartmentIds = departmentService.getFullDepartmentIdList(orgId, userId);
        // 获得我的全部部门id
        if (CollectionUtils.isEmpty(fullDepartmentIds)) {
            return Page.empty();
        }
        // 获得有事件处理的权限的角色
        List<Long> roleIds = roleService.toIds(roleService.getByOrgAndPermission(orgId, List.of(EVENTS_EVENT_ACTION_EDIT.getPath(), EVENTS_EVENT_ACTION_VIEW.getPath())));
        if (CollectionUtils.isEmpty(roleIds)) {
            return Page.empty();
        }
        SqlBuilder sqlBuilder = createSqlBuilder(orgId, q, roleIds, fullDepartmentIds, null, List.of(userId), page, limit);
        return nativeSqlHelper.queryPage(sqlBuilder, SimpleUser::getId, (ignore, ids) -> getSimpleByIds(new HashSet<>(ids)));
    }

    /**
     * 问卷审核用户
     * 此方法目前只用作查询问卷审核的用户，所以还需要这些用户有问卷审核的权限
     */
    public Page<SimpleUser> surveyVerifyUsers(int page, int limit, String q) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        // 选择审核人的时候，只能选择所在部门和上级部门作为审核人。


        List<Long> departmentIds = new ArrayList<>();

        Optional.ofNullable(departmentService.parentTreeByUser(orgId, userId)).ifPresent(i -> departmentIds.addAll(i.stream().map(DepartmentTreeDto::getId).collect(Collectors.toList())));

        // 获得有问卷审核的权限的角色
        List<Long> roleIds = roleService.toIds(roleService.getByOrgAndPermission(orgId, List.of(TOUCH_MANAGE_SURVEY_VERIFY.getPath())));
        if (CollectionUtils.isEmpty(roleIds)) {
            return Page.empty();
        }
        SqlBuilder sqlBuilder = createSqlBuilder(orgId, q, roleIds, departmentIds.isEmpty() ? null : departmentIds, null, List.of(userId), page, limit);
        return nativeSqlHelper.queryPage(sqlBuilder, SimpleUser::getId, (ignore, ids) -> getSimpleByIds(new HashSet<>(ids)));
    }

    /**
     * 查询企业最大用户数量
     */
    public int maxUserSize(Organization org) {
        if (org == null) {
            org = organizationService.getCurrent();
        }
        if (org == null) {
            return 0;
        }
        return organizationService.parseOrgOptionalLimit(org).getChildUserLimit();
    }

    /**
     * 查询企业的可注册的用户数量
     */
    public int remainingUserSize(Organization org) {
        if (org == null) {
            org = organizationService.getCurrent();
        }
        if (org == null) {
            return 0;
        }
        int limit = maxUserSize(org);
        int existsSize = userRepository.countByOrgIdAndStatusIn(org.getId(), UserStatus.activeStatus());
        return Math.max(limit - existsSize, 0);
    }

    private void checkNotSelf(Long userId, String opDesc) {
        Long currentUserId = TenantContext.getCurrentUserId();
        if (userId == null || currentUserId == null || userId.equals(currentUserId)) {
            throw new BadRequestException(String.format("不能%s当前用户", opDesc));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean enableUser(Long userId) {
        checkNotSelf(userId, "启用");
        User user = require(userId);
        if (remainingUserSize(null) <= 0) {
            throw new BadRequestException("成员数量已达到满额，如需添加新成员，请购买可使用名额。");
        }
        user.setStatus(ENABLE.getStatus());
        userRepository.save(user);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean disableUser(Long userId) {
        checkNotSelf(userId, "禁用");
        User user = require(userId);
        clearLoginToken(user);
        user.setStatus(DISABLE.getStatus());
        userRepository.save(user);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean disableUserByDepartmentIds(Long orgId, List<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return true;
        }
        List<User> users = getAllUsersByDepartmentIds(orgId, departmentIds);
        if (CollectionUtils.isNotEmpty(users)) {
            users.forEach(u -> {
                u.setStatus(DISABLE.getStatus());
                clearLoginToken(u);
            });
            userRepository.saveAll(users);
        }
        return true;
    }

    /**
     * 删除用户和禁用用户后，清除登录token
     */
    private void clearLoginToken(User user) {
        if (user != null) {
            String userSessionKey = "user.session:" + user.getId();
            Long size = stringRedisTemplate.opsForZSet().zCard(userSessionKey);
            if (size != null && size > 0) {
                Set<String> tokens = stringRedisTemplate.opsForZSet().range(userSessionKey, 0, -1);
                if (CollectionUtils.isNotEmpty(tokens)) {
                    tokens.forEach(i -> stringRedisTemplate.delete("session:" + i));
                }
                stringRedisTemplate.delete(userSessionKey);
            }
        }
    }

    /**
     * 1 删除 token
     * 1 删除用户
     */
    @Override
    @Transactional
    public Boolean deleteOne(long userId) {
        checkNotSelf(userId, "删除");
        User user = require(userId);
        if (user.getIsAdmin() != null && user.getIsAdmin()) {
            throw new BadRequestException("不能删除此用户");
        }
        clearLoginToken(user);
//        if (user.getUserRoles() != null && !user.getUserRoles().isEmpty()) {
//            userRoleRepository.deleteAll(user.getUserRoles());
//        }
        thirdPartyUserService.deleteByUser(userId);
        userRepository.delete(user);
        authEventTrigger.userDelete(user.getOrgId(), user.getId());
        return true;
    }

    /**
     * 保存（用户名|邮箱|手机号）之前需要校验是否在
     *
     * @return 是否存在
     */
    public boolean existsUser(Long id, List<User> users) {
        if (CollectionUtils.isEmpty(users)) {
            // 未查到，不存在
            return false;
        }
        if (id == null) {
            return true;
        } else {
            return users.stream().noneMatch(i -> id.equals(i.getId()));
        }
    }

    public boolean existsUser(Long id, User user) {
        if (user == null) {
            // 未查到，不存在
            return false;
        }
        if (id == null) {
            return true;
        } else {
            // 不是当前用户
            return !id.equals(user.getId());
        }
    }

    /**
     * 通过第三方注册用户
     */
    public User addUserByThirdParty(ThirdPartyAuth auth, String openId, String name, String mobile, String email, Set<String> departmentCode, String employeeNo) {
        Long orgId = auth.getOrgId();
        if (orgId == null || orgId <= 0) {
            return null;
        }
        Organization org = organizationService.getById(orgId).orElse(null);
        return addUserByThirdParty(org, auth, UserStatus.ENABLE, openId, name, mobile, email, departmentCode, employeeNo);
    }

    /**
     * 通过第三方注册用户
     */
    public User addUserByThirdParty(Organization org, ThirdPartyAuth auth, UserStatus status, String openId, String name, String mobile, String email, Set<String> departmentCode, String employeeNo) {
        if (org == null) {
            return null;
        }
        Long orgId = org.getId();
        int size = remainingUserSize(org);
        if (size <= 0) {
            throw new BadRequestException("成员数量已达到满额，如需添加新成员，请购买可使用名额。");
        }
        String username = name == null ? openId : name;
        if (StringUtils.isNotEmpty(username)
                && existsByUsername(orgId, username)) {
            username = null;
        }

        if (StringUtils.isNotEmpty(mobile) && RegHelper.isMobile(mobile)) {
            List<User> users = getListByMobile(mobile);
            // 全系统只有一个手机号
            if (CollectionUtils.isNotEmpty(users)) {
                // 如果当前企业下有这个手机号的用户，就返回这个用户
                // 如果没有，就返回null 不允许注册
                return users.stream().filter(i -> i.getOrgId().equals(orgId)).findFirst().orElse(null);
            }
        }

        if (StringUtils.isNotEmpty(email) && RegHelper.isEmail(email)) {
            // 全系统只有一个邮箱
            List<User> users = getListByEmail(email);
            if (CollectionUtils.isNotEmpty(users)) {
                // 如果当前企业下有这个邮箱的用户，就返回这个用户
                // 如果没有，就返回null 不允许注册
                return users.stream().filter(i -> i.getOrgId().equals(orgId)).findFirst().orElse(null);
            }
        }

        if (StringUtils.isNotEmpty(employeeNo)) {
            User user = getByOrgEmployeeNo(orgId, employeeNo);
            if (user != null) {
                return user;
            }
        }

        Role memberRole = roleService.getOrCreateMemberRole(orgId);
        List<Department> department = new ArrayList<>();
        departmentCode.forEach(code -> {
            Optional.ofNullable(departmentService.getByEquivalentCode(orgId, code)).ifPresent(department::addAll);
        });

        if (department.isEmpty()) {
            throw new BadRequestException(String.format("没有匹配到有效部门-%s", JsonHelper.toJson(departmentCode)));
        }

        User user = addUserByThirdParty(orgId, status, name, mobile, email, username, List.of(memberRole.getId()), department, employeeNo);
        // 添加用户角色
        userRoleService.addUserRole(user, memberRole);
        thirdPartyUserService.add(orgId, user.getId(), auth.getSource(), auth.getApp(), openId, name, true);
        return user;
    }

    /**
     * 通过第三方注册用户
     */
    public User addUserByThirdParty(Long orgId, UserStatus status, String name, String mobile, String email, String username, List<Long> roleId, List<Department> department, String employeeNo) {
        User user = new User();
        user.setOrgId(orgId);
        user.setTruename(name);
        user.setMobile(mobile);
        user.setOldRoleId(roleId == null ? null : roleId.stream().map(Objects::toString).collect(Collectors.joining(",")));
        user.setEmail(email);
        user.setUsername(username);
        department.forEach(i -> user.appendDepartmentId(i.getId()));
        user.setIsAdmin(false);
        user.setStatus(status.getStatus());
        user.setAvailableSystems("{\"login_surveyplus\":0,\"login_cem\":1}");
        user.setIsFinishedGuide("N");
        int currentTime = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        user.setCreated(currentTime);
        user.setUpdated(currentTime);
        user.setEmployeeNo(employeeNo);
        userRepository.save(user);
        return user;
    }

//    public User addUserByThirdParty(Long orgId, String name, String mobile, String email, List<Long> roleId, Department department, String employeeNo) {
//        return addUserByThirdParty(orgId, ENABLE, name, mobile, email, null, roleId, department, employeeNo);
//    }

    /**
     * 通过邮箱邀请用户（用户状态为待邀请激活）
     */
    public User addUserByEmailInvite(Long orgId, IUserInviteInfo inviteInfo, List<Role> roles, List<Department> departments) {
        User user = new User();
        user.setOrgId(orgId);
        user.setOldRoleId(roles == null ? null : roles.stream().map(Role::getId).map(Objects::toString).collect(Collectors.joining(",")));
        user.setEmail(inviteInfo.getEmail());
        user.setTruename(inviteInfo.getTruename());
        user.setEmployeeNo(inviteInfo.getEmployeeNo());
        if (StringUtils.isNotEmpty(inviteInfo.getPassword())) {
            user.setPassword(PasswordHelper.encrypt(inviteInfo.getPassword()));
            user.setPasswordStrength(PasswordHelper.passwordStrength(inviteInfo.getPassword()));
        }
        List<Long> departmentIds = departments == null ? null : departments.stream().map(Department::getId).collect(Collectors.toList());
        if (departmentIds != null) {
            user.formatDepartmentIds(departmentIds);
        }
        user.setIsAdmin(false);
        user.setStatus(inviteInfo.userStatus());
        user.setAvailableSystems("{\"login_surveyplus\":0,\"login_cem\":1}");
        user.setIsFinishedGuide("N");
        int currentTime = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        user.setCreated(currentTime);
        user.setUpdated(currentTime);
        userRepository.save(user);
        return user;
    }


    @Transactional
    public void updateUserByActiveInvite(User user, String truename, String password) {
        user.setTruename(truename);
        user.setPassword(PasswordHelper.encrypt(password));
        user.setPasswordStrength(PasswordHelper.passwordStrength(password));
        user.setStatus(ENABLE.getStatus());
        userRepository.save(user);
    }

    @Transactional
    public void updateUserDepartment(List<Long> userIds, Long departmentId) {
        userRepository.findByIdIn(userIds).stream().peek(i -> {
            if (i.getIsAdmin() != null && i.getIsAdmin()) {
                throw new BadRequestException("超级管理员不能修改部门");
            }
        }).forEach(user -> {
            user.appendDepartmentId(departmentId);
            userRepository.save(user);
        });
    }

    @Transactional
    public boolean updateUserInfo(Long userId, UserUpdateInfoRequestDto dto) {
        User user = require(userId);
        // check email
        if (StringUtils.isEmpty(dto.getEmail())) {
            user.setEmail(null);
        } else if (!dto.getEmail().equals(user.getEmail())) {
            if (isUniqueInAllOrg("email", dto.getEmail(), user)) {
                user.setEmail(dto.getEmail());
            } else {
                throw new BadRequestException("该邮箱已存在账号，请更换");
            }
        }
        // check employeeNo
        if (StringUtils.isEmpty(dto.getEmployeeNo())) {
            user.setEmployeeNo(null);
        } else if (!dto.getEmployeeNo().equals(user.getEmployeeNo())) {
            if (isUniqueInOrg(user.getOrgId(), "employeeNo", dto.getEmployeeNo(), user)) {
                user.setEmployeeNo(dto.getEmployeeNo());
            } else {
                throw new BadRequestException("该员工号已存在，请更换");
            }
        }
        user.setTruename(dto.getTruename());
        return updateUserDepartmentAndRole(user, dto.getDepartmentIds(), dto.getRoleIds());
    }

    @Transactional
    public boolean updateUserDepartmentAndRole(Long userId, List<Long> departmentIds, List<Long> roleIds) {
        User user = require(userId);
        return updateUserDepartmentAndRole(user, departmentIds, roleIds);
    }

    private boolean updateUserDepartmentAndRole(User user, List<Long> departmentIds, List<Long> roleIds) {
        user.setOldRoleId(roleIds == null ? null : roleIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
        user.formatDepartmentIds(departmentIds);
        userRepository.save(user);
        userRoleService.saveUserRole(user.getId(), roleIds);
        return true;
    }

    @Transactional
    public boolean updateUserRole(Long userId, UserRoleRequestDto dto) {
        User user = require(userId);
        List<Long> roleIds = StringHelper.toLongListOrEmpty(dto.getRoleIds());
        user.setOldRoleId(dto.getRoleIds());
        repository.save(user);
        return userRoleService.saveUserRole(user.getId(), roleIds);
    }

    public User requireCurrent() {
        Long userId = TenantContext.getCurrentUserId();
        User current = null;
        if (userId != null) {
            current = userRepository.findById(userId).orElse(null);
        }
        if (current == null) {
            throw new BadRequestException("用户不存在");
        }
        return current;
    }

    public SimpleUser requireCurrentSimple() {
        Long userId = TenantContext.getCurrentUserId();
        SimpleUser current = null;
        if (userId != null) {
            current = userRepository.findSimpleById(userId);
        }
        if (current == null) {
            throw new BadRequestException("用户不存在");
        }
        return current;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(@Valid UserChangePasswordRequestDto dto) {
        dto.confirmPassword(passwordHelper::decryptRsa);
        User current = requireCurrent();
        if (PasswordHelper.verify(dto.getOldPassword(), current.getPassword())) {
            current.setPassword(PasswordHelper.encrypt(dto.getNewPassword()));
            current.setPasswordStrength(PasswordHelper.passwordStrength(dto.getNewPassword()));
            userRepository.save(current);
        } else {
            throw new BadRequestException("原密码不正确");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(@Valid UserChangeInfoRequestDto dto) {
        User current = requireCurrent();
        if (dto.getTruename() != null) {
            current.setTruename(dto.getTruename());
        }
        if (dto.getAvatar() != null) {
            current.setAvatar(dto.getAvatar());
        }
        userRepository.save(current);
        return true;
    }

    public boolean updateOrganization(SimpleOrganization dto) {
        Organization org = organizationService.getCurrent();
        Optional.ofNullable(org).ifPresent(o -> {
            updateSimpleOrgConfig(o, dto);
            authEventTrigger.updateOrgName(o.getId(), TenantContext.getCurrentUserId(), o.getName(), o.getCode());
        });
        return true;
    }

    private void updateSimpleOrgConfig(Organization org, SimpleOrganization simpleOrganization) {

        if (null != simpleOrganization.getName()) {
            org.setName(simpleOrganization.getName());
            organizationService.save(org);
        }

        OrgConfigDto config = Optional.ofNullable(organizationConfigService.getConfig(org.getId(), OrganizationConfigType.baseInfo)).orElseGet(() -> new OrgConfigDto(new OrgConfigBaseInfoDto()));
        OrgConfigBaseInfoDto baseInfo = config.getBaseInfo();
        if (null != simpleOrganization.getLogo()) {
            baseInfo.setLogo(simpleOrganization.getLogo());
            organizationConfigService.saveConfig(OrganizationConfigType.baseInfo, config);
        }
    }


    public UserInfoResponseDto currentUserInfo() {
        User current = requireCurrent();
        Organization org = organizationService.getById(current.getOrgId()).orElse(null);
        if (org != null) {
            UserInfoResponseDto dto = new UserInfoResponseDto();
            dto.setOrganization(organizationConfigService.getSimpleOrgInfo(org));
            dto.setAvatar(current.getAvatar());
            dto.setUsername(current.getUsername());
            dto.setTruename(current.getTruename());
            dto.setMobile(current.getMobile());
            dto.setEmail(current.getEmail());
            Optional.ofNullable(departmentService.getByIds(current.parseDepartmentIds())).ifPresent(i -> {
                String departmentNames = i.stream().map(Department::getTitle).collect(Collectors.joining(","));
                dto.setDepartmentName(departmentNames);
            });
            dto.setRoleNames(roleService.getRoleNamesByUserId(current.getId()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            if (org.getAvailableDateBegin() != null) {
                dto.setAvailableDateStart(sdf.format(org.getAvailableDateBegin()));
            }
            if (org.getAvailableDateEnd() != null) {
                dto.setAvailableDateEnd(sdf.format(org.getAvailableDateEnd()));
            }
            dto.setIsAdmin(roleService.hasSuperAdminRole(current.getId()) ? 1 : 0);
            dto.setPasswordStrength(current.getPasswordStrength());
            dto.setWalletSms(smsAccountService.balance(org.getId()));
            dto.setWalletMoney(organizationWalletService.balance(org.getId()));
            dto.setWalletAiPoint(organizationAIPointService.balance(org.getId()));
            AppVersion appVersion = organizationService.parseOrgVersion2(org);
            dto.setCemVersion(appVersion.getText());
            OrganizationOptionalLimitDto limit = organizationService.parseOrgOptionalLimit(org);
            dto.setMaxMembers(limit.getChildUserLimit() == null ? 0 : limit.getChildUserLimit());
            return dto;
        }
        return null;
    }

    public PermissionResponseDto getPermissions() {
        Long userId = TenantContext.getCurrentUserId();
        return new PermissionResponseDto(roleService.getMenusByUserId(userId));
    }

    public User initUser(RegisterInfoDto registerInfoDto, Long orgId, Long roleId, Department department, AppVersion cemVersion, boolean isAdmin) throws JsonProcessingException {

        boolean loginCem = !AppVersion.EMPTY.equals(cemVersion);
        LoginAppDto loginAppDto = loginCem ? new LoginAppDto(1, 0) : new LoginAppDto(0, 0);

        User user = User.builder()
                .password(PasswordHelper.encrypt(registerInfoDto.getPassword()))
                .oldRoleId(roleId == null ? null : String.valueOf(roleId))
                .truename(registerInfoDto.getName())
                .mobile(registerInfoDto.getMobile())
                .email(registerInfoDto.getEmail())
                .status(UserStatus.ENABLE.getStatus())
                .availableSystems(JsonHelper.toJson(loginAppDto))
                .isAdmin(isAdmin)
                .isDelete(0)
                .passwordStrength(PasswordHelper.passwordStrength(registerInfoDto.getPassword()))
                .build();
        user.appendDepartmentId(department.getId());
        user.setOrgId(orgId);
        return userRepository.save(user);
    }

    public User initUserWithIp(RegisterInfoDto registerInfoDto, Long orgId, Long roleId, Department department, AppVersion cemVersion, boolean isAdmin) throws JsonProcessingException {

        boolean loginCem = !AppVersion.EMPTY.equals(cemVersion);
        LoginAppDto loginAppDto = loginCem ? new LoginAppDto(1, 0) : new LoginAppDto(0, 0);

        // 获取当前请求的request
        var servletServerHttpRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = RestUtils.getClientIpAddress(servletServerHttpRequest);
        RegionInfo regionInfo = ipResolverService.resolveIpToRegion(ip);

        User user = User.builder()
                .password(PasswordHelper.encrypt(registerInfoDto.getPassword()))
                .oldRoleId(roleId == null ? null : String.valueOf(roleId))
                .truename(registerInfoDto.getName())
                .mobile(registerInfoDto.getMobile())
                .email(registerInfoDto.getEmail())
                .status(UserStatus.ENABLE.getStatus())
                .availableSystems(JsonHelper.toJson(loginAppDto))
                .isAdmin(isAdmin)
                .isDelete(0)
                .passwordStrength(PasswordHelper.passwordStrength(registerInfoDto.getPassword()))
                .ip(ip)
                .country(regionInfo.getCountry())
                .province(regionInfo.getProvince())
                .city(regionInfo.getCity())
                .oldSurveyPlusUser(0)
                .build();
        user.setOrgId(orgId);
        user.appendDepartmentId(department.getId());
        return userRepository.save(user);
    }

    public boolean hasSuperAdminRole(Long userId) {
        return hasSuperAdminRole(get(userId));
    }

    public boolean hasSuperAdminRole(User user) {
        if (user == null) {
            return false;
        }
        if (user.getIsAdmin() != null && user.getIsAdmin()) {
            return true;
        }
        return roleService.hasSuperAdminRole(user.getId());
    }

    /**
     * ctm 调用了此方法
     */
    public boolean currentHasSuperAdminRole() {
        SimpleUser user = requireCurrentSimple();
        if (user.getIsAdmin() != null && user.getIsAdmin()) {
            return true;
        }
        return roleService.hasSuperAdminRole(user.getId());
    }

    /**
     * 用户指导
     */
    @SneakyThrows
    public Set<UserGuideIndoType> updateUserGuideInfo(UserGuideInfoRequestDto type) {
        User user = requireCurrent();
        Set<UserGuideIndoType> guideInfo = UserGuideIndoType.parse(user.getGuideInfo());
        guideInfo.add(type.getType());
        user.setGuideInfo(UserGuideIndoType.format(guideInfo));
        userRepository.save(user);
        return guideInfo;
    }

    public SimpleUser requireCurrentOrgUser() {
        Organization org = organizationService.requireCurrent();
        return requireSimple(org.getOwnerId());
    }

    public List<SimpleUser> queryTruename(String truename) {
        return userRepository.findByTruename(truename);
    }

    public SimpleUser getAdminUser(Long orgId) {
        Optional<Organization> org = organizationService.getById(orgId);
        if (org.isPresent()) {
            Long userId = org.get().getOwnerId();
            if (userId != null) {
                return getSimple(userId).orElse(null);
            }
        }
        return null;
    }

    public Boolean changeOwnerResources(ChangeOwnerResourcesDto dto) {
        dto.getTypes().forEach(type -> {
            type.buildChangeOwnerSql(dto.getFromUserIds(), dto.getTargetUserId()).forEach(sql -> jdbcTemplate.update(sql));
        });
        return true;
    }

    public void syncDepartment(MultipartFile file) {
        try {
            ExcelParserHelper.parseExcel(file);
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }
}
