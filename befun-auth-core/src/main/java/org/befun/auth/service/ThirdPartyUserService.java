package org.befun.auth.service;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.ThirdPartyUserDto;
import org.befun.auth.provider.wechat.work.WeChatWorkAccountHelper;
import org.befun.auth.repository.ThirdPartyUserRepository;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ThirdPartyUserService extends BaseService<ThirdPartyUser, ThirdPartyUserDto, ThirdPartyUserRepository> {

    @Autowired
    private ThirdPartyUserRepository thirdPartyUserRepository;
    @Lazy
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Lazy
    @Autowired
    private WeChatWorkAccountHelper weChatWorkAccountHelper;

    @Override
    public Page<ThirdPartyUserDto> findAll(ResourceEntityQueryDto<ThirdPartyUserDto> queryDto) {
        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();
        Optional.ofNullable(queryDto.getQueryCriteriaList()).ifPresent(i -> {
            i.add(new ResourceQueryCriteria("userId", userId));
            i.add(new ResourceQueryCriteria("orgId", orgId));
        });
        return super.findAll(queryDto);
    }

    public ThirdPartyUser getBySourceAppOpenId(String source, String app, String openId) {
        return thirdPartyUserRepository.findFirstByAppAndSourceAndOpenId(app, source, openId);
    }

    public ThirdPartyUser getByOrgSourceAppOpenId(Long orgId, String source, String app, String openId) {
        return thirdPartyUserRepository.findFirstByOrgIdAndAppAndSourceAndOpenId(orgId, app, source, openId);
    }

    public ThirdPartyUser add(Long orgId, Long userId, String source, String app, String openId, String name, boolean isValid) {
        ThirdPartyUser thirdPartyUser = new ThirdPartyUser();
        thirdPartyUser.setOrgId(orgId);
        thirdPartyUser.setUserId(userId);
        thirdPartyUser.setSource(source);
        thirdPartyUser.setApp(app);
        thirdPartyUser.setOpenId(openId);
        thirdPartyUser.setNickname(name);
        thirdPartyUser.setValid(isValid);
        thirdPartyUserRepository.save(thirdPartyUser);
        return thirdPartyUser;
    }

    public ThirdPartyUser valid(ThirdPartyUser thirdPartyUser) {
        thirdPartyUser.setValid(true);
        thirdPartyUserRepository.save(thirdPartyUser);
        weChatWorkAccountHelper.bindMember(thirdPartyUser);
        return thirdPartyUser;
    }

    public void validWechatWorkMember(Long orgId, Long userId) {
        String app = "cem";
        ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(orgId, ThirdPartyAuthType.WECHAT_WORK, app);
        if (auth != null) {
            ThirdPartyUser thirdPartyUser = getByUserSourceApp(userId, auth.getSource(), app);
            if (thirdPartyUser != null) {
                thirdPartyUser.setValid(true);
                thirdPartyUserRepository.save(thirdPartyUser);
                weChatWorkAccountHelper.bindMember(thirdPartyUser);
            }
        }
    }

    /**
     * 用户删除了，则把与之关联的绑定关系都删除
     */
    public void deleteByUser(Long userId) {
        if (userId != null && userId > 0) {
            List<ThirdPartyUser> users = thirdPartyUserRepository.findAllByUserId(userId);
            if (CollectionUtils.isNotEmpty(users)) {
                thirdPartyUserRepository.deleteAll(users);
            }
        }
    }

    /**
     * 用户授权渠道，则把与之关联的绑定关系都删除
     */
    public void deleteByAuth(Long orgId, String source, String app) {
        if (orgId != null && orgId > 0 && StringUtils.isNotEmpty(source) && StringUtils.isNotEmpty(app)) {
            List<ThirdPartyUser> users = thirdPartyUserRepository.findAllByOrgIdAndAppAndSource(orgId, app, source);
            if (CollectionUtils.isNotEmpty(users)) {
                thirdPartyUserRepository.deleteAll(users);
            }
        }
    }

    public ThirdPartyUser getByUserSourceApp(Long userId, String source, String app) {
        return thirdPartyUserRepository.findFirstByAppAndSourceAndUserId(app, source, userId).orElse(null);
    }

}
