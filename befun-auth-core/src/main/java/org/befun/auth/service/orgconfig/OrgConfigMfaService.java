package org.befun.auth.service.orgconfig;

import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.OrganizationConfigMfaType;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrgConfigMfaService implements BaseOrgConfigService {

    @Autowired
    private AuthProperties authProperties;


    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.mfa;
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        config.setMfa(OrganizationConfigMfaType.NONE);
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {

    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        return null;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setMfa(source.getMfa());
    }
}
