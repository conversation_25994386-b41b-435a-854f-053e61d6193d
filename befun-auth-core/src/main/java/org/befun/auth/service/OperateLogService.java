package org.befun.auth.service;

import org.apache.commons.lang3.ObjectUtils;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.entity.UserDto;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.PermissionException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.ListHelper;
import org.befun.extension.entity.OperateLog;
import org.befun.extension.entity.OperateLogDto;
import org.befun.extension.service.AbstractOperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OperateLogService extends AbstractOperateLogService {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserService userService;

    @Override
    public List<OperateLogDto> mapToDto(List<OperateLog> entity) {
        var operateLogs = super.mapToDto(entity);

        Map<Long/*id*/, List<Long>/*departmentIds*/> userDepartmentIdsMap = new HashMap<>();
        var departmentIds = entity.stream().flatMap(e -> {
            List<Long> ds = ListHelper.parseDepartmentIds(e.getDepartmentIds());
            userDepartmentIdsMap.put(e.getId(), ds);
            return ds.stream();
        }).distinct().collect(Collectors.toList());
        var departmentIdMap = departmentService.getByIds(departmentIds).stream().collect(Collectors.toMap(d -> d.getId(), d -> d.getTitle()));

        var userIds = entity.stream().map(e -> e.getUserId()).collect(Collectors.toSet());
        var userIdMap = userService.getSimpleMapByIds(userIds);

        operateLogs.forEach(operateLogDto -> {
            var e = operateLogDto.getEntity();
            Optional.ofNullable(userDepartmentIdsMap.get(operateLogDto.getId())).ifPresent(l -> {
                String departmentNames = l.stream().map(departmentIdMap::get).filter(Objects::nonNull).collect(Collectors.joining(","));
                operateLogDto.setDepartmentName(departmentNames);
            });
            var user = userIdMap.get(e.getUserId());
            if (ObjectUtils.isNotEmpty(user)) {
                operateLogDto.setUserName(user.getTruename());
                operateLogDto.setAvatar(user.getAvatar());
            }

            var location = new ArrayList<String>() {{
                add(e.getProvince());
                add(e.getCity());
            }}.stream().filter(l -> !"内网IP".equals(l)).distinct().collect(Collectors.joining("/"));

            operateLogDto.setLocation(location);

        });
        return operateLogs;
    }

    @Override
    public Page<OperateLogDto> findAll(ResourceEntityQueryDto<OperateLogDto> queryDto) {
        if (!TenantContext.getCurrentIsAdmin() && !TenantContext.getCurrentPermissions().getAction().contains(PermissionPath.LOG_MANAGE_VIEW)) {
            throw new PermissionException("无权限，请联系管理员");
        }
        queryDto.getQueryCriteriaList().forEach(c -> {
            if ("userName".equals(c.getKey()) && c.getValue() != null) {
                c.setKey("userId");
                c.setParamKey("userId");
                var userIds = userService.getAllUsersInOrg(TenantContext.getCurrentTenant()).stream().filter(u -> Objects.toString(u.getTruename(), "").contains(c.getValue().toString())).map(UserDto::getId).collect(Collectors.toList());
                c.setValue(userIds);
                c.setOperator(QueryOperator.IN);
            }
        });

        queryDto.getQueryCriteriaList().add(new ResourceQueryCriteria("userId", null, QueryOperator.NOT_NULL));
        return super.findAll(queryDto);
    }
}
