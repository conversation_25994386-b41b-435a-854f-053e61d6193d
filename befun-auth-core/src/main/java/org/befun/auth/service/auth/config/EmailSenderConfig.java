package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class EmailSenderConfig extends AbstractConfig implements IClearConfigSecret {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用", required = true)
    private boolean enable;

    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "host", required = true)
    private String host;

    @NotNull
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "port", required = true)
    private Integer port;

    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "username", required = true)
    private String username;

    @Schema(hidden = true)
    private String password;

    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "加密密码", required = true)
    private String encryptedPassword;

    @NotEmpty
    @Email
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "发送人邮箱")
    private String from;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "发送人名称")
    private String personal;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用ssl", required = true)
    private Boolean enableSSL;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "额外的邮件发送配置", required = true)
    private List<String> configs = new ArrayList<>();


    @Override
    public void clearSecret() {
        password = null;
    }
}

