package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class WechatWorkConfig extends AbstractConfig implements IClearConfigSecret {

    @JsonView(ResourceViews.Basic.class)
    private String name;
    @JsonView(ResourceViews.Basic.class)
    private String logo;

    @Schema(hidden = true)
    private String companyId;

    @Schema(hidden = true)
    private String agentId;
    @Schema(hidden = true)
    private String permanentCode;

    @Schema(hidden = true)
    private String visibleUsers;
    @Schema(hidden = true)
    private String visibleDepartments;
    @Schema(hidden = true)
    private String visibleTags;

    @Override
    public void clearSecret() {
        companyId = null;
        agentId = null;
        permanentCode = null;
        visibleUsers = null;
        visibleDepartments = null;
        visibleTags = null;
    }

}
