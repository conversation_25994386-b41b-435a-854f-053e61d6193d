package org.befun.auth.service.userconfig;

import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.userconfig.UserConfigBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigDto;
import org.befun.auth.entity.UserConfig;

import java.util.List;

public interface BaseUserConfigService {

    UserConfigType type();

    UserConfigBuilderDto getConfigBuilder();

    UserConfigDto getConfig(List<UserConfig> configs, boolean useDefault);

    void checkConfig(UserConfigDto data);

    String formatConfig(UserConfigDto data);
}
