package org.befun.auth.service;

import org.befun.auth.dto.IUserInviteInfo;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserInvitation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AsyncService {

    @Autowired
    @Lazy
    private UserInviteService userInviteService;

    /**
     * 发送邀请邮件
     */
    @Async
    public void inviteNotify(Organization org, User fromUser, String app, Map<UserInvitation, User> invitations) {
        invitations.forEach((k, v) -> userInviteService.inviteNotify(org, fromUser, k, v, app, false, false));
    }

    /**
     * 发送邀请邮件
     */
    @Async
    public void bindNotify(String email, String code, String app) {
        userInviteService.bindNotify(email, code, app);
    }

    /**
     * 发送邀请邮件
     */
    @Async
    public void inviteNotifyByUserInfo(Organization org, User fromUser, IUserInviteInfo inviteInfo) {
        userInviteService.inviteNotifyByUserInfo(org, fromUser, inviteInfo);
    }
}
