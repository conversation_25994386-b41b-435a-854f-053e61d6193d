package org.befun.auth.service;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.AuthProviderType;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.provider.wechat.mp.WechatMpAuthProvider;
import org.befun.auth.provider.wechat.work.WechatWorkAuthProvider;
import org.befun.auth.repository.ThirdPartyUserRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.workertrigger.IAuthTaskTrigger;
import org.befun.extension.service.InboxMessageService;
import org.befun.extension.service.MailService;
import org.befun.extension.service.SmsService;
import org.befun.task.service.TaskDelayHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SystemNotificationService {
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ThirdPartyUserRepository thirdPartyUserRepository;

    @Autowired
    private SmsService smsService;

    @Autowired
    private MailService mailService;

    @Autowired
    private WechatMpAuthProvider wechatMpAuthProvider;

    @Autowired
    private WechatWorkAuthProvider wechatWorkAuthProvider;

    @Autowired
    private InboxMessageService inboxMessageService;

    @Autowired
    private TaskDelayHelper taskDelayHelper;

    @Autowired
    private IAuthTaskTrigger authTaskTrigger;

    public boolean notifyToUser(String app, Long userId, NotificationType[] types, String templateName, Map<String, Object> params, boolean ignoreException) {
        log.info("notify to user {} with template {}", userId, templateName);
        Optional<User> user = userRepository.findById(userId);
        if (user.isPresent()) {
            for (int i = 0; i < types.length; i++) {
                NotificationType notificationType = types[i];
                try {
                    switch (notificationType) {
                        case SMS:
                            doSendSms(app, user.get(), templateName, params);
                            break;
                        case WECHAT:
                            doSendWechat(app, user.get(), templateName, params);
                            break;
                        case WECHAT_WORK:
                            doSendWechatWork(app, user.get(), templateName, params);
                            break;
                        case EMAIL:
                            doSendEmail(app, user.get(), templateName, params);
                            break;
                        case INBOX_MESSAGE:
                            doSendInboxMessage(app, user.get(), templateName, params);
                    }
                } catch (Throwable e) {
                    if (!ignoreException) {
                        throw e;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 通知用户
     *
     * @param types
     */
    public boolean notifyToUser(String app, Long userId, NotificationType[] types, String templateName, Map<String, Object> params) {
        log.info("notify to user {} with template {}", userId, templateName);
        return notifyToUser(app, userId, types, templateName, params, false);
    }

    /**
     * @param user
     */
    @Async
    public void doSendSms(String app, User user, String templateName, Map<String, Object> parameters) {
        if (Strings.isNullOrEmpty(user.getMobile())) {
            return;
        }
        String mobile = user.getMobile();
        log.info("send sms to mobile: {}", mobile.substring(mobile.length() - 4));
        List<String> delayTemplateNames = List.of("journey-indicator-warning");
        Duration delay = null;
        if (delayTemplateNames.contains(templateName)) {
            String key = "sms-delay:" + mobile;
            delay = taskDelayHelper.calcAvailableDuration(key, 1, 120);
        }
        if (delay != null) {
            authTaskTrigger.smsNotificationDelay(user.getOrgId(), user.getId(), templateName, mobile, parameters, delay);
        } else {
            smsService.sendMessageByTemplate(templateName, mobile, parameters);
        }
    }

    /**
     * @param user
     */
    @Async
    public void doSendWechat(String app, User user, String templateName, Map<String, Object> parameters) {
        ThirdPartyUser thirdPartyUser = thirdPartyUserRepository.findFirstByAppAndSourceAndUserId(app, AuthProviderType.wechat_mp.name(), user.getId()).orElse(null);
        if (thirdPartyUser != null) {
            try {
                log.info("send wechat notify to user: {} openId: {}", user.getId(), thirdPartyUser.getOpenId());
                wechatMpAuthProvider.notifyUser(app, thirdPartyUser.getOpenId(), templateName, parameters);
            } catch (Exception ex) {
                log.error("failed to send wechat notify to user: {}, openId: {}, due to {}",
                        user.getId(),
                        thirdPartyUser.getOpenId(),
                        ex.getMessage());
            }
        }
    }

    @Async
    public void doSendWechatWork(String app, User user, String templateName, Map<String, Object> parameters) {
        try {
            log.info("send wechat work notify to user: {}", user.getId());
            wechatWorkAuthProvider.sendTemplateMessage(user.getOrgId(), app, user.getId(), templateName, parameters);
        } catch (Exception ex) {
            log.error("failed to send wechat work notify to user: {}, error={}", user.getId(), ex.getMessage());
        }
    }

    /**
     * @param user
     */
    @Async
    public void doSendEmail(String app, User user, String templateName, Map<String, Object> parameters) {
        if (Strings.isNullOrEmpty(user.getEmail())) {
            return;
        }
        try {
            String email = user.getEmail();
            log.info("send email notify to user: {} email: {}", user.getId(), email);
            mailService.sendMessageByTemplate(templateName, email, parameters);
        } catch (Exception ex) {
            log.error("failed to send email notify to user: {}, error={}", user.getId(), ex.getMessage());
        }

    }

    /**
     * @param user
     */
    @Async
    public void doSendInboxMessage(String app, User user, String templateName, Map<String, Object> parameters) {
        try {
            inboxMessageService.addInboxMessage(user.getOrgId(), user.getId(), templateName,parameters);
            log.info("add inbox message notify to user: {}", user.getId());
        } catch (Exception ex) {
            log.error("add inbox message notify to user: {}, error={}", user.getId(), ex.getMessage());
        }

    }
}
