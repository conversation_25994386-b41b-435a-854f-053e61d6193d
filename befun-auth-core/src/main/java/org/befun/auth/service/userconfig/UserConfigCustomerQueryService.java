package org.befun.auth.service.userconfig;

import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.configuration.UserConfigProperties;
import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.userconfig.UserConfigBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigCustomerQueryBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigCustomerQueryDto;
import org.befun.auth.dto.userconfig.UserConfigDto;
import org.befun.auth.entity.UserConfig;
import org.befun.auth.service.orgconfig.OrgConfigExtendCustomerFieldService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UserConfigCustomerQueryService implements BaseUserConfigService {

    @Autowired
    private UserConfigProperties userConfigProperties;
    @Autowired
    private OrgConfigExtendCustomerFieldService orgConfigExtendCustomerFieldService;

    private List<UserConfigCustomerQueryBuilderDto> getBuilder() {
        Function<String, List<UserConfigCustomerQueryBuilderDto.UserConfigCustomerQueryBuilderItemDto>> getItems = t ->
                userConfigProperties.getCustomerQueryType().getOrDefault(t, List.of())
                        .stream()
                        .map(j -> new UserConfigCustomerQueryBuilderDto.UserConfigCustomerQueryBuilderItemDto(
                                j.getQueryTypeLabel(),
                                j.getQueryType(),
                                j.getQueryValueType()
                        ))
                        .collect(Collectors.toList());

        List<UserConfigCustomerQueryBuilderDto> builder = userConfigProperties.getCustomerQuery()
                .stream().map(i -> {
                    UserConfigCustomerQueryBuilderDto dto = new UserConfigCustomerQueryBuilderDto();
                    dto.setPropertyLabel(i.getPropertyLabel());
                    dto.setPropertyName(i.getPropertyName());
                    dto.setPropertySource(i.getPropertySource());
                    dto.setPropertyColumn(i.getPropertyColumn());
                    dto.setPropertyType(i.getPropertyType());
                    dto.setEnableBatchUpdate(i.isEnableBatchUpdate());
                    dto.setInputType(i.getInputType());
                    dto.setPropertyValueOptions(new ArrayList<>());
                    dto.setQueryItems(getItems.apply(i.getQueryItemType()));
                    return dto;
                })
                .collect(Collectors.toList());
        orgConfigExtendCustomerFieldService.extendFields().forEach(i -> {
            String queryType = userConfigProperties.getCustomerQueryExtendField().get(i.getType().name());
            List<UserConfigCustomerQueryBuilderDto.UserConfigCustomerQueryBuilderItemDto> items = getItems.apply(queryType);
            if (CollectionUtils.isNotEmpty(items)) {
                UserConfigCustomerQueryBuilderDto dto = new UserConfigCustomerQueryBuilderDto();
                dto.setPropertyName(i.getProp());
                dto.setPropertyLabel(i.getLabel());
                dto.setPropertySource("extend");
                dto.setPropertyType("string");
                dto.setInputType("none");
                dto.setEnableBatchUpdate(true);
                dto.setExtendField(i);
                dto.setQueryItems(items);
                dto.setPropertyValueOptions(i.getOptions()
                        .stream()
                        .map(j -> new UserConfigCustomerQueryBuilderDto.UserConfigCustomerQueryBuilderValueOptionDto(j.getValue(), j.getLabel()))
                        .collect(Collectors.toList()));
                builder.add(dto);
            }
        });
        return builder;
    }

    @Override
    public UserConfigType type() {
        return UserConfigType.customerQuery;
    }

    @Override
    public UserConfigBuilderDto getConfigBuilder() {
        UserConfigBuilderDto dto = new UserConfigBuilderDto();
        dto.setCustomerQuery(getBuilder());
        return dto;
    }

    @Override
    public UserConfigDto getConfig(List<UserConfig> configs, boolean useDefault) {
        UserConfigDto dto = new UserConfigDto();
        dto.setCustomerQuery(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(configs)) {
            configs.forEach(i -> {
                Optional.ofNullable(JsonHelper.toObject(i.getConfig(), UserConfigCustomerQueryDto.class)).ifPresent(j -> {
                    j.setConfigId(i.getId());
                    dto.getCustomerQuery().add(j);
                });
            });
        }
        return dto;
    }

    @Override
    public void checkConfig(UserConfigDto data) {
        UserConfigCustomerQueryDto query = getFirst(data);
        if (query == null) {
            throw new BadRequestException();
        }
    }

    private UserConfigCustomerQueryDto getFirst(UserConfigDto data) {
        if (data != null && CollectionUtils.isNotEmpty(data.getCustomerQuery())) {
            return data.getCustomerQuery().get(0);
        }
        return null;
    }

    @Override
    public String formatConfig(UserConfigDto data) {
        UserConfigCustomerQueryDto query = getFirst(data);
        return JsonHelper.toJson(true, query);
    }
}
