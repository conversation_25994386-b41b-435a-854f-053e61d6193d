package org.befun.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.dto.permission.ResourcePermissionWrapDto;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.Role;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.utils.ListHelper;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.dto.ResourcePermissionDepartmentDto;
import org.befun.core.dto.ResourcePermissionPartDto;
import org.befun.core.dto.ResourcePermissionRoleDto;
import org.befun.core.dto.ResourcePermissionUserDto;
import org.befun.core.entity.ResourcePermission;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.PermissionException;
import org.befun.core.repo.ResourcePermissionRepository;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.IResourceInfoService;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.befun.auth.constant.ResourcePermissionRelationType.*;
import static org.befun.auth.service.ResourcePermissionService.ChangeFlag.*;

@Slf4j
@Service
public class ResourcePermissionService implements IResourceInfoService {

    @Autowired
    private ResourcePermissionRepository repository;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IAuthEventTrigger authEventTrigger;

    private <T> List<T> resourcePermissions(long orgId, long resourceId, ResourcePermissionType resourceType,
                                            Predicate<ResourcePermission> filter,
                                            Function<ResourcePermission, T> map) {
        List<T> r = new ArrayList<>();
        List<ResourcePermission> permissions = repository.findAllByResourceTypeAndResourceId(resourceType.name(), resourceId);
        if (CollectionUtils.isNotEmpty(permissions)) {
            permissions.stream().filter(i -> i.getOrgId() != null && orgId == i.getOrgId() && filter.test(i)).forEach(i -> Optional.ofNullable(map.apply(i)).ifPresent(r::add));
        }
        return r;
    }

    private void checkUserPermission(long resourceId, ResourcePermissionType resourceType, ResourcePermissionRelationType atLeast) {
        if (!resourceType.isCheckRelation()) {
            return;//不需要校验
        }
        Boolean isAdmin = TenantContext.getCurrentIsAdmin();
        if (isAdmin != null && isAdmin) {
            return;//是 超级管理员
        }
        ResourcePermissionRelationType relation = calculateCurrentRelations(resourceId, resourceType);
        if (relation.atLeast(atLeast)) {
            return; //有数据的编辑权限
        }
        throw new PermissionException("无权限，请联系管理员");
    }


    private <T extends ResourcePermissionPartDto> void checkRelation(ResourcePermissionType resourceType, List<T> parts) {
        if (resourceType.isCheckRelation()) {
            Optional.ofNullable(parts).ifPresent(l -> l.forEach(i -> {
                ResourcePermissionRelationType relation = ResourcePermissionRelationType.parse(i.getType());
                if (resourceType.getRelations().contains(relation)) {
                    i.setType(relation.name());
                } else {
                    String partDesc = "";
                    if (i instanceof ResourcePermissionUserDto) {
                        partDesc = "用户";
                    } else if (i instanceof ResourcePermissionRoleDto) {
                        partDesc = "角色";
                    } else if (i instanceof ResourcePermissionDepartmentDto) {
                        partDesc = "部门";
                    }
                    throw new BadRequestException(String.format("%s(%s)的权限类型错误", partDesc, i.getName()));
                }
            }));
        }
    }

    /**
     * 如果数据类型设置了权限类型范围，则需要过滤掉不在权限类型范围之内的数据
     */
    private <T extends ResourcePermissionPartDto> List<T> filterRelation(ResourcePermissionType resourceType, List<T> parts) {
        if (resourceType.isCheckRelation()) {
            List<T> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(parts)) {
                for (T part : parts) {
                    ResourcePermissionRelationType relation = ResourcePermissionRelationType.parse(part.getType());
                    if (resourceType.getRelations().contains(relation)) {
                        part.setType(relation.name());
                        result.add(part);
                    }
                }
            }
            return result;
        } else {
            return parts == null ? new ArrayList<>() : parts;
        }
    }

    private <X extends ResourcePermissionPartDto> List<ResourcePermission> mapToEntity(Long orgId, long resourceId, ResourcePermissionType resourceType, List<X> permissions) {
        return permissions.stream().map(i -> {
            ResourcePermission rp = new ResourcePermission();
            rp.setResourceId(resourceId);
            rp.setResourceType(resourceType.name());
            rp.setRelationType(resourceType.emptyIsDefault(i.getType()));
            rp.setOrgId(orgId);
            if (i instanceof ResourcePermissionUserDto) {
                rp.setUserId(i.getId());
            } else if (i instanceof ResourcePermissionRoleDto) {
                rp.setRoleId(i.getId());
            } else if (i instanceof ResourcePermissionDepartmentDto) {
                rp.setDepartmentId(i.getId());
            }
            return rp;
        }).collect(Collectors.toList());
    }

    private <X extends ResourcePermissionPartDto> boolean calculateChangeAndSave(ChangeFlag flag, long resourceId, ResourcePermissionType resourceType, List<X> permissions, Function<ResourcePermission, Long> getId) {
        Long orgId = TenantContext.getCurrentTenant();
        List<ResourcePermission> oldList = resourcePermissions(orgId, resourceId, resourceType, i -> getId.apply(i) != null, Function.identity());
        List<ResourcePermission> newList = mapToEntity(orgId, resourceId, resourceType, permissions);

        List<ResourcePermission> add = null;
        List<ResourcePermission> update = null;
        List<ResourcePermission> del = null;
        if (flag == SYNC) {
            // 计算新增的，旧列表不存在，新列表存在
            add = ListHelper.minus(newList, oldList, getId::apply);
            // 计算移除的，旧列表存在，新列表不存在
            del = ListHelper.minus(oldList, newList, getId::apply);
            // 计算修改的，旧列表存在，新列表存在
            update = ListHelper.contain(oldList, newList, getId::apply, (o, n) -> {
                o.setRelationType(n.getRelationType());
                return o;
            });
        } else if (flag == ADD) {
            // 计算新增的，旧列表不存在，新列表存在
            add = ListHelper.minus(newList, oldList, getId::apply);
        } else if (flag == UPDATE) {
            // 计算修改的，旧列表存在，新列表存在
            update = ListHelper.contain(oldList, newList, getId::apply, (o, n) -> {
                o.setRelationType(n.getRelationType());
                return o;
            });
        } else if (flag == REMOVE) {
            // 计算移除的，旧列表存在，新列表不存在
            del = ListHelper.contain(oldList, newList, getId::apply, (o, n) -> o);
        }
        boolean change = false;
        if (CollectionUtils.isNotEmpty(del)) {
            change = true;
            repository.deleteAll(del);
        }
        if (CollectionUtils.isNotEmpty(add)) {
            change = true;
            repository.saveAll(add);
        }
        if (CollectionUtils.isNotEmpty(update)) {
            change = true;
            repository.saveAll(update);
        }
        return change;
    }

    //****************************************************************************
    //************************************ all ***********************************
    //****************************************************************************

    public List<ResourcePermissionUserDto> allUsers(long resourceId, ResourcePermissionType resourceType) {
        checkUserPermission(resourceId, resourceType, MEMBER);
        List<ResourcePermissionUserDto> users = new ArrayList<>();
        all(resourceId, resourceType, users, null, null);
        return users;
    }

    public List<ResourcePermissionRoleDto> allRoles(long resourceId, ResourcePermissionType resourceType) {
        checkUserPermission(resourceId, resourceType, MEMBER);
        List<ResourcePermissionRoleDto> roles = new ArrayList<>();
        all(resourceId, resourceType, null, roles, null);
        return roles;
    }

    public ResourcePermissionWrapDto all(long resourceId, ResourcePermissionType resourceType) {
        checkUserPermission(resourceId, resourceType, MEMBER);
        List<ResourcePermissionUserDto> users = new ArrayList<>();
        List<ResourcePermissionRoleDto> roles = new ArrayList<>();
        all(resourceId, resourceType, users, roles, null);
        return new ResourcePermissionWrapDto(users, roles);
    }

    private void all(long resourceId, ResourcePermissionType resourceType,
                     List<ResourcePermissionUserDto> inUsers,
                     List<ResourcePermissionRoleDto> inRoles,
                     List<ResourcePermissionDepartmentDto> inDepartments) {
        Long orgId = TenantContext.getCurrentTenant();
        Map<Long, ResourcePermissionRelationType> userRelationTypeMap = new HashMap<>();
        Map<Long, ResourcePermissionRelationType> roleRelationTypeMap = new HashMap<>();
        resourcePermissions(orgId, resourceId, resourceType, i -> true, i -> {
            if (i.getUserId() != null) {
                if (inUsers != null) {
                    ResourcePermissionRelationType relation = resourceType.emptyRelationIsDefault(i.getRelationType());
                    userRelationTypeMap.put(i.getUserId(), relation);
                }
            } else if (i.getRoleId() != null) {
                if (inRoles != null) {
                    ResourcePermissionRelationType relation = resourceType.emptyRelationIsDefault(i.getRelationType());
                    roleRelationTypeMap.put(i.getRoleId(), relation);
                }
            }
            return null;
        });
        if (inUsers != null) {
            Long ownerId = Optional.ofNullable(getOwnerUserId(resourceId, resourceType)).orElse(0L);
            List<SimpleUser> users = userService.getSimpleByOrgId(orgId);
            Optional.ofNullable(users).ifPresent(i -> i.forEach(user -> {
                if (ownerId.equals(user.getId())) {
                    return;
                }
                ResourcePermissionUserDto permissionUser = mapToUser(user);
                ResourcePermissionRelationType relation = userRelationTypeMap.getOrDefault(user.getId(), NONE);
                permissionUser.setType(relation.name());
                inUsers.add(permissionUser);
            }));
        }
        if (inRoles != null) {
            List<Role> roles = roleService.getByOrgId(orgId);
            Optional.ofNullable(roles).ifPresent(i -> i.forEach(role -> {
                ResourcePermissionRoleDto permissionRole = mapToRole(role);
                ResourcePermissionRelationType relation = roleRelationTypeMap.getOrDefault(role.getId(), NONE);
                permissionRole.setType(relation.name());
                inRoles.add(permissionRole);
            }));
        }
    }

    //****************************************************************************
    //********************************* not exists *******************************
    //****************************************************************************

    public Page<ResourcePermissionUserDto> notExistsUsers(long resourceId, ResourcePermissionType resourceType, String q, int page, int limit) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<Long> existsUserIds = resourcePermissions(orgId, resourceId, resourceType,
                i -> i.getUserId() != null,
                ResourcePermission::getUserId);
        Optional.ofNullable(userId).ifPresent(existsUserIds::add);      //把自己加入排除列表
        Long ownerUserId = getOwnerUserId(resourceId, resourceType);
        Optional.ofNullable(ownerUserId).ifPresent(existsUserIds::add); //把创建人加入排除列表
        String excludeIdString = existsUserIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        SqlBuilder sqlBuilder = SqlBuilder.select("select id, 1 type from user u")
                .countSelect("select count(1) from user u")
                .where("u.org_id = %d", orgId).alwaysTrue()
                .where("u.is_delete=0").alwaysTrue()
                .where("u.id not in (%s)", excludeIdString).ifTrue(StringUtils.isNotEmpty(excludeIdString))
                .where("u.truename like '%%%s%%'", q).ifTrue(StringUtils.isNotEmpty(q))
                .limit(page, limit);
        return nativeSqlHelper.queryPage(sqlBuilder, ResourcePermissionUserDto::getId, (ignore, ids) -> {
            return Optional.ofNullable(userService.getSimpleByIds(new HashSet<>(ids))).map(list -> list.stream().map(this::mapToUser).collect(Collectors.toList())).orElse(new ArrayList<>());
        });
    }

    private ResourcePermissionUserDto mapToUser(SimpleUser user) {
        return new ResourcePermissionUserDto(user.getId(), user.getIsAdmin(), user.getTruename(), user.getAvatar(), user.getEmail(), user.getMobile());
    }

    public Page<ResourcePermissionRoleDto> notExistsRoles(long resourceId, ResourcePermissionType resourceType, String q, int page, int limit) {
        Long orgId = TenantContext.getCurrentTenant();
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<Long> existsRoleIds = resourcePermissions(orgId, resourceId, resourceType,
                i -> i.getRoleId() != null,
                ResourcePermission::getRoleId);
        String excludeIdString = existsRoleIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        SqlBuilder sqlBuilder = SqlBuilder.select("select id, 1 type from role r")
                .countSelect("select count(1) from role r")
                .where("r.org_id = %d", orgId).alwaysTrue()
                .where("r.id not in (%s)", excludeIdString).ifTrue(StringUtils.isNotEmpty(excludeIdString))
                .where("r.name like '%%%s%%'", q).ifTrue(StringUtils.isNotEmpty(q))
                .limit(page, limit);
        return nativeSqlHelper.queryPage(sqlBuilder, ResourcePermissionRoleDto::getId, (ignore, ids) -> {
            return Optional.ofNullable(roleService.getByIds(ids)).map(list -> list.stream().map(this::mapToRole).collect(Collectors.toList())).orElse(new ArrayList<>());
        });
    }

    private ResourcePermissionRoleDto mapToRole(Role role) {
        return new ResourcePermissionRoleDto(role.getId(), role.getName(), null);
    }

    //****************************************************************************
    //*********************************** exists *********************************
    //****************************************************************************

    public ResourcePermissionWrapDto exists(long resourceId, ResourcePermissionType resourceType) {
        checkUserPermission(resourceId, resourceType, MEMBER);
        List<ResourcePermissionUserDto> users = new ArrayList<>();
        List<ResourcePermissionRoleDto> roles = new ArrayList<>();
        getExistsPermissions(resourceId, resourceType, users, roles, null);
        return new ResourcePermissionWrapDto(users, roles);
    }

    public List<ResourcePermissionUserDto> existsUsers(long resourceId, ResourcePermissionType resourceType) {
        checkUserPermission(resourceId, resourceType, MEMBER);
        List<ResourcePermissionUserDto> users = new ArrayList<>();
        getExistsPermissions(resourceId, resourceType, users, null, null);
        return users;
    }

    public List<ResourcePermissionRoleDto> existsRoles(long resourceId, ResourcePermissionType resourceType) {
        checkUserPermission(resourceId, resourceType, MEMBER);
        List<ResourcePermissionRoleDto> roles = new ArrayList<>();
        getExistsPermissions(resourceId, resourceType, null, roles, null);
        return roles;
    }

    //****************************************************************************
    //************************************ sync **********************************
    //****************************************************************************

    // listener user change
    @Transactional
    public ResourcePermissionRelationType sync(long resourceId, ResourcePermissionType resourceType, ResourcePermissionWrapDto data) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, data.getPermissionUsers());
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, data.getPermissionRoles());
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            boolean r1 = calculateChangeAndSave(SYNC, resourceId, resourceType, users, ResourcePermission::getUserId);
            boolean r2 = calculateChangeAndSave(SYNC, resourceId, resourceType, roles, ResourcePermission::getRoleId);
            return r1 || r2;
        });
    }

    // listener user change
    @Transactional
    public ResourcePermissionRelationType syncUsers(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionUserDto> userIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, userIds);
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            return calculateChangeAndSave(SYNC, resourceId, resourceType, users, ResourcePermission::getUserId);
        });
    }

    // listener user change
    @Transactional
    public ResourcePermissionRelationType syncRoles(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionRoleDto> roleIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, roleIds);
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            return calculateChangeAndSave(SYNC, resourceId, resourceType, roles, ResourcePermission::getRoleId);
        });
    }


    //****************************************************************************
    //************************************ add ***********************************
    //****************************************************************************

    // listener user change
    @Transactional
    public ResourcePermissionRelationType add(long resourceId, ResourcePermissionType resourceType, ResourcePermissionWrapDto data) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, data.getPermissionUsers());
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, data.getPermissionRoles());
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            boolean r1 = calculateChangeAndSave(ADD, resourceId, resourceType, users, ResourcePermission::getUserId);
            boolean r2 = calculateChangeAndSave(ADD, resourceId, resourceType, roles, ResourcePermission::getRoleId);
            return r1 || r2;
        });
    }

    // listener user change
    @Transactional
    public ResourcePermissionRelationType addUsers(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionUserDto> userIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, userIds);
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            return calculateChangeAndSave(ADD, resourceId, resourceType, users, ResourcePermission::getUserId);
        });
    }

    // listener user change
    @Transactional
    public ResourcePermissionRelationType addRoles(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionRoleDto> roleIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, roleIds);
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            return calculateChangeAndSave(ADD, resourceId, resourceType, roles, ResourcePermission::getRoleId);
        });
    }


    //****************************************************************************
    //*********************************** remove *********************************
    //****************************************************************************

    // listener user change
    @Transactional
    public ResourcePermissionRelationType remove(long resourceId, ResourcePermissionType resourceType, ResourcePermissionWrapDto data) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, data.getPermissionUsers());
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, data.getPermissionRoles());
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            boolean r1 = calculateChangeAndSave(REMOVE, resourceId, resourceType, users, ResourcePermission::getUserId);
            boolean r2 = calculateChangeAndSave(REMOVE, resourceId, resourceType, roles, ResourcePermission::getRoleId);
            return r1 || r2;
        });
    }

    // listener user change
    @Transactional
    public ResourcePermissionRelationType removeUsers(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionUserDto> userIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            return calculateChangeAndSave(REMOVE, resourceId, resourceType, userIds, ResourcePermission::getUserId);
        });
    }

    // listener user change
    @Transactional
    public ResourcePermissionRelationType removeRoles(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionRoleDto> roleIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        Long orgId = TenantContext.getCurrentTenant();
        return listenerChangeUser(orgId, resourceId, resourceType, () -> {
            return calculateChangeAndSave(REMOVE, resourceId, resourceType, roleIds, ResourcePermission::getRoleId);
        });
    }


    //****************************************************************************
    //*********************************** update *********************************
    //****************************************************************************

    @Transactional
    public ResourcePermissionRelationType update(long resourceId, ResourcePermissionType resourceType, ResourcePermissionWrapDto data) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, data.getPermissionUsers());
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, data.getPermissionRoles());
        calculateChangeAndSave(UPDATE, resourceId, resourceType, users, ResourcePermission::getUserId);
        calculateChangeAndSave(UPDATE, resourceId, resourceType, roles, ResourcePermission::getRoleId);
        return calculateCurrentRelations(resourceId, resourceType);
    }

    @Transactional
    public ResourcePermissionRelationType updateUsers(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionUserDto> userIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionUserDto> users = filterRelation(resourceType, userIds);
        calculateChangeAndSave(UPDATE, resourceId, resourceType, users, ResourcePermission::getUserId);
        return calculateCurrentRelations(resourceId, resourceType);
    }

    @Transactional
    public ResourcePermissionRelationType updateRoles(long resourceId, ResourcePermissionType resourceType, List<ResourcePermissionRoleDto> roleIds) {
        checkUserPermission(resourceId, resourceType, ADMIN);
        List<ResourcePermissionRoleDto> roles = filterRelation(resourceType, roleIds);
        calculateChangeAndSave(UPDATE, resourceId, resourceType, roles, ResourcePermission::getRoleId);
        return calculateCurrentRelations(resourceId, resourceType);
    }

    //****************************************************************************
    //**************************** listener **************************************
    //****************************************************************************
    private ResourcePermissionRelationType listenerChangeUser(Long orgId, Long resourceId, ResourcePermissionType resourceType, Supplier<Boolean> change) {
        List<Long> oldList = getUserIdByResource(orgId, resourceId, resourceType);
        boolean isChange = Optional.ofNullable(change.get()).orElse(true);
        if (!isChange) {
            // 如果没有变动，直接跳过
            return calculateCurrentRelations(resourceId, resourceType);
        }
        List<Long> newList = getUserIdByResource(orgId, resourceId, resourceType);

        List<Long> del = ListHelper.minus(oldList, newList);
        if (CollectionUtils.isNotEmpty(del)) {
            Long ownerUserId = getOwnerUserId(resourceId, resourceType);
            if (ownerUserId != null) {
                del.remove(ownerUserId);
            }
            Long userId = TenantContext.getCurrentUserId();
            if (userId != null && del.contains(userId)) {
                // 进行此操作后会导致自己无权限，此处抛出异常
                throw new BadRequestException("无权限，请联系管理员");
            }
        }
        if (CollectionUtils.isNotEmpty(del)) {
            authEventTrigger.resourcePermissionRemoveUser(orgId, TenantContext.getCurrentUserId(), resourceId, resourceType.name(), del);
        }
        List<Long> add = ListHelper.minus(newList, oldList);
        if (CollectionUtils.isNotEmpty(add)) {
            authEventTrigger.resourcePermissionAddUser(orgId, TenantContext.getCurrentUserId(), resourceId, resourceType.name(), add);
        }

        return calculateCurrentRelations(resourceId, resourceType);
    }

    //****************************************************************************
    //****************************************************************************
    //****************************************************************************


    @Override
    public List<ResourcePermissionUserDto> getPermissionUsers(Set<Long> userIds) {
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<SimpleUser> users = userService.getSimpleByIds(userIds);
            if (CollectionUtils.isNotEmpty(users)) {
                return users.stream().map(i -> {
                    ResourcePermissionUserDto dto = new ResourcePermissionUserDto();
                    dto.setId(i.getId());
                    dto.setEmail(i.getEmail());
                    dto.setTruename(i.getTruename());
                    dto.setAvatar(i.getAvatar());
                    dto.setMobile(i.getMobile());
                    dto.setIsAdmin(i.getIsAdmin());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        return null;
    }

    @Override
    public List<ResourcePermissionRoleDto> getPermissionRoles(Set<Long> roleIds) {
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<Role> users = roleService.getByIds(new ArrayList<>(roleIds));
            if (CollectionUtils.isNotEmpty(users)) {
                return users.stream().map(i -> {
                    ResourcePermissionRoleDto dto = new ResourcePermissionRoleDto();
                    dto.setId(i.getId());
                    dto.setName(i.getName());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        return null;
    }

    @Override
    public List<ResourcePermissionDepartmentDto> getPermissionDepartments(Set<Long> departmentIds) {
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            List<Department> users = departmentService.getByIds(new ArrayList<>(departmentIds));
            if (CollectionUtils.isNotEmpty(users)) {
                return users.stream().map(i -> {
                    ResourcePermissionDepartmentDto dto = new ResourcePermissionDepartmentDto();
                    dto.setId(i.getId());
                    dto.setName(i.getTitle());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        return null;
    }

    public void getExistsPermissions(long resourceId, ResourcePermissionType resourceType,
                                     List<ResourcePermissionUserDto> inUsers,
                                     List<ResourcePermissionRoleDto> inRoles,
                                     List<ResourcePermissionDepartmentDto> inDepartments) {
        Long orgId = TenantContext.getCurrentTenant();
        Map<Long, ResourcePermissionUserDto> userMap = new HashMap<>();
        Map<Long, ResourcePermissionRoleDto> roleMap = new HashMap<>();
        Map<Long, ResourcePermissionDepartmentDto> departmentMap = new HashMap<>();

        // 查询指定数据的全部权限关系
        List<ResourcePermission> permissions = resourcePermissions(orgId, resourceId, resourceType, i -> true, Function.identity());

        if (CollectionUtils.isNotEmpty(permissions)) {
            Set<Long> userIds = new HashSet<>();
            Set<Long> roleIds = new HashSet<>();
            Set<Long> departmentIds = new HashSet<>();
            permissions.forEach(i -> {
                if (i.getUserId() != null && i.getUserId() > 0) {
                    userIds.add(i.getUserId());// 与用户的关系
                }
                if (i.getRoleId() != null && i.getRoleId() > 0) {
                    roleIds.add(i.getRoleId());// 与角色的关系
                }
                if (i.getDepartmentId() != null && i.getDepartmentId() > 0) {
                    departmentIds.add(i.getDepartmentId()); // 与部门的关系
                }
            });
            if (inUsers != null && !userIds.isEmpty()) {
                // 通过有关系的用户id,查询出用户信息
                Optional.ofNullable(getPermissionUsers(userIds)).ifPresent(s -> {
                    s.forEach(i -> userMap.put(i.getId(), i));
                });
            }
            if (inRoles != null && !roleIds.isEmpty()) {
                // 通过有关系的角色id,查询出角色信息
                Optional.ofNullable(getPermissionRoles(roleIds)).ifPresent(s -> {
                    s.forEach(i -> roleMap.put(i.getId(), i));
                });
            }
            if (inDepartments != null && !departmentIds.isEmpty()) {
                // 通过有关系的部门id,查询出部门信息
                Optional.ofNullable(getPermissionDepartments(departmentIds)).ifPresent(s -> {
                    s.forEach(i -> departmentMap.put(i.getId(), i));
                });
            }
            permissions.forEach(i -> {
                if (inUsers != null && i.getUserId() != null && i.getUserId() > 0) {
                    Optional.ofNullable(userMap.get(i.getUserId()))
                            .ifPresent(d -> {
                                d.setType(resourceType.emptyIsDefault(i.getRelationType()));
                                inUsers.add(d);
                            });
                }
                if (inRoles != null && i.getRoleId() != null && i.getRoleId() > 0) {
                    Optional.ofNullable(roleMap.get(i.getRoleId()))
                            .ifPresent(d -> {
                                d.setType(resourceType.emptyIsDefault(i.getRelationType()));
                                inRoles.add(d);
                            });
                }
                if (inDepartments != null && i.getDepartmentId() != null && i.getDepartmentId() > 0) {
                    Optional.ofNullable(departmentMap.get(i.getDepartmentId()))
                            .ifPresent(d -> {
                                d.setType(resourceType.emptyIsDefault(i.getRelationType()));
                                inDepartments.add(d);
                            });
                }
            });
        }
    }


    // 查询当前资源的所有用户（用户，角色的用户，部门的用户）
    public List<Long> getUserIdByResource(Long orgId, Long resourceId, ResourcePermissionType resourceType) {
        List<ResourcePermission> permissions = resourcePermissions(orgId, resourceId, resourceType, i -> true, Function.identity());
        if (CollectionUtils.isNotEmpty(permissions)) {
            List<Long> userIds = new ArrayList<>();
            List<Long> roleIds = new ArrayList<>();
            List<Long> departmentIds = new ArrayList<>();
            permissions.forEach(i -> {
                if (i.getUserId() != null && i.getUserId() > 0) {
                    userIds.add(i.getUserId());
                }
                if (i.getRoleId() != null && i.getRoleId() > 0) {
                    roleIds.add(i.getRoleId());
                }
                if (i.getDepartmentId() != null && i.getDepartmentId() > 0) {
                    departmentIds.add(i.getDepartmentId());
                }
            });
            String users = Stream.concat(Stream.of(-1L), userIds.stream()).map(Objects::toString).collect(Collectors.joining(","));
            String roles = Stream.concat(Stream.of(-1L), roleIds.stream()).map(Objects::toString).collect(Collectors.joining(","));
            String departments = Stream.concat(Stream.of(-1L), departmentIds.stream()).map(Objects::toString).collect(Collectors.joining(","));
            String sql = "select distinct u.id from user u " +
                    " where u.org_id=%d " +
                    " and (" +
                    " u.id in (%s) " +
                    " or u.department_id in (%s) " +
                    " or exists (select 1 from user_role ur where ur.user_id=u.id and ur.role_id in (%s))" +
                    " )";
            sql = String.format(sql, orgId, users, departments, roles);
            log.info("查询资源{}({})的有权限的用户id列表：{}", resourceType.name(), resourceId, sql);
            return jdbcTemplate.queryForList(sql, Long.class);
        }
        return null;
    }

    public Map<Long, ResourcePermissionRelationType> calculateCurrentRelations(List<Long> resourceIds, ResourcePermissionType resourceType) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
//        Long departmentId = TenantContext.getCurrentDepartmentId();
        List<Long> roleIds = TenantContext.getCurrentRoleIds();
        Map<Long, ResourcePermissionRelationType> map = new ConcurrentHashMap<>();
        if (CollectionUtils.isNotEmpty(resourceIds)) {
            resourceIds.stream().parallel().forEach(resourceId -> {
                ResourcePermissionRelationType type = calculateRelations(orgId, userId, null, roleIds, resourceId, resourceType);
                map.put(resourceId, type);
            });
        }
        return map;
    }

    public ResourcePermissionRelationType calculateCurrentRelations(long resourceId, ResourcePermissionType resourceType) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
//        Long departmentId = TenantContext.getCurrentDepartmentId();
        List<Long> roleIds = TenantContext.getCurrentRoleIds();
        return calculateRelations(orgId, userId, null, roleIds, resourceId, resourceType);
    }

    public ResourcePermissionRelationType calculateRelations(Long orgId, Long userId, Long departmentId, List<Long> roleIds, long resourceId, ResourcePermissionType resourceType) {
        if (orgId == null || userId == null) {
            return NONE;
        }
        Long ownerUserId = getOwnerUserId(resourceId, resourceType);
        if (ownerUserId != null && ownerUserId > 0 && ownerUserId.equals(userId)) {
            return OWNER;
        }
        String sql = String.format("select relation_type from resource_permission where org_id=%d and resource_id = %d and resource_type='%s' and user_id = %d", orgId, resourceId, resourceType.name(), userId);
        Optional<ResourcePermissionRelationType> relation = calculateRelations0(sql, resourceType);
        if (relation.isPresent()) {
            return relation.get();
        }
        if (CollectionUtils.isEmpty(roleIds)) {
            return NONE;
        }
        sql = String.format("select relation_type from resource_permission where org_id=%d and resource_id = %d and resource_type='%s' and role_id in (%s)", orgId, resourceId, resourceType.name(), roleIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
        relation = calculateRelations0(sql, resourceType);
        return relation.orElse(NONE);
    }

    private Optional<ResourcePermissionRelationType> calculateRelations0(String sql, ResourcePermissionType resourceType) {
        List<String> relations = jdbcTemplate.queryForList(sql, String.class);
        if (CollectionUtils.isNotEmpty(relations)) {
            return relations.stream().map(i -> ResourcePermissionRelationType.parseOrDefault(i, resourceType)).max(Enum::compareTo);
        }
        return Optional.empty();
    }

    // 查询当前资源的所有用户（用户，角色的用户）, 包括用户的最大权限
    public List<ResourcePermissionUserDto> relationUsers(Long orgId, long resourceId, ResourcePermissionType resourceType) {
        Map<Long, ResourcePermissionRelationType> userRelationTypeMap = new LinkedHashMap<>();
        Map<Long, ResourcePermissionRelationType> roleRelationTypeMap = new HashMap<>();
        Long ownerUserId = getOwnerUserId(resourceId, resourceType);
        if (ownerUserId != null) {
            userRelationTypeMap.put(ownerUserId, OWNER);
        }
        resourcePermissions(orgId, resourceId, resourceType, i -> true, i -> {
            if (i.getUserId() != null) {
                ResourcePermissionRelationType relation = resourceType.emptyRelationIsDefault(i.getRelationType());
                userRelationTypeMap.put(i.getUserId(), relation);
            } else if (i.getRoleId() != null) {
                ResourcePermissionRelationType relation = resourceType.emptyRelationIsDefault(i.getRelationType());
                roleRelationTypeMap.put(i.getRoleId(), relation);
            }
            return null;
        });
        if (!roleRelationTypeMap.isEmpty()) {
            roleService.getUserRolesByRoleIds(orgId, roleRelationTypeMap.keySet()).forEach((userId, roleIds) -> {
                List<ResourcePermissionRelationType> types = new ArrayList<>();
                Optional.ofNullable(userRelationTypeMap.get(userId)).ifPresent(types::add);
                roleIds.forEach(roleId -> Optional.ofNullable(roleRelationTypeMap.get(roleId)).ifPresent(types::add));
                ResourcePermissionRelationType max = ResourcePermissionRelationType.max(types);
                if (max != NONE) {
                    userRelationTypeMap.put(userId, max);
                }
            });
        }
        List<ResourcePermissionUserDto> users = getPermissionUsers(userRelationTypeMap.keySet());
        if (CollectionUtils.isNotEmpty(users)) {
            users.forEach(i -> {
                i.setType(Optional.ofNullable(userRelationTypeMap.get(i.getId())).orElse(NONE).name());
            });
        }
        return users;
    }

    private Long getOwnerUserId(long resourceId, ResourcePermissionType resourceType) {
        List<Long> ids = Optional.ofNullable(resourceType.buildOwnerSql(resourceId)).map(sql -> jdbcTemplate.queryForList(sql, Long.class)).orElse(null);
        if (CollectionUtils.isNotEmpty(ids)) {
            return ids.get(0);
        }
        return null;
    }

    enum ChangeFlag {
        SYNC,
        ADD,
        REMOVE,
        UPDATE;

        public boolean calculateAdd() {
            return this == SYNC || this == ADD;
        }

        public boolean calculateDel() {
            return this == SYNC || this == REMOVE;
        }

        public boolean calculateUpdate() {
            return this == SYNC || this == UPDATE;
        }
    }
}

