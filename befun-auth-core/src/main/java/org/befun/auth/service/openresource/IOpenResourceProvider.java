package org.befun.auth.service.openresource;

import org.befun.auth.constant.IOpenResourceType;
import org.befun.auth.dto.OpenResourceInfo;
import org.befun.auth.entity.OpenResource;
import org.befun.core.utils.JsonHelper;

public interface IOpenResourceProvider<P, T extends IOpenResourceType<P>, R> {

    T getType();

    void checkEnabled();

    R get(Long orgId, Long userId, P params);

    OpenResourceInfo build(OpenResource entity, String token);

    default P parseParams(String params) {
        return JsonHelper.toObject(params, getType().getParamsClass());
    }

    default String formatParams(P params) {
        return JsonHelper.toJson(params);
    }
}
