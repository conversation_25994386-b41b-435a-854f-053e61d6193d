package org.befun.auth.service;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.AuthBindStatus;
import org.befun.auth.constant.AuthProviderType;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.*;
import org.befun.auth.dto.auth.authcode.AuthCodeCache;
import org.befun.auth.dto.auth.authcode.AuthCodeDto;
import org.befun.auth.dto.auth.authcode.AuthCodeStatusDto;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.entity.User;
import org.befun.auth.event.AuthBindEvent;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.provider.IAuthProvider;
import org.befun.auth.provider.ILoginStatus;
import org.befun.auth.repository.ThirdPartyUserRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.auth.config.CasConfig;
import org.befun.auth.service.auth.config.OauthConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.befun.auth.constant.ThirdPartyAuthType.CAS;
import static org.befun.auth.constant.ThirdPartyAuthType.OAUTH;
import static org.befun.core.security.LegacyAuthTokenFilter.AUTHORIZATION;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service("auth")
@Slf4j
public class AuthService {

    public static final String CACHE_LOGIN_RESPONSE_PREFIX = "loginResponse:";

    @Autowired
    private AuthProperties authProperties;
    @Autowired(required = false)
    private Map<String, IAuthProvider> authProviderMap;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private ThirdPartyUserRepository thirdPartyUserRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;

    private boolean containAuthProvider(String source) {
        String key = AuthProviderType.providerKey(source);
        if (StringUtils.isNotEmpty(key)) {
            return authProviderMap.containsKey(key);
        }
        return false;
    }

    private IAuthProvider getAuthProvider(String source) {
        String key = AuthProviderType.providerKey(source);
        if (StringUtils.isNotEmpty(key)) {
            return authProviderMap.get(key);
        }
        return null;
    }

    private IAuthProvider requireAuthProvider(String source) {
        if (!containAuthProvider(source)) {
            log.warn("invalid auth source for ping {}, skip", source);
            throw new BadRequestException("invalid source");
        }
        return getAuthProvider(source);
    }

    private void checkIsSupportApp(String app) {
        if (!authProperties.getApps().contains(app)) {
            throw new BadRequestException("请输入正确的公司代号！");
        }
    }

    /**
     * 授权地址
     */
    public String authorize(String source, String app) {
        checkIsSupportApp(app);
        return requireAuthProvider(source).authorize(source, app);
    }

    /**
     * 授权回调-登录
     */
    public <P> LoginStatusDto loginStatus(String source, String app, P param) {
        checkIsSupportApp(app);
        IAuthProvider provider = requireAuthProvider(source);
        boolean hasActiveToken = false;
        if (provider instanceof ILoginStatus) {
            hasActiveToken = ((ILoginStatus<P>) provider).hasActiveToken(param, app);
        }
        return new LoginStatusDto(hasActiveToken);
    }

    /**
     * 授权回调-登录
     */
    @Transactional
    public Object login(String source, String app, Object callback) {
        checkIsSupportApp(app);
        return requireAuthProvider(source).login(source, app, callback);
    }

    public LoginResponseDto loginResponse(String token) {
        String key = CACHE_LOGIN_RESPONSE_PREFIX + token;
        String value = redisTemplate.opsForValue().get(key);
        LoginResponseDto responseDto = null;
        if (value != null) {
            responseDto = JsonHelper.toObject(value, LoginResponseDto.class);
            redisTemplate.delete(key);
        }
        if (responseDto != null) {
            return responseDto;
        } else {
            throw new BadCredentialsException("请重新登录");
        }
    }

    /**
     * 发送验证码
     */
    public boolean sendVerifyCode(String source, String app, String useFor, Object account) {
        checkIsSupportApp(app);
        VerifyCodeUseFor e = VerifyCodeUseFor.parse(useFor);
        if (e == null) {
            throw new BadRequestException("不支持的useFor类型：" + useFor);
        }
        return requireAuthProvider(source).sendVerifyCode(source, app, e, account);
    }

    /**
     * 校验验证码
     */
    public boolean verifyCodeStatus(String source, String app, String useFor, Object account) {
        checkIsSupportApp(app);
        VerifyCodeUseFor e = VerifyCodeUseFor.parse(useFor);
        if (e == null) {
            throw new BadRequestException("不支持的useFor类型：" + useFor);
        }
        if (requireAuthProvider(source).verifyCodeStatus(source, app, e, account)) {
            return true;
        }
        throw new BadRequestException("验证码错误");
    }

    /**
     * 绑定账号信息
     */
    public boolean bind(String source, String app, Object accountInfo) {
        checkIsSupportApp(app);
        return requireAuthProvider(source).bind(app, source, accountInfo);
    }

    /**
     * 验证码重置密码
     */
    public boolean resetPasswordByVerifyCode(String source, String app, Object accountInfo) {
        checkIsSupportApp(app);
        return requireAuthProvider(source).resetPasswordByVerifyCode(app, source, accountInfo);
    }

    /**
     * @param source
     * @return
     */
    public BindResponseDto bindByQrCode(String app, String source) {
        checkIsSupportApp(app);

        String bindId = UUID.randomUUID().toString();
        IAuthProvider authProvider = requireAuthProvider(source);
        ThirdPartyBindDto tbd = authProvider.bindByQrCode(app, source, bindId);
        log.debug("bindByQrCode bindId: {} state: {}", bindId, tbd.getState());
        String redisKey = String.format("%s:bind:%s", authProperties.getRedisPrefix(), bindId);
        redisTemplate.opsForValue().set(redisKey, tbd.getState(), authProperties.getBindExpirationInSeconds(), TimeUnit.SECONDS);
        return BindResponseDto.builder()
                .id(bindId)
                .expiredInSeconds(authProperties.getBindExpirationInSeconds())
                .qrCode(tbd.getQrCode())
                .build();
    }

    /**
     * check binding result
     *
     * @param id
     * @return
     */
    public BindResultResponseDto checkBindingResult(String id) {
        BindResultResponseDto response = new BindResultResponseDto();
        String redisKey = String.format("%s:bind:%s", authProperties.getRedisPrefix(), id);
        String state = redisTemplate.opsForValue().get(redisKey);
        if (Strings.isNullOrEmpty(state)) {
            response.setStatus(AuthBindStatus.EXPIRED);
        } else {
            AuthBindStateDto metaDto = new AuthBindStateDto(state);
            Long userId = metaDto.getUserId();
            String app = metaDto.getApp();
            String source = metaDto.getSource();
            if (userId == null) {
                throw new BadRequestException();
            }
            Optional<User> user = userRepository.findById(userId);
            if (user.isEmpty()) {
                throw new BadRequestException();
            }
            Optional<ThirdPartyUser> tpUser = thirdPartyUserRepository.findFirstByAppAndSourceAndUserId(app, source, userId);
            if (tpUser.isPresent()) {
                response.setStatus(AuthBindStatus.ALREADY_BIND);
            } else {
                response.setStatus(AuthBindStatus.PENDING);
            }
        }
        return response;
    }

    /**
     * onMessage
     */
    public void onMessage(String app, String source, HttpServletRequest request, String requestBody, HttpServletResponse response) {
        checkIsSupportApp(app);
        if (!containAuthProvider(source)) {
            log.warn("invalid auth source for ping {}, skip", source);
            response.setStatus(200);
            return;
        }
        IAuthProvider authProvider = getAuthProvider(source);
        authProvider.onMessage(app, request, requestBody, response);
    }

    /**
     * verify
     */
    public Object verify(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        checkIsSupportApp(app);
        if (!containAuthProvider(source)) {
            log.warn("invalid auth source for ping {}, skip", source);
            response.setStatus(200);
            return null;
        }
        IAuthProvider authProvider = getAuthProvider(source);
        return authProvider.verify(app, source, request, response);
    }

    /**
     * ping
     */
    public AuthPingResponseDto ping(String app, String source, HttpServletRequest request, HttpServletResponse response) {
        checkIsSupportApp(app);
//        if (!authProviderMap.containsKey(source)) {
//            log.warn("invalid auth source for ping {}, skip", source);
//            throw new BadRequestException("invalid source");
//        }
        IAuthProvider authProvider = requireAuthProvider(source);
        return authProvider.ping(app, request, response);
    }

    /**
     * 内部 auth-bind 事件处理
     *
     * @param bindEvent
     */
    @EventListener(classes = {AuthBindEvent.class})
    @Transactional
    public void handleBindEvent(AuthBindEvent bindEvent) {
        String app = bindEvent.getApp();
        String openId = bindEvent.getOpenId();
        String source = bindEvent.getSource();

        switch (bindEvent.getBindType()) {
            case BINDING:
                Long userId = bindEvent.getUserId();
                if (userId == null) {
                    log.warn("missing userId, skip binding event");
                    return;
                }
                Optional<User> userOptional = userRepository.findById(userId);
                if (userOptional.isEmpty()) {
                    log.warn("user {} not found, skip binding event", userId);
                    return;
                }
                User user = userOptional.get();
                log.info("bind user:{} with app:{} source:{} openId:{}", userId, app, source, openId);
                Optional<ThirdPartyUser> tpUser = thirdPartyUserRepository.findFirstByAppAndSourceAndOpenIdAndUserId(
                        app, source, openId, userId);
                if (tpUser.isEmpty()) {
                    log.info("create a new bind to user {} with openId: {}", userId, openId);
                    ThirdPartyUser thirdPartyUser = new ThirdPartyUser();
                    thirdPartyUser.setUserId(user.getId());
                    thirdPartyUser.setOrgId(user.getOrgId());
                    thirdPartyUser.setApp(app);
                    thirdPartyUser.setSource(source);
                    thirdPartyUser.setOpenId(openId);
                    thirdPartyUser.setNickname(bindEvent.getUserInfoDto().getNickname());
                    thirdPartyUserRepository.save(thirdPartyUser);
                } else {
                    log.info("bind to user {} with openId: {} already exist, just skip", userId, openId);
                }
                break;
            case UNBINDING:
                List<ThirdPartyUser> tpUsers = thirdPartyUserRepository.findAllByAppAndSourceAndOpenId(app, source, openId);
                thirdPartyUserRepository.deleteAll(tpUsers);
                log.info("unbind app:{} openId:{} total count {}", app, openId, tpUsers.size());
                break;
            case AUTH_CODE:
                AuthCodeCache cache = getCacheAuthCode(bindEvent.getEventKey());
                if (cache != null && !cache.isActive()) {
                    cache.setOpenId(openId);
                    cache.setStatus("active");
                    List<ThirdPartyUser> users = thirdPartyUserRepository.findAllByAppAndSourceAndOpenId(app, source, openId);
                    if (CollectionUtils.isNotEmpty(users)) {
                        AtomicBoolean find = new AtomicBoolean(false);
                        users.forEach(u -> {
                            if (!find.get() && u.getUserId() != null && u.getUserId() > 0) {
                                SimpleUser simpleUser = userRepository.findSimpleById(u.getUserId());
                                if (simpleUser != null) {
                                    cache.setUserId(simpleUser.getId());
                                    find.set(true);
                                    return;
                                }
                            }
                            // 删除多余的绑定信息
                            thirdPartyUserRepository.delete(u);
                        });
                    }
                    cacheAuthCode(bindEvent.getEventKey(), cache, null);
                }
        }
    }

    public LogoutResponseDto logout(HttpServletRequest request, HttpServletResponse response) {
        LogoutResponseDto dto = new LogoutResponseDto();
        try {
            var token = request.getHeader(AUTHORIZATION);
            var sessionKey = LegacyAuthTokenFilter.getSessionKey(token);
            redisTemplate.delete(sessionKey);
            dto.setLogoutRedirectUrl(logoutRedirect());
        } catch (Exception e) {
            log.error("logout error", e);
        }
        return dto;
    }


    public String logoutRedirect() {
        try {
            for (ThirdPartyAuthType type : List.of(CAS, OAUTH)) {
                ThirdPartyAuth auth = thirdPartyAuthService.getByOrg(TenantContext.getCurrentTenant(), type, "cem");
                if (auth != null) {
                    String config = auth.getConfig();
                    if (StringUtils.isNotEmpty(config)) {
                        if (type == CAS) {
                            CasConfig casConfig = JsonHelper.toObject(config, CasConfig.class);
                            if (casConfig != null && StringUtils.isNotEmpty(casConfig.getLogoutRedirectUrl())) {
                                return casConfig.getLogoutRedirectUrl();
                            }
                        } else if (type == OAUTH) {
                            OauthConfig oauthConfig = JsonHelper.toObject(config, OauthConfig.class);
                            if (oauthConfig != null && StringUtils.isNotEmpty(oauthConfig.getLogoutRedirectUrl())) {
                                return oauthConfig.getLogoutRedirectUrl();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("logOutRedirect error", e);
        }

        return null;
    }

    public AuthCodeDto buildAuthCode(String app, String source) {
        checkIsSupportApp(app);
        IAuthProvider authProvider = requireAuthProvider(source);
        String code = "authCode:" + UUID.randomUUID().toString().replaceAll("-", "");
        String qrCode = authProvider.authCodeByQrCode(app, source, code);
        log.debug("authCode code: {} qrCode: {}", code, qrCode);
        cacheAuthCode(app, source, code);
        return new AuthCodeDto(app, source, code, qrCode, authProperties.getBindExpirationInSeconds());
    }

    public AuthCodeStatusDto authCodeStatus(String app, String source, String code) {
        AuthCodeCache cache = getCacheAuthCode(code);
        if (cache == null) {
            return AuthCodeStatusDto.expire(app, source, code);
        }
        return AuthCodeStatusDto.fromCache(cache);
    }

    /**
     * 1 授权码缓存还在
     * 2 授权码缓存中的信息状态为active,并且写入了openId
     * 3 授权码缓存中的信息没有用户id
     */
    public AuthCodeCache checkAuthCodeByBind(String authCode) {
        if (StringUtils.isEmpty(authCode)) {
            return null;
        }
        AuthCodeCache cache = getCacheAuthCode(authCode);
        if (cache == null) {
            throw new BadRequestException("授权码已失效");
        }
        if (!cache.isActive()) {
            throw new BadRequestException("授权码无效");
        }
        if (cache.getUserId() != null) {
            throw new BadRequestException("授权码不支持绑定用户");
        }
        return cache;
    }

    /**
     * 绑定之前需要校验这个用户还没有绑定关系
     */
    public int countUserBinding(String app, String source, User user) {
        if (user == null) {
            return 0;
        }
        return (int) thirdPartyUserRepository.countByAppAndSourceAndUserId(app, source, user.getId());
    }

    public void authCodeBindUser(User user, AuthCodeCache cache) {
        if (user == null || cache == null) {
            return;
        }
        ThirdPartyUser thirdPartyUser = new ThirdPartyUser();
        thirdPartyUser.setUserId(user.getId());
        thirdPartyUser.setOrgId(user.getOrgId());
        thirdPartyUser.setApp(cache.getApp());
        thirdPartyUser.setSource(cache.getSource());
        thirdPartyUser.setOpenId(cache.getOpenId());
        thirdPartyUser.setValid(true);
        thirdPartyUserRepository.save(thirdPartyUser);
        clearAuthCode(cache.getCode());
    }

    /**
     * 1 授权码缓存还在
     * 2 授权码缓存中的信息状态为active,并且写入了openId
     * 3 授权码缓存中的信息有用户id
     */
    public AuthCodeCache checkAuthCodeByLogin(String authCode) {
        if (StringUtils.isEmpty(authCode)) {
            throw new BadRequestException("授权码已失效");
        }
        AuthCodeCache cache = getCacheAuthCode(authCode);
        if (cache == null) {
            throw new BadRequestException("授权码已失效");
        }
        if (!cache.isActive()) {
            throw new BadRequestException("授权码无效");
        }
        if (cache.getUserId() == null) {
            throw new BadRequestException("授权码未绑定用户");
        }
        return cache;
    }

    public void clearAuthCode(String authCode) {
        redisTemplate.delete(authCodeKey(authCode));
    }

    private String authCodeKey(String code) {
        return String.format("%s:%s", authProperties.getRedisPrefix(), code);
    }

    public AuthCodeCache getCacheAuthCode(String code) {
        String key = authCodeKey(code);
        String value = redisTemplate.opsForValue().get(key);
        return JsonHelper.toObject(value, AuthCodeCache.class);
    }

    private void cacheAuthCode(String app, String source, String code) {
        AuthCodeCache cache = new AuthCodeCache(app, source, code);
        Duration expire = Duration.ofSeconds(authProperties.getBindExpirationInSeconds());
        cacheAuthCode(code, cache, expire);
    }

    private void cacheAuthCode(String code, AuthCodeCache cache, Duration expire) {
        String key = authCodeKey(code);
        if (expire != null) {
            redisTemplate.opsForValue().set(key, JsonHelper.toJson(cache), expire);
        } else {
            redisTemplate.opsForValue().setIfPresent(key, JsonHelper.toJson(cache));
        }
    }
}
