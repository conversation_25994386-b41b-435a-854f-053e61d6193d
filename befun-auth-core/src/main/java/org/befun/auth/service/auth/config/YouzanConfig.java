package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.auth.IClearConfigSecret;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class YouzanConfig extends AbstractConfig implements IClearConfigSecret {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "true 有效的; false 无效的，检测到有赞商城已解除授权，或者授权已过期")
    private boolean valid = true;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否开启")
    private boolean enable;

    @Schema(hidden = true, description = "店铺id")
    private String kdtId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "触发规则")
    private List<TriggerJourneyRule> rules = new ArrayList<>();

    @Getter
    @Setter
    public static class TriggerJourneyRule {
        @JsonView(ResourceViews.Basic.class)
        @Schema(description = "有赞事件")
        private String event;
        @JsonView(ResourceViews.Basic.class)
        @Schema(description = "场景id完整路径")
        private String paths;
        @JsonView(ResourceViews.Basic.class)
        @Schema(description = "场景id")
        private Long sceneId;
        @JsonView(ResourceViews.Basic.class)
        @Schema(description = "旅程id")
        private Long journeyMapId;
        @JsonView(ResourceViews.Basic.class)
        @Schema(description = "发送管理id")
        private Long sendManageId;
    }

    @Override
    public void clearSecret() {
        kdtId = null;
    }
}

