package org.befun.auth.service.auth.config;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.auth.oauth.OauthRequestDto;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class OauthConfig extends AbstractConfig {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "授权地址", required = true)
    private String authorizeUrl = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "认证地址", required = false)
    private OauthRequestDto tokenConfig;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "资源地址", required = true)
    private OauthRequestDto resourceConfig;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "退出地址", required = false)
    private String logoutRedirectUrl = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：id", required = true)
    private String paramId = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：姓名")
    private String paramName = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：部门")
    private String paramDepartment = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：手机")
    private String paramMobile = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：邮箱")
    private String paramEmail = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：员工号")
    private String paramEmployeeNo = "";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "参数名：角色编号")
    private String paramRoleCode = "";

}
