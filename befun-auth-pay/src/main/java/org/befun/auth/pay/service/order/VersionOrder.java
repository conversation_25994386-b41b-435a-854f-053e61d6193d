package org.befun.auth.pay.service.order;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.OrgVersionRecordStatus;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationVersionRecord;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.dto.order.PlaceOrderDto;
import org.befun.auth.pay.dto.order.PlaceOrderResponseDto;
import org.befun.auth.pay.dto.order.VersionOrderAmountResponseDto;
import org.befun.auth.pay.dto.order.VersionOrderRequestDto;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.property.OrderVersionProperties;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.OrganizationVersionRecordService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.service.XpackConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Component
public class VersionOrder extends OrderImpl<Boolean, VersionOrderAmountResponseDto, VersionOrderRequestDto, Boolean> {

    @Autowired
    private OrderVersionProperties orderVersionProperties;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private XpackConfigService xpackConfigService;
    @Autowired
    private OrganizationVersionRecordService organizationVersionRecordService;

    @Override
    public OrderType type() {
        return OrderType.order_org_version;
    }

    @Override
    public VersionOrderAmountResponseDto amount(Long orgId, Long userId, Boolean a) {
        Organization org = organizationService.requireById(orgId);
        OrgConfigDto orgConfig = organizationConfigService.getConfig(orgId, OrganizationConfigType.baseInfo);
        AppVersion version = organizationService.parseOrgVersion2(org);
        LocalDate startDate = DateHelper.toLocalDate(org.getAvailableDateBegin());
        LocalDate endDate = DateHelper.toLocalDate(org.getAvailableDateEnd());
        Integer currentVersionPrice = null;
        OrderVersionProperties orderVersionProperties = getOrderVersionProperties();
        if (version == AppVersion.EMPTY || version == AppVersion.FREE || endDate == null || endDate.isBefore(LocalDate.now())) {
            // 如果当前版本为免费版，或者过期了，则为0
            currentVersionPrice = 0;
        } else {
            // 否则使用当前版本的价格
            currentVersionPrice = orderVersionProperties.getVersionPrice(version);
        }
        String logo = null;
        if (orgConfig != null && orgConfig.getBaseInfo() != null) {
            logo = orgConfig.getBaseInfo().getLogo();
        }
        return new VersionOrderAmountResponseDto(org.getName(), logo, version, startDate, endDate, currentVersionPrice,
                orderVersionProperties.getDaysOfYear(),
                orderVersionProperties.getLimitLeftDays(),
                orderVersionProperties.getSupportVersions());
    }


    /**
     * 如果数据库中设置了，则使用数据库的数据，否则使用配置的数据
     */
    private OrderVersionProperties getOrderVersionProperties() {
        XpackConfig versionPrice = xpackConfigService.getConfigByType(XPackAppType.APP_VERSION_PRICE);
        OrderVersionProperties orderVersionProperties = null;
        if (versionPrice != null && versionPrice.getEnabled() != null && versionPrice.getEnabled()) {
            orderVersionProperties = JsonHelper.toObject(versionPrice.getConfig(), OrderVersionProperties.class);
        }
        if (orderVersionProperties == null) {
            orderVersionProperties = this.orderVersionProperties;
        }
        return orderVersionProperties;
    }

    @Override
    public PlaceOrderResponseDto place(Long orgId, Long userId, VersionOrderRequestDto dto) {
        VersionOrderAmountResponseDto orderAmount = amount(orgId, userId, true);
        if (!orderAmount.getAvailablePayTypes().contains(dto.getPayType())) {
            throw new BadRequestException("支付方式校验失败");
        }
        VersionOrderAmountResponseDto.AmountInfo amountInfo = orderAmount.getVersions().get(dto.getVersion());
        if (amountInfo == null || !amountInfo.isAvailable()) {
            throw new BadRequestException("购买版本校验失败");
        } else if (amountInfo.getCostAmount() != dto.getCostAmount()) {
            throw new BadRequestException("支付金额校验失败");
        } else if (!amountInfo.getStartDate().isEqual(dto.getStartDate()) || !amountInfo.getEndDate().isEqual(dto.getEndDate())) {
            throw new BadRequestException("购买时长校验失败");
        }
        // 如果当前有版本购买订单，取消掉
        cancelOldVersionOrder(orgId);
        // 添加版本购买记录
        OrganizationVersionRecord record = organizationVersionRecordService.add(orgId, userId, dto.getVersion(), amountInfo.getAmountType().name(), amountInfo.getPrice(), dto.getCostAmount(), dto.getStartDate(), dto.getEndDate());
        // 构造下单参数
        PlaceOrderDto placeOrderDto = dto.buildPlaceOrderParam(record.getId());
        // 下单并支付
        return placeOrder(orgId, userId, OrderType.order_org_version, PayServiceRate.none, placeOrderDto, null);
    }

    /**
     * 关闭旧的版本订单
     */
    public void cancelOldVersionOrder(Long orgId) {
        List<OrganizationVersionRecord> versionRecords = organizationVersionRecordService.getByStatus(orgId, OrgVersionRecordStatus.init);
        if (CollectionUtils.isNotEmpty(versionRecords)) {
            versionRecords.forEach(i -> {
                List<OrganizationOrder> orders = repository.findByOrgIdAndTypeAndSourceId(orgId, OrderType.order_org_version, i.getId());
                if (CollectionUtils.isNotEmpty(orders)) {
                    for (OrganizationOrder oldOrder : orders) {
                        if (oldOrder.getStatus() == OrderStatus.init) {
                            organizationRechargeService.rechargeCancel(oldOrder.getRechargeId());
                        }
                    }
                }
            });
        }
    }

    /**
     * 版本订单-订单结束回调
     * 如果订单成功了，修改企业版本信息，修改购买记录状态
     * 订单取消了，修改购买记录状态
     */
    public OrgVersionRecordStatus callback(OrganizationOrder order) {
        OrganizationVersionRecord record = organizationVersionRecordService.get(order.getSourceId());
        if (record == null || record.getStatus() != OrgVersionRecordStatus.init) {
            // 购买记录状态已修改过，直接结束
            return null;
        }
        OrgVersionRecordStatus status = OrgVersionRecordStatus.failure;
        if (order.getStatus() == OrderStatus.success) {
            boolean changeVersion = false;
            try {
                changeVersion = organizationService.updateVersion(order.getOrgId(), record.getVersion(), record.getStartDate(), record.getEndDate());
            } catch (Throwable e) {
                log.error("购买版本时，修改版本失败");
            }
            if (changeVersion) {
                status = OrgVersionRecordStatus.success;
            }
        } else if (order.getStatus() == OrderStatus.cancel) {
            status = OrgVersionRecordStatus.cancel;
        }
        organizationVersionRecordService.updateStatus(record, status);
        return status;
    }

}
