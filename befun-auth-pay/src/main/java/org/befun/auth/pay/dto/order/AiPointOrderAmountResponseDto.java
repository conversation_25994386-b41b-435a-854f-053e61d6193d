package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;
import org.befun.auth.utils.NumberHelper;
import org.befun.core.exception.BadRequestException;

import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;

@Getter
@Setter
@Schema(description = "AI点数充值金额计算结果")
@NoArgsConstructor
public class AiPointOrderAmountResponseDto extends OrderAmountResponseDto {

    @Schema(description = "充值数量")
    private int aiPoint;

    @Schema(description = "每个AI点数的价格（单位：分）: 1")
    private int price;

    public AiPointOrderAmountResponseDto(Integer aiPoint, RechargeType rechargeType, Integer price, Integer walletBalance,
                                         Set<RechargeType> supportRechargeTypes, BiFunction<Integer, RechargeType, RechargeAmountDto> getRechargeWechatAmount) {
        this.aiPoint = NumberHelper.unbox(aiPoint);
        if (aiPoint < 1000 || aiPoint > 1000000) {
            throw new BadRequestException("ai点数充值最低充值1000点，最高不超过100万点");
        }
        this.price = NumberHelper.unbox(price);
        calcAmount(this.aiPoint * this.price, walletBalance, rechargeType, supportRechargeTypes, getRechargeWechatAmount);
    }
}
