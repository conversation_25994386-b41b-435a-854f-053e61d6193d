package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.constant.PayType;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class PlaceOrderDto {

    @NotNull
    @Schema(description = "支付方式", required = true)
    private PayType payType;

    @NotEmpty
    @Schema(description = "订单名称", required = true)
    private String title;

    @Schema(description = "充值名称，别名")
    private String titleAlias;

    @NotEmpty
    @Schema(description = "订单金额", required = true)
    private Integer costAmount;

    @NotEmpty
    @Schema(description = "钱包支付金额", required = true)
    private Integer amountWallet;

    @NotEmpty
    @Schema(description = "充值支付金额", required = true)
    private Integer amountRecharge;

    @Schema(description = "订单来源：红包id,短信充值记录id,版本购买记录id", required = true)
    private Long sourceId;

}
