package org.befun.auth.pay.service;

import org.apache.commons.lang3.StringUtils;
import org.befun.auth.pay.constant.*;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;
import org.befun.auth.pay.dto.recharge.RechargeRequestDto;
import org.befun.auth.pay.dto.recharge.RechargeResponseDto;
import org.befun.auth.pay.dto.recharge.RechargeStatusDto;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeDto;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.property.PayProperties;
import org.befun.auth.pay.property.RechargeProperties;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.repository.OrganizationRechargeRepository;
import org.befun.auth.pay.thirdpartypay.AuthPayHandler;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.OrganizationWalletService;
import org.befun.auth.utils.GeneratorHelper;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.LockRunnableHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

@Service
public class OrganizationRechargeService extends BaseService<OrganizationRecharge, OrganizationRechargeDto, OrganizationRechargeRepository> {

    @Autowired
    private RechargeProperties rechargeProperties;
    @Autowired
    private PayProperties payProperties;
    @Autowired
    private AuthPayHandler authPayHandler;
    @Autowired
    private IAuthEventTrigger authEventTrigger;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationBillService organizationBillService;
    @Autowired
    private OrganizationWalletService organizationWalletService;
    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    private LockRunnableHelper lockRunnableHelper;
    @Autowired
    private OrganizationRechargeRefundRepository organizationRechargeRefundRepository;

    /**
     * @param rechargeAmount 红包返还金额
     * @param title          名称：红包返回：{surveyName}
     */
    @Transactional
    public void rechargeByRedPacket(Long orgId, Long userId, Integer rechargeAmount, String title) {
        RechargeRequestDto dto = new RechargeRequestDto(RechargeType.recharge_red_packet, rechargeAmount, title);
        RechargeResponseDto rechargeResponseDto = recharge(orgId, userId, null, dto, PayServiceRate.none);
        rechargeCallback(dto.getRechargeType(), Map.of(), rechargeResponseDto.getRechargeId().toString());
    }

    @Transactional
    public void rechargeByPlatform(Long orgId, Long userId, Integer rechargeAmount, String title) {
        RechargeRequestDto dto = new RechargeRequestDto(RechargeType.recharge_platform, rechargeAmount, title);
        RechargeResponseDto rechargeResponseDto = recharge(orgId, userId, null, dto, PayServiceRate.none);
        rechargeCallback(dto.getRechargeType(), Map.of(), rechargeResponseDto.getRechargeId().toString());
    }

    /**
     * 计算充值金额（可能会有服务费）
     */
    public RechargeAmountDto rechargeAmount(int amount, RechargeType rechargeType, PayServiceRate payServiceRate) {
        Float serviceRate = payProperties.getServiceRate().get(payServiceRate);
        return new RechargeAmountDto(rechargeType, serviceRate, amount);
    }

    /**
     * 充值
     * 1 写入充值记录
     * 2 使用指定的充值方式，生成充值二维码
     * 3 缓存充值信息
     * 4 添加定时任务，如果到时间了还没支付结果，则设置取消状态
     */
    @Transactional
    public RechargeResponseDto recharge(Long orgId, Long userId, Long orderId, RechargeRequestDto dto, PayServiceRate payServiceRate) {
        RechargeAmountDto amount = rechargeAmount(dto.getRechargeAmount(), dto.getRechargeType(), payServiceRate);
        Date expireTime = DateHelper.toDate(LocalDateTime.now().plusMinutes(rechargeProperties.getExpireMinute()));
        OrganizationRecharge entity = new OrganizationRecharge();
        entity.setOrgId(orgId);
        entity.setTitle(dto.getTitle());
        entity.setTitleAlias(dto.getTitleAlias());
        entity.setOrderId(orderId);
        entity.setType(dto.getRechargeType());
        entity.setRechargeAmount(amount.getRechargeAmount());
        entity.setServiceAmount(amount.getServiceAmount());
        entity.setTotalAmount(amount.getTotalAmount());
        entity.setServiceRate(amount.getServiceRate());
        entity.setExpireTime(expireTime);
        entity.setCreateUserId(userId);
        repository.save(entity);
        entity.setPayNo(GeneratorHelper.generatorPayNo(entity.getId()));
        repository.save(entity);
        // 发起第三方支付
        String payUrl = authPayHandler.placeOrder(dto.getRechargeType(), entity);
        RechargeResponseDto rechargeInfo = RechargeResponseDto.buildFromRecharge(entity, expireTime, payUrl);
        // cache
        cacheRechargeStatus(entity.getId(), entity.getStatus());
        cacheRechargeInfo(entity.getId(), rechargeInfo);
        // expire task
        authEventTrigger.rechargeExpired(orgId, userId, entity.getId(), Duration.ofMinutes(rechargeProperties.getExpireMinute() + 1));
        return rechargeInfo;
    }


    /**
     * 查询支付信息
     */
    public RechargeResponseDto rechargePayInfo(Long rechargeId) {
        String cacheKey = rechargeInfoKey(rechargeId);
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(value)) {
            RechargeResponseDto rechargeInfo = JsonHelper.toObject(value, RechargeResponseDto.class);
            if (rechargeInfo != null) {
                return rechargeInfo;
            }
        }
        throw new BadRequestException("支付已过期");
    }

    private String rechargeInfoKey(Long rechargeId) {
        return String.format("recharge-info:%d", rechargeId);
    }

    /**
     * 缓存支付信息
     */
    private void cacheRechargeInfo(Long rechargeId, RechargeResponseDto rechargeInfo) {
        String cacheKey = rechargeInfoKey(rechargeId);
        stringRedisTemplate.opsForValue().set(cacheKey, JsonHelper.toJson(rechargeInfo), Duration.ofMinutes(rechargeProperties.getExpireMinute()));
    }

    private String rechargeStatusKey(Long rechargeId) {
        return String.format("recharge-status:%d", rechargeId);
    }

    /**
     * 缓存充值状态
     */
    private void cacheRechargeStatus(Long rechargeId, RechargeStatus status) {
        String cacheKey = rechargeStatusKey(rechargeId);
        stringRedisTemplate.opsForValue().set(cacheKey, status.name(), Duration.ofMinutes(rechargeProperties.getExpireMinute()));
    }

    /**
     * 查询充值结果
     */
    public RechargeStatusDto rechargeStatus(Long rechargeId) {
        return new RechargeStatusDto(rechargeStatus0(rechargeId));
    }

    /**
     * 查询充值结果
     */
    public RechargeStatus rechargeStatus0(Long rechargeId) {
        String rechargeKey = rechargeStatusKey(rechargeId);
        Object value = stringRedisTemplate.opsForValue().get(rechargeKey);
        RechargeStatus status = null;
        if (value == null) {
            OrganizationRecharge entity = get(rechargeId);
            if (entity != null) {
                status = entity.getStatus();
            }
        } else {
            status = EnumHelper.parse(RechargeStatus.values(), value.toString());
        }
        if (status == null) {
            status = RechargeStatus.failure;
        }
        return status;
    }


    /**
     * 充值回调
     * 1 解析回调数据，判断充值结果
     * 2 充值记录加锁，
     * 2 充值成功，
     * 2.1 如果充值记录无关联的订单，或者订单金额需要从钱包流转，则把充值的金额加到钱包中，并写入账单
     * 2.2 缓存充值状态，
     * 2.3 执行充值成功后的订单逻辑
     * 3 充值失败，
     * 3.1 缓存充值状态
     * 3.2 执行充值失败后的订单逻辑
     * 4 发送充值完成事件
     * 使用场景
     * 模拟支付回调，在访问模拟支付地址后，会调用此方法，对指定的充值id进行充值
     * 红包返还充值时，会立即调用此回调方法，进行红包返还充值
     * 平台支付充值时，会立即调用此回调方法，进行平台支付充值
     * 接收到微信支付回调消息，会调用此方法，通过解析回调中的商户订单号，查找到充值id进行充值
     */
    @Transactional
    public void rechargeCallback(RechargeType rechargeType, Map<String, String> requestHeader, Object data) {
        String payNo = authPayHandler.placeOrderCallback(rechargeType, requestHeader, data);
        if (StringUtils.isNotEmpty(payNo)) {
            repository.findFirstByTypeAndPayNo(rechargeType, payNo).ifPresent(this::rechargeCallback);
        }
    }

    /**
     * 这个方法可以多次调用，如果充值状态是 init 状态，则会调用第三方接口去查询新的状态
     * 如果状态变更为 success 则会调用订单支付成功的逻辑
     */
    @Transactional
    public void rechargeCallback(OrganizationRecharge recharge) {
        // 充值记录加锁，禁止同一充值记录并发处理
        lockRunnableHelper.runOrThrow(LockKeys.recharge_callback, recharge.getId(), () -> {
            if (recharge.getStatus() != RechargeStatus.init) {
                return;
            }
            // 判断一下状态
            if (rechargeStatus0(recharge.getId()) != RechargeStatus.init) {
                // 已经处理了
                return;
            }
            if (!authPayHandler.queryOrder(recharge.getType(), recharge)) {
                return;
            }
            // 更新充值记录
            repository.save(recharge);
            // 尝试查询关联的订单，可能为null
            if (recharge.getStatus() == RechargeStatus.success) {
                OrganizationOrder order = organizationOrderService.get(recharge.getOrderId());
                // 如果没有找到订单，把充值的金额加到钱包中
                if (order == null) {
                    // add recharge amount
                    int balance = organizationWalletService.recharge(recharge.getOrgId(), recharge.getRechargeAmount());
                    // add recharge bill
                    organizationBillService.addByRecharge(balance, recharge);
                }
                // cache recharge success
                cacheRechargeStatus(recharge.getId(), RechargeStatus.success);
                // order success
                organizationOrderService.placeOrderByRechargeSuccess(order, recharge);
                authEventTrigger.rechargeCompleted(recharge.getOrgId(), recharge.getCreateUserId(), recharge.getId(), recharge.getStatus().name());
            }
        });
    }

    /**
     * 充值过期取消充值回调
     * 当前的充值状态必须是初始状态
     * 如果在取消之前重新去第三方查询发现已经成功了，这里会把最新的状态返回
     * worker 中的充值过期任务会调用此方式取消充值
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public RechargeStatus rechargeCancel(Long rechargeId) {
        RechargeStatus rechargeStatus = rechargeStatus0(rechargeId);
        if (rechargeStatus == RechargeStatus.init) {
            OrganizationRecharge recharge = get(rechargeId);
            if (recharge != null) {
                // 去第三方重新查询一次订单状态
                rechargeCallback(recharge);
                // 还是未支付状态，则关闭支付
                rechargeStatus = rechargeStatus0(rechargeId);
                if (rechargeStatus == RechargeStatus.init) {
                    // close
                    authPayHandler.closeOrder(recharge.getType(), recharge);
                    recharge.setStatus(RechargeStatus.cancel);
                    repository.save(recharge);
                    cacheRechargeStatus(rechargeId, RechargeStatus.cancel);
                    rechargeStatus = RechargeStatus.cancel;
                    // cancel order
                    if (recharge.getOrderId() != null && recharge.getOrderId() > 0) {
                        organizationOrderService.placeOrderByRechargeCancel(recharge.getOrderId(), recharge);
                    }
                    authEventTrigger.rechargeCompleted(recharge.getOrgId(), recharge.getCreateUserId(), recharge.getId(), recharge.getStatus().name());
                }
            }
        }
        return rechargeStatus;
    }

    public OrganizationRechargeRefund refund(Long orgId, Long userId, Long rechargeId, String title, int refundAmount, Long orderId, Long orderRefundId) {
        // 退款加锁，禁止同一记录并发处理
        return lockRunnableHelper.runOrThrow(LockKeys.recharge_refund, rechargeId, () -> {
            OrganizationRecharge recharge = get(rechargeId);
            if (recharge != null && refundAmount <= recharge.getRechargeAmount()) {
                OrganizationRechargeRefund refund = new OrganizationRechargeRefund();
                refund.setOrgId(orgId);
                refund.setOrderId(orderId);
                refund.setOrderRefundId(orderRefundId);
                refund.setRechargeId(rechargeId);
                refund.setTitle(title);
                refund.setType(recharge.getType());
                refund.setAmount(refundAmount);
                organizationRechargeRefundRepository.save(refund);
                refund.setRefundNo(GeneratorHelper.generatorByTimeAndId(refund.getId()));
                return refund(recharge, refund);
            }
            throw new BadRequestException("退款失败");
        });
    }

    public OrganizationRechargeRefund refund(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        // 去第三方发起退款
        authPayHandler.refundOrder(recharge, refund);
        organizationRechargeRefundRepository.save(refund);
        return refund;
    }

    @Transactional
    public void refundCallback(RechargeType rechargeType, Map<String, String> requestHeader, Object data) {
        String refundNo = authPayHandler.refundOrderCallback(rechargeType, requestHeader, data);
        if (StringUtils.isNotEmpty(refundNo)) {
            OrganizationRechargeRefund refund = organizationRechargeRefundRepository.findFirstByTypeAndRefundNo(rechargeType, refundNo);
            if (refund != null) {
                repository.findById(refund.getRechargeId()).ifPresent(recharge -> refundCallback(recharge, refund));
            }
        }
    }

    @Transactional
    public void refundCallback(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        // 退款回调加锁
        lockRunnableHelper.runOrThrow(LockKeys.recharge_refund_callback, refund.getId(), () -> {
            if (refund.getStatus() != RechargeRefundStatus.init) {
                // 有结果了，直接结束
                return;
            }
            if (!authPayHandler.queryRefundOrder(recharge, refund)) {
                // 没有查询到结果，结束
                return;
            }
            organizationRechargeRefundRepository.save(refund);
            if (recharge.getOrderId() != null && recharge.getOrderId() > 0) {
                OrganizationOrder order = organizationOrderService.get(recharge.getOrderId());
                if (order != null) {
                    if (refund.getStatus() == RechargeRefundStatus.success) {
                        // 如果订单类型为单次退款
                        if (order.getType().getRefundType().isSingle()) {
                            // 退款优先级为如果退款金额小于钱包支付的金额，则直接退到钱包，
                            // 如果大于钱包支付的金额，则先去第三方退款，然后在退钱包支付的所有金额
                            if (order.getAmountWallet() != null && order.getAmountWallet() > 0) {
                                // 退款钱包部分
                                rechargeByPlatform(recharge.getOrgId(), null, order.getAmountWallet(), refund.getTitle());
                            }
                        }
                        organizationBillService.addByRechargeRefund(recharge, refund);
                        order.setStatus(OrderStatus.refund);
                        organizationOrderService.save(order);
                    } else {
                        order.setStatus(OrderStatus.refund_failure);
                        organizationOrderService.save(order);
                    }
                }
            }
            // trigger refund completed event
            authEventTrigger.rechargeRefundCompleted(recharge.getOrgId(), null, recharge.getId(), refund.getId(), refund.getStatus().name());
        });
    }
}
