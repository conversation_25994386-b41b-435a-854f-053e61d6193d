package org.befun.auth.pay.dto.order;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayType;
import org.befun.auth.pay.entity.OrganizationOrder;

@Getter
@Setter
public class OrderInfoDto {

    private Long id;
    private String title;
    private OrderType type;
    private PayType payType;
    private Integer amount;
    private Integer amountWallet;
    private Integer amountRecharge;
    private OrderStatus status;

    public static OrderInfoDto build(OrganizationOrder order) {
        OrderInfoDto dto = new OrderInfoDto();
        dto.id = order.getId();
        dto.title = order.getTitle();
        dto.type = order.getType();
        dto.payType = order.getPayType();
        dto.amount = order.getAmount();
        dto.amountWallet = order.getAmountWallet();
        dto.amountRecharge = order.getAmountRecharge();
        dto.status = order.getStatus();
        return dto;
    }
}
