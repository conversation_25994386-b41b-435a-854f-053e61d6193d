package org.befun.auth.pay;

import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@ComponentScan({"org.befun.auth.pay"})
public class AuthPayAutoConfiguration {

    public static final String PACKAGE_ENTITY = "org.befun.auth.pay.entity";
    public static final String PACKAGE_REPOSITORY = "org.befun.auth.pay.repository";

    @Configuration
    @EntityScan({AuthPayAutoConfiguration.PACKAGE_ENTITY})
    @EnableJpaRepositories(basePackages = {AuthPayAutoConfiguration.PACKAGE_REPOSITORY}, repositoryBaseClass = BaseRepositoryImpl.class)
    public static class AuthPayConfig {
    }

}

