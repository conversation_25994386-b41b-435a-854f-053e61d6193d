package org.befun.auth.pay.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.OrderType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_order_refund")
@EntityScopeStrategy
@DtoClass
public class OrganizationOrderRefund extends EnterpriseEntity {

    @Schema(description = "订单id")
    @Column(name = "order_id")
    private Long orderId;

    @Schema(description = "退款单号")
    @Column(name = "refund_no")
    private String refundNo;

    @Column(name = "title")
    private String title;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private OrderType type;

    @Schema(description = "退款金额")
    @Column(name = "amount_refund")
    private Integer amountRefund;

    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;
}