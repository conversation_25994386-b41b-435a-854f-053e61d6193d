package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class AiPointOrderRequestDto extends OrderRequestDto {
    @Min(1000)
    @Max(1000000)
    @NotNull
    @Schema(description = "充值数量", required = true)
    private Integer aiPoint;

}
