package org.befun.auth.pay.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_recharge")
@EntityScopeStrategy
@DtoClass
public class OrganizationRecharge extends EnterpriseEntity {

    @Schema(description = "企业支付单号")
    @Column(name = "pay_no")
    private String payNo;

    @Schema(description = "第三方支付单号")
    @Column(name = "thirtparty_pay_no")
    private String thirtpartyPayNo;

    @Schema(description = "充值名称（商品名称）")
    @Column(name = "title")
    private String title;

    @Schema(description = "企业订单id，如果是企业订单发起的充值则有此参数")
    @Column(name = "order_id")
    private Long orderId;

    @Schema(description = "充值渠道：微信，支付宝")
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private RechargeType type;

    @Schema(description = "充值金额")
    @Column(name = "recharge_amount")
    private Integer rechargeAmount;

    @Schema(description = "服务费金额")
    @Column(name = "service_amount")
    private Integer serviceAmount;

    @Schema(description = "合计金额")
    @Column(name = "total_amount")
    private Integer totalAmount;

    @Schema(description = "服务费率")
    @Column(name = "service_rate")
    private Float serviceRate;

    @Schema(description = "充值状态：init 未支付 success 已成功 failure 已失败 cancel 已取消")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private RechargeStatus status = RechargeStatus.init;

    @Schema(description = "第三方支付状态")
    @Column(name = "thirtparty_status")
    private String thirtpartyStatus;

    @Column(name = "mock_recharge")
    private Integer mockRecharge = 0;

    @Schema(description = "过期时间")
    @Column(name = "expire_time")
    private Date expireTime;

    @Schema(description = "支付时间")
    @Column(name = "pay_time")
    private Date payTime;

    @Schema(description = "原始响应内容")
    @Column(name = "thirtpartyResponse")
    private String thirtpartyResponse;

    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;

    @Transient
    @Schema(hidden = true,description = "微信支付商品名称不能包含敏感词，这里使用别名")
    private String titleAlias;
}