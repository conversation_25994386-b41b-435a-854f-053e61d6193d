package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;

import java.util.Set;
import java.util.function.BiFunction;

@Getter
@Setter
@NoArgsConstructor
@Schema(description = "红包金额计算结果")
public class RedPacketOrderAmountResponseDto extends OrderAmountResponseDto {

    public RedPacketOrderAmountResponseDto(Integer redPacket,
                                           Integer walletBalance,
                                           RechargeType rechargeType,
                                           Set<RechargeType> supportRechargeTypes,
                                           BiFunction<Integer, RechargeType, RechargeAmountDto> getRechargeWechatAmount) {
        calcAmount(redPacket, walletBalance, rechargeType, supportRechargeTypes, getRechargeWechatAmount);
    }
}
