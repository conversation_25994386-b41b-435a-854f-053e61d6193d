package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.constant.PayType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class OrderRequestDto {

    @NotEmpty
    @Schema(description = "订单名称", required = true)
    protected String title;

    @Schema(description = "充值名称，别名")
    protected String titleAlias;

    @Min(1)
    @NotNull
    @Schema(description = "付款金额（单位：分），把之前计算出来的扣费金额重新传入，防止短信单价变动了，支付金额不一致")
    protected Integer costAmount;

    @NotNull
    @Schema(description = "钱包付款金额（单位：分）")
    protected Integer walletAmount = 0;

    @Schema(description = "需要充值金额")
    protected RechargeAmountDto recharge;

    @NotNull
    @Schema(description = "支付方式")
    protected PayType payType;

    public PlaceOrderDto buildPlaceOrderParam(Long sourceId) {
        PlaceOrderDto dto = new PlaceOrderDto();
        dto.setSourceId(sourceId);
        dto.setPayType(payType);
        dto.setTitle(title);
        dto.setTitleAlias(titleAlias);
        dto.setCostAmount(costAmount);
        dto.setAmountWallet(walletAmount);
        dto.setAmountRecharge(recharge == null ? 0 : recharge.getRechargeAmount());
        return dto;
    }
}
