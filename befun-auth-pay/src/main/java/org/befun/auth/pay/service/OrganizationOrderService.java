package org.befun.auth.pay.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.pay.constant.BillType;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.OrderInfoDto;
import org.befun.auth.pay.dto.order.OrderStatusDto;
import org.befun.auth.pay.dto.order.PlaceOrderResponseDto;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationOrderDto;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.repository.OrganizationOrderRepository;
import org.befun.auth.pay.service.order.IOrder;
import org.befun.auth.service.OrganizationWalletService;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.EnumHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;

@Slf4j
@Service
@SuppressWarnings("unchecked")
public class OrganizationOrderService extends BaseService<OrganizationOrder, OrganizationOrderDto, OrganizationOrderRepository> {

    @Autowired
    private OrganizationWalletService organizationWalletService;
    @Autowired
    private OrganizationBillService organizationBillService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IAuthEventTrigger authEventTrigger;

    @Autowired(required = false)
    private List<IOrder<?, ?, ?, ?>> orders = new ArrayList<>();
    private Map<OrderType, IOrder<?, ?, ?, ?>> orderMap = new HashMap<>();

    @PostConstruct
    public void init() {
        orders.forEach(i -> orderMap.put(i.type(), i));
    }

    private <A1, A2, O, F> IOrder<A1, A2, O, F> requireOrder(OrderType type) {
        IOrder<A1, A2, O, F> order = (IOrder<A1, A2, O, F>) orderMap.get(type);
        if (order == null) {
            throw new BadRequestException("订单类型不支持");
        }
        return order;
    }

    /**
     * 订单-计算金额
     */
    public <A1, A2> A2 amountOrder(long orgId, long userId, OrderType type, A1 a1) {
        return (A2) requireOrder(type).amount(orgId, userId, a1);
    }

    /**
     * 订单-下单
     */
    @Transactional
    public <O> PlaceOrderResponseDto placeOrder(long orgId, long userId, OrderType type, O o) {
        return requireOrder(type).place(orgId, userId, o);
    }

    /**
     * 订单-订单退还
     *
     * @return true 退还成功 false 退款中，需要去支付渠道退款
     */
    @Transactional
    public <F> boolean refundOrder(long orgId, long userId, OrderType type, F refund) {
        return requireOrder(type).refund(orgId, userId, refund);
    }

    /**
     * 订单-订单重新退还
     */
    @Transactional
    public boolean reRefundOrder(long orgId, long userId, OrderType type, Long orderRefundId) {
        return requireOrder(type).reRefund(orgId, userId, orderRefundId);
    }

    /**
     * 订单-订单取消
     */
    @Transactional
    public boolean cancelOrder(long orgId, long userId, OrderType type, Long orderId) {
        return requireOrder(type).cancel(orgId, userId, orderId);
    }

    /**
     * 缓存订单的key
     */
    public static String orderKey(Long orderId) {
        return String.format("order-info:%d", orderId);
    }

    /**
     * 缓存订单状态
     */
    private void cacheOrderStatus(Long orderId, OrderStatus status) {
        String orderKey = orderKey(orderId);
        stringRedisTemplate.opsForHash().put(orderKey, "status", status.name());
        stringRedisTemplate.expire(orderKey, Duration.ofHours(4));
    }

    private HashOperations<String, String, String> hashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    /**
     * 查询订单状态，并且确认订单的来源和订单的类型是否匹配
     */
    public OrderStatusDto orderStatus(Long orderId, Long confirmSourceId, OrderType confirmOrderType, Integer confirmOrderAmount) {
        if (confirmOrderType == null || confirmSourceId == null || confirmOrderAmount == null) {
            throw new BadRequestException();
        }
        String orderKey = orderKey(orderId);
        Map<String, String> value = hashOpt().entries(orderKey);
        OrderStatus status = null;
        OrderType orderType = null;
        Long sourceId = null;
        Integer amount = null;
        if (MapUtils.isEmpty(value)) {
            OrganizationOrder entity = get(orderId);
            if (entity != null) {
                status = entity.getStatus();
                orderType = entity.getType();
                sourceId = entity.getSourceId();
                amount = entity.getAmount();
            }
        } else {
            status = EnumHelper.parse(OrderStatus.values(), value.get("status"));
            orderType = EnumHelper.parse(OrderType.values(), value.get("orderType"));
            sourceId = Optional.ofNullable(value.get("sourceId")).filter(NumberUtils::isDigits).map(Long::parseLong).orElse(0L);
            amount = Optional.ofNullable(value.get("amount")).filter(NumberUtils::isDigits).map(Integer::parseInt).orElse(0);
        }
        if (status == null
                || orderType == null
                || sourceId == null
                || amount == null
                || orderType != confirmOrderType
                || !sourceId.equals(confirmSourceId)
                || !amount.equals(confirmOrderAmount)) {
            status = OrderStatus.failure;
        }
        return new OrderStatusDto(status);
    }

    /**
     * 查询订单状态，只是用订单号查询，不管订单的来源是什么
     */
    public OrderStatusDto orderStatus(Long orderId) {
        String orderKey = orderKey(orderId);
        String value = hashOpt().get(orderKey, "status");
        OrderStatus status = null;
        if (StringUtils.isEmpty(value)) {
            OrganizationOrder entity = get(orderId);
            if (entity != null) {
                status = entity.getStatus();
            }
        } else {
            status = EnumHelper.parse(OrderStatus.values(), value);
        }
        if (status == null) {
            status = OrderStatus.failure;
        }
        return new OrderStatusDto(status);
    }

    /**
     * 查询订单信息
     */
    public OrderInfoDto orderInfo(Long orderId) {
        return OrderInfoDto.build(require(orderId));
    }

    /**
     * 充值成功之后，处理订单
     * 1 从余额中扣除订单金额
     * 2 添加账单
     * 3 修改订单状态
     * 4 触发订单结束事件
     */
    public void placeOrderByRechargeSuccess(OrganizationOrder order, OrganizationRecharge recharge) {
        if (order != null && order.getType() != null) {
            Long orderId = order.getId();
            if (order.getStatus() != OrderStatus.init) {
                log.warn("企业订单{}已处理，当前状态为：{}", orderId, order.getStatus().name());
                return;
            }
            boolean success = false;
            try {
                int balance = organizationWalletService.balance(order.getOrgId());
                if (order.getType().isUseWallet()) {
                    // 充值金额记录
                    organizationBillService.addByOrderAndAmount(balance, order, recharge.getTotalAmount(), recharge.getType().getBillType());
                    // 订单金额需要从企业钱包中扣款
                    if (order.getAmountWallet() > 0) {
                        balance = organizationWalletService.consumer(order.getOrgId(), order.getAmountWallet());
                        organizationBillService.addByOrderAndAmount(balance, order, order.getAmountWallet(), BillType.wallet);
                    }
                } else {
                    organizationBillService.addByOrder(balance, order, recharge.getType().getBillType());
                }
                success = true;
            } catch (Throwable e) {
                log.error("企业订单{}付款失败{}", orderId, e.getMessage()); // 这里不抛出异常，只是设置订单失败了
            } finally {
                OrderStatus status = success ? OrderStatus.success : OrderStatus.failure;
                updateOrderStatusAndTriggerOrderCompletedEvent(order, status);
            }
        }
    }

    /**
     * 充值失败，订单状态也设置为失败，触发订单结束事件
     */
    public void placeOrderByRechargeFailure(OrganizationOrder order, OrganizationRecharge ignore) {
        if (order != null) {
            updateOrderStatusAndTriggerOrderCompletedEvent(order, OrderStatus.failure);
        }
    }

    /**
     * 充值过期，订单状态也设置为取消，触发订单结束事件
     */
    public void placeOrderByRechargeCancel(Long orderId, OrganizationRecharge ignore) {
        OrganizationOrder order = get(orderId);
        if (order != null) {
            updateOrderStatusAndTriggerOrderCompletedEvent(order, OrderStatus.cancel);
        }
    }

    /**
     * 修改订单状态，并发出订单结束事件
     */
    private void updateOrderStatusAndTriggerOrderCompletedEvent(OrganizationOrder order, OrderStatus status) {
        if (order.getStatus() != OrderStatus.init) {
            log.warn("企业订单{}已处理，当前状态为：{}", order.getId(), order.getStatus().name());
            return;
        }
        order.setStatus(status);
        repository.save(order);
        cacheOrderStatus(order.getId(), status);
        // order completed event (success|failure|cancel)
        requireOrder(order.getType()).orderCompleted(order);
        authEventTrigger.orderCompleted(order.getOrgId(), order.getCreateUserId(), order.getId(), order.getStatus().name());
    }

}
