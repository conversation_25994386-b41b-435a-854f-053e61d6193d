package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class SmsOrderRequestDto extends OrderRequestDto {

    @Min(10)
    @Max(100000)
    @NotNull
    @Schema(description = "充值数量", required = true)
    private Integer sms;


}
