package org.befun.auth.pay.property;


import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "befun.auth.recharge")
public class RechargeProperties {

    private Integer expireMinute = 30;
    private String mockPayUrl; // https://dev.xmplus.cn/api/auth/organization/wallet/recharge/mockPay?rechargeId=%d&rechargeType=%s
    private Map<RechargeType, Boolean> enabled = new HashMap<>();
}
