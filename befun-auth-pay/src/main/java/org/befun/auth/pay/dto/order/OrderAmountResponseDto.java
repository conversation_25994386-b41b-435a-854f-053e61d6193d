package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.PayType;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;
import org.befun.auth.utils.NumberHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;

@Getter
@Setter
@NoArgsConstructor
public abstract class OrderAmountResponseDto {

    @Schema(description = "付款金额（单位：分）")
    protected int costAmount;

    @Schema(description = "钱包余额（单位：分）")
    protected int walletBalance;

    @Schema(description = "钱包付款金额（单位：分）")
    protected int walletAmount = 0;

    @Schema(description = "需要充值金额")
    protected RechargeAmountDto recharge;

    @Schema(description = "可用的支付方式")
    protected List<PayType> availablePayTypes = new ArrayList<>();

    public void calcAmount(Integer costAmount,
                           Integer walletBalance,
                           RechargeType rechargeType,
                           Set<RechargeType> supportRechargeTypes,
                           BiFunction<Integer, RechargeType, RechargeAmountDto> getRechargeWechatAmount) {
        this.costAmount = NumberHelper.unbox(costAmount);
        this.walletBalance = NumberHelper.unbox(walletBalance);
        if (this.costAmount > 0) { // 如果付款金额大于0
            if (this.walletBalance >= this.costAmount) { // 钱包余额足够
                this.walletAmount = this.costAmount;
                this.recharge = new RechargeAmountDto(rechargeType);
                availablePayTypes.add(PayType.wallet);
            } else if (this.walletBalance <= 0) { // 钱包无余额
                this.walletAmount = 0;
                this.recharge = getRechargeWechatAmount.apply(this.costAmount, rechargeType);
                if (supportRechargeTypes.contains(RechargeType.recharge_wechat)) {
                    availablePayTypes.add(PayType.wechat);
                }
                if (supportRechargeTypes.contains(RechargeType.recharge_alipay)) {
                    availablePayTypes.add(PayType.alipay);
                }
            } else { // 钱包付款一部分，其他的微信支付
                this.walletAmount = this.walletBalance;
                this.recharge = getRechargeWechatAmount.apply(this.costAmount - this.walletAmount, rechargeType);
                if (supportRechargeTypes.contains(RechargeType.recharge_wechat)) {
                    availablePayTypes.add(PayType.mixed_wallet_wechat);
                }
                if (supportRechargeTypes.contains(RechargeType.recharge_alipay)) {
                    availablePayTypes.add(PayType.mixed_wallet_alipay);
                }
            }
        }
    }
}
