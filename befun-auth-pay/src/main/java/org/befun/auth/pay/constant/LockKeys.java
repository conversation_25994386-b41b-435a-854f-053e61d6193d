package org.befun.auth.pay.constant;

import org.befun.extension.constant.LockKey;

public enum LockKeys implements <PERSON><PERSON>ey {

    recharge_callback("lock:recharge_callback:%s"), // rechargeId
    order_refund("lock:order_refund:%d:%s"),        // orderId refundNo
    order_re_refund("lock:order_re_refund:%d:%d"),        // orderId refundOrderId
    recharge_refund("lock:recharge_refund:%d"),  // rechargeId
    recharge_refund_callback("lock:recharge_refund_callback:%s"),  // refundId
    ;
    private final boolean format;
    private final String placeholder;

    LockKeys(String placeholder) {
        this.format = true;
        this.placeholder = placeholder;
    }

    LockKeys(boolean format, String placeholder) {
        this.format = format;
        this.placeholder = placeholder;
    }

    @Override
    public String getPlaceholder() {
        return placeholder;
    }

    @Override
    public boolean isFormat() {
        return format;
    }
}
