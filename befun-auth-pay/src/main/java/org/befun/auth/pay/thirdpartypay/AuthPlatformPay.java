package org.befun.auth.pay.thirdpartypay;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.repository.OrganizationRechargeRepository;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class AuthPlatformPay implements IAuthThirdPartyPay {

    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public RechargeType type() {
        return RechargeType.recharge_platform;
    }

    private String platformRechargeKey(String rechargeId) {
        return String.format("recharge-platform:%s", rechargeId);
    }

    public void setPlatformRecharge(Long rechargeId) {
        stringRedisTemplate.opsForValue().set(platformRechargeKey(rechargeId.toString()), "1", Duration.ofHours(4));
    }

    public boolean isPlatformRecharge(String data) {
        if (data != null && data.length() < 64 && NumberUtils.isDigits(data)) {
            return Optional.ofNullable(stringRedisTemplate.hasKey(platformRechargeKey(data))).orElse(false);
        }
        return false;
    }

    @Override
    public String placeOrder(OrganizationRecharge recharge) {
        setPlatformRecharge(recharge.getId());
        return null;
    }

    @Override
    public boolean queryOrder(OrganizationRecharge recharge) {
        if (recharge.getStatus() == RechargeStatus.init) {
            String thirdpartyResponse = "平台支付";
            String thirdpartyStatus = "success";
            RechargeStatus status = RechargeStatus.success;
            String thirdpartyPayNo = "platform-" + RandomStringUtils.random(13, true, true);
            Date payTime = new Date();
            recharge.setThirtpartyPayNo(thirdpartyPayNo);
            recharge.setThirtpartyStatus(thirdpartyStatus);
            recharge.setThirtpartyResponse(thirdpartyResponse);
            recharge.setPayTime(payTime);
            recharge.setStatus(status);
        }
        return false;
    }

    @Override
    public String placeOrderCallback(Map<String, String> requestHeader, Object data) {
        try {
            String data1;
            if (!(data instanceof String) || !isPlatformRecharge((data1 = data.toString()))) {
                log.error("平台支付回调失败，支付类型不匹配");
                return null;
            }
            Long rechargeId = Long.valueOf(data1);
            OrganizationRecharge entity = organizationRechargeRepository.findById(rechargeId).orElse(null);
            if (entity != null) {
                if (entity.getType() == null || entity.getType() != type()) {
                    log.error("平台支付回调失败，支付类型不匹配");
                    return null;
                }
                return entity.getPayNo();
            }
        } catch (Throwable e) {
            log.error("平台支付回调失败", e);
            throw new BadRequestException("平台支付回调失败");
        }
        return null;
    }

    @Override
    public void closeOrder(OrganizationRecharge recharge) {
        throw new BadRequestException("平台支付暂不支持");
    }
}
