package org.befun.auth.pay.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_recharge_refund")
@EntityScopeStrategy
@DtoClass
public class OrganizationRechargeRefund extends EnterpriseEntity {

    @Schema(description = "企业订单id，如果是企业订单发起的充值则有此参数")
    @Column(name = "order_id")
    private Long orderId;

    @Schema(description = "订单退款id，如果是企业订单发起的充值则有此参数")
    @Column(name = "order_refund_id")
    private Long orderRefundId;

    @Schema(description = "充值id")
    @Column(name = "recharge_id")
    private Long rechargeId;

    @Schema(description = "企业退款单号")
    @Column(name = "refund_no")
    private String refundNo;

    @Schema(description = "第三方退款单号")
    @Column(name = "thirtparty_refund_no")
    private String thirtpartyRefundNo;

    @Schema(description = "退款名称（商品名称）")
    @Column(name = "title")
    private String title;

    @Schema(description = "退款渠道：微信，支付宝")
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private RechargeType type;

    @Schema(description = "退款金额")
    @Column(name = "amount")
    private Integer amount;

    @Schema(description = "退款状态：init 已发起 success 已成功 failure 已失败")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private RechargeRefundStatus status = RechargeRefundStatus.init;

    @Schema(description = "成功时间")
    @Column(name = "success_time")
    private Date successTime;

    @Schema(description = "原始请求内容")
    @Column(name = "thirtparty_request")
    private String thirtpartyRequest;

    @Schema(description = "原始响应退款状态")
    @Column(name = "thirtparty_status")
    private String thirtpartyStatus;

    @Schema(description = "原始响应内容")
    @Column(name = "thirtparty_response")
    private String thirtpartyResponse;

    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;
}