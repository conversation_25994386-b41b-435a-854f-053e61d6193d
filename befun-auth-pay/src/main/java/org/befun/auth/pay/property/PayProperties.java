package org.befun.auth.pay.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.constant.PayServiceRate;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "befun.auth.pay")
public class PayProperties {
    private Map<PayServiceRate, Float> serviceRate = new HashMap<>();
}
