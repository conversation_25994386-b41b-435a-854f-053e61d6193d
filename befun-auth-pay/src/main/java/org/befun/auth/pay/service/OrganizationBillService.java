package org.befun.auth.pay.service;

import org.befun.auth.pay.constant.BillType;
import org.befun.auth.pay.dto.ext.OrganizationBillExtDto;
import org.befun.auth.pay.entity.*;
import org.befun.auth.pay.repository.OrganizationBillRepository;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationWalletService;
import org.befun.auth.service.UserService;
import org.befun.core.dto.fillvalue.FillValue;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrganizationBillService extends BaseService<OrganizationBill, OrganizationBillDto, OrganizationBillRepository> {

    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationWalletService organizationWalletService;

    @Override
    public void afterMapToDto(List<OrganizationBill> entity, List<OrganizationBillDto> dto) {
        userService.fillValueById(dto, SimpleUser::fromUser, FillValue.create(OrganizationBillDto::getCreateUserId, OrganizationBillExtDto::setCreateUser));
    }

    public void addByRechargeRefund(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        OrganizationBill entity = new OrganizationBill();
        entity.setOrgId(recharge.getOrgId());
        entity.setTitle(refund.getTitle());
        entity.setType(recharge.getType().name());
        entity.setRechargeId(recharge.getId());
        entity.setAmount(refund.getAmount());
        entity.setBalance(organizationWalletService.balance(recharge.getOrgId()));
        entity.setPayType(recharge.getType().getBillType());
        entity.setCreateUserId(recharge.getCreateUserId());
        repository.save(entity);
    }

    public void addByRecharge(int afterBalance, OrganizationRecharge recharge) {
        OrganizationBill entity = new OrganizationBill();
        entity.setOrgId(recharge.getOrgId());
        entity.setTitle(recharge.getTitle());
        entity.setType(recharge.getType().name());
        entity.setRechargeId(recharge.getId());
        entity.setAmount(recharge.getTotalAmount());
        entity.setBalance(afterBalance);
        entity.setPayType(recharge.getType().getBillType());
        entity.setCreateUserId(recharge.getCreateUserId());
        repository.save(entity);
    }

    public void addByOrder(int afterBalance, OrganizationOrder order, BillType billType) {
        OrganizationBill entity = new OrganizationBill();
        entity.setOrgId(order.getOrgId());
        entity.setTitle(order.getTitle());
        entity.setType(order.getType().name());
        entity.setRechargeId(order.getId());
        entity.setAmount(order.getAmount());
        entity.setBalance(afterBalance);
        entity.setPayType(billType);
        entity.setCreateUserId(order.getCreateUserId());
        repository.save(entity);
    }

    public void addByOrderAndAmount(int afterBalance, OrganizationOrder order, Integer amount, BillType billType) {
        OrganizationBill entity = new OrganizationBill();
        entity.setOrgId(order.getOrgId());
        entity.setTitle(order.getTitle());
        entity.setType(order.getType().name());
        entity.setRechargeId(order.getId());
        entity.setAmount(amount);
        entity.setBalance(afterBalance);
        entity.setPayType(billType);
        entity.setCreateUserId(order.getCreateUserId());
        repository.save(entity);
    }
}
