package org.befun.auth.pay.repository;

import org.befun.auth.pay.entity.OrganizationOrderRefund;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

public interface OrganizationOrderRefundRepository extends ResourceRepository<OrganizationOrderRefund, Long> {
    boolean existsByOrderIdAndRefundNo(Long orderId, String refundNo);

    List<OrganizationOrderRefund> findByOrderId(Long orderId);


}
