package org.befun.auth.pay.service.order;

import org.befun.auth.constant.SmsRecordStatus;
import org.befun.auth.entity.OrganizationSmsRecord;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.dto.order.*;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.property.OrderSmsProperties;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SmsOrder extends OrderImpl<SmsOrderAmountRequestDto, SmsOrderAmountResponseDto, SmsOrderRequestDto, Boolean> {

    @Autowired
    private OrderSmsProperties orderSmsProperties;
    @Autowired
    private ISmsAccountService smsAccountService;

    @Override
    public OrderType type() {
        return OrderType.order_sms;
    }

    /**
     * 短信订单-计算金额
     */
    @Override
    public SmsOrderAmountResponseDto amount(Long orgId, Long userId, SmsOrderAmountRequestDto dto) {
        return new SmsOrderAmountResponseDto(
                dto.getSms(),
                dto.getRechargeType(),
                orderSmsProperties.getPrice(),
                getBalance(orgId),
                supportRechargeTypes(),
                (rechargeAmount, rechargeType) -> organizationRechargeService.rechargeAmount(rechargeAmount, rechargeType, PayServiceRate.order_sms));
    }

    /**
     * 短信订单-下单
     */
    @Override
    public PlaceOrderResponseDto place(Long orgId, Long userId, SmsOrderRequestDto dto) {
        // 检验扣费金额是否一致
        SmsOrderAmountResponseDto calcAmount = amount(orgId, userId, new SmsOrderAmountRequestDto(dto.getSms(), dto.getPayType().getRechargeType()));
        checkPlaceOrderAmount(calcAmount, dto);
        // 添加短信充值记录
        OrganizationSmsRecord record = organizationSmsRecordService.addByRecharge(orgId, userId, dto.getSms());
        // 构造下单参数
        PlaceOrderDto placeOrderDto = dto.buildPlaceOrderParam(record.getId());
        // 下单并支付
        return placeOrder(orgId, userId, OrderType.order_sms, PayServiceRate.order_sms, placeOrderDto, null);
    }

    /**
     * 短信订单-订单结束回调
     */
    @Override
    public void orderCompleted(OrganizationOrder order) {
        OrganizationSmsRecord record = organizationSmsRecordService.get(order.getSourceId());
        if (record == null || record.getStatus() != SmsRecordStatus.init) {
            return;
        }
        if (order.getStatus() == OrderStatus.success) {
            if (record.getAmount() > 0) {
                int balance = smsAccountService.recharge(order.getOrgId(), record.getAmount());
                organizationSmsRecordService.rechargeSuccess(record.getId(), balance);
            } else {
                organizationSmsRecordService.rechargeFailure(record.getId());
            }
        } else if (order.getStatus() == OrderStatus.failure) {
            organizationSmsRecordService.rechargeFailure(record.getId());
        } else if (order.getStatus() == OrderStatus.cancel) {
            organizationSmsRecordService.rechargeCancel(record.getId());
        }
    }
}
