package org.befun.auth.pay.property;


import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.AppVersion;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "befun.auth.order.version")
public class OrderVersionProperties {

    private int daysOfYear = 365;
    private int limitLeftDays = 60;
    private List<Version> supportVersions = new ArrayList<>();

    @Getter
    @Setter
    public static class Version {
        private AppVersion version;
        private int price;
    }

    public int getVersionPrice(AppVersion version) {
        return supportVersions.stream().filter(i -> i.version == version).findFirst().map(Version::getPrice).orElse(0);
    }
}
