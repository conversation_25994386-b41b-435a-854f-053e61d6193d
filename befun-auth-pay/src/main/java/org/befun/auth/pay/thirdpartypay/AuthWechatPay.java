package org.befun.auth.pay.thirdpartypay;

import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.repository.OrganizationRechargeRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.property.WeChatPayProperty;
import org.befun.extension.service.WeChatPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Stream;

@Slf4j
@Service
@ConditionalOnProperty(name = "befun.auth.recharge.enabled.recharge_wechat", havingValue = "true")
public class AuthWechatPay implements IAuthThirdPartyPay {

    @Autowired
    private WeChatPayProperty weChatPayProperty;
    @Autowired
    private WeChatPayService weChatPayService;
    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;

    @Override
    public RechargeType type() {
        return RechargeType.recharge_wechat;
    }

    @Override
    public String placeOrder(OrganizationRecharge recharge) {
        try {
            WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
            request.setAmount(buildAmount(recharge));
            if (StringUtils.isNotEmpty(recharge.getTitleAlias())) {
                request.setDescription(recharge.getTitleAlias());
            } else {
                request.setDescription(recharge.getTitle());
            }
            request.setOutTradeNo(recharge.getPayNo());
            request.setTimeExpire(DateHelper.formatOffset(recharge.getExpireTime()));
            WxPayUnifiedOrderV3Result result = weChatPayService.unifiedOrderV3(TradeTypeEnum.NATIVE, request);
            return result.getCodeUrl();
        } catch (WxPayException e) {
            log.error("微信支付下单失败", e);
            throw new BadRequestException("微信支付下单失败");
        }
    }

    private WxPayUnifiedOrderV3Request.Amount buildAmount(OrganizationRecharge recharge) {
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(recharge.getTotalAmount());
        amount.setCurrency("CNY");
        return amount;
    }

    public Map<String, String> parseRequestHeader(HttpServletRequest request) {
        Map<String, String> map = new HashMap<>();
        List.of("Mock-Id", "Mock-Status", "Request-ID", "Wechatpay-Nonce", "Wechatpay-Signature", "Wechatpay-Timestamp", "Wechatpay-Serial").forEach(k -> Optional.ofNullable(request.getHeader(k)).ifPresent(v -> map.put(k, v)));
        return map;
    }

    @Override
    public String placeOrderCallback(Map<String, String> requestHeader, Object data) {
        try {
            SignatureHeader header = getSignatureHeader(requestHeader);
            if (!(data instanceof String) || Stream.of(header.getSignature(), header.getNonce(), header.getTimeStamp(), header.getSerial()).anyMatch(StringUtils::isEmpty)) {
                log.error("微信支付回调失败，缺少签名参数:{}", JsonHelper.toJson(header));
                return null;
            }
            log.info("微信支付回调（原始数据）：header={}, body={}", JsonHelper.toJson(requestHeader), data);
            WxPayNotifyV3Result result = weChatPayService.parseOrderNotifyV3Result(data.toString(), header);
            if (result == null) {
                return null;
            }
            String thirdpartyResponse = JsonHelper.toJson(result.getResult());
            log.info("微信支付回调（解密数据）：{}", thirdpartyResponse);
            return result.getResult().getOutTradeNo();
        } catch (Throwable e) {
            log.error("微信支付回调失败", e);
            throw new BadRequestException("微信支付回调失败");
        }
    }

    private SignatureHeader getSignatureHeader(Map<String, String> requestHeader) {
        return SignatureHeader.builder().signature(requestHeader.get("Wechatpay-Signature")).nonce(requestHeader.get("Wechatpay-Nonce")).timeStamp(requestHeader.get("Wechatpay-Timestamp")).serial(requestHeader.get("Wechatpay-Serial")).build();
    }

    @Override
    public boolean queryOrder(OrganizationRecharge recharge) {
        try {
            if (recharge.getStatus() == RechargeStatus.init) {
                WxPayOrderQueryV3Result result = weChatPayService.queryOrderV3(null, recharge.getPayNo());
                String response = JsonHelper.toJson(result);
                String status = result.getTradeState();
                recharge.setThirtpartyResponse(response);
                recharge.setThirtpartyStatus(status);
                if ("SUCCESS".equalsIgnoreCase(status)) {
                    recharge.setThirtpartyPayNo(result.getTransactionId());
                    recharge.setPayTime(DateHelper.toDate(DateHelper.parseZoneDateTime(result.getSuccessTime()).toLocalDateTime()));
                    recharge.setStatus(RechargeStatus.success);
                }
                return true;
            }
        } catch (Throwable e) {
            log.error("微信支付查询订单失败", e);
            throw new BadRequestException("支付宝支付查询订单失败");
        }
        return false;
    }

    @Override
    public void closeOrder(OrganizationRecharge recharge) {
        try {
            // 如果第三方支付订单是未支付状态，则可以取消订单
            if ("NOTPAY".equals(recharge.getThirtpartyStatus())) {
                weChatPayService.closeOrderV3(recharge.getPayNo());
            }
        } catch (Throwable e) {
            log.error("微信支付取消订单失败", e);
            throw new BadRequestException("微信支付取消订单失败");
        }
    }


    @Override
    public boolean refundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        try {
            WxPayRefundV3Request request = buildRefundRequest(recharge, refund);
            WxPayRefundV3Result result = weChatPayService.refundV3(request);
            RechargeRefundStatus status = RechargeRefundStatus.init;
            if (result != null) {
                String thirdpartyResponse = JsonHelper.toJson(result);
                String thirdpartyStatus = result.getStatus();
                String thirdpartyRefundNo = result.getRefundId();
                Date successTime = null;
                if ("SUCCESS".equals(result.getStatus())) {
                    status = RechargeRefundStatus.success;
                    ZonedDateTime dateTime = DateHelper.parseZoneDateTime(result.getSuccessTime());
                    if (dateTime != null) {
                        successTime = DateHelper.toDate(dateTime.toLocalDateTime());
                    }
                }
                refund.setThirtpartyResponse(thirdpartyResponse);
                refund.setThirtpartyStatus(thirdpartyStatus);
                refund.setThirtpartyRefundNo(thirdpartyRefundNo);
                refund.setStatus(status);
                refund.setSuccessTime(successTime);
                return true;
            } else {
                refund.setStatus(status);
            }
        } catch (Throwable e) {
            log.error("微信支付退款失败", e);
            refund.setThirtpartyResponse(e.getMessage());
            refund.setStatus(RechargeRefundStatus.failure);
        }
        return false;
    }

    private WxPayRefundV3Request buildRefundRequest(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        WxPayRefundV3Request request = new WxPayRefundV3Request();
        request.setTransactionId(recharge.getThirtpartyPayNo());
        request.setOutTradeNo(recharge.getPayNo());
        request.setOutRefundNo(refund.getRefundNo());
        request.setReason(refund.getTitle());
        request.setNotifyUrl(weChatPayProperty.getPayRefundNotifyUrl());
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        amount.setRefund(refund.getAmount());
        amount.setTotal(recharge.getTotalAmount());
        amount.setCurrency("CNY");
        request.setAmount(amount);
        refund.setThirtpartyRequest(JsonHelper.toJson(request));
        return request;
    }

    @Override
    public boolean queryRefundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        try {
            WxPayRefundQueryV3Result result = weChatPayService.refundQueryV3(refund.getRefundNo());
            if (result != null) {
                refund.setThirtpartyRefundNo(result.getRefundId());
                refund.setThirtpartyResponse(JsonHelper.toJson(result));
                refund.setThirtpartyStatus(result.getStatus());
                if ("SUCCESS".equals(result.getStatus())) {
                    refund.setStatus(RechargeRefundStatus.success);
                    ZonedDateTime dateTime = DateHelper.parseZoneDateTime(result.getSuccessTime());
                    if (dateTime != null) {
                        refund.setSuccessTime(DateHelper.toDate(dateTime.toLocalDateTime()));
                    }
                    return true;
                } else if ("PROCESSING".equals(result.getStatus())) {
                    return false;
                } else {
                    refund.setStatus(RechargeRefundStatus.failure);
                    return true;
                }
            }
        } catch (Throwable e) {
            log.error("微信支付查询退款失败", e);
        }
        return false;
    }

    @Override
    public String refundOrderCallback(Map<String, String> requestHeader, Object refundData) {
        try {
            SignatureHeader header = getSignatureHeader(requestHeader);
            if (!(refundData instanceof String) || Stream.of(header.getSignature(), header.getNonce(), header.getTimeStamp(), header.getSerial()).anyMatch(StringUtils::isEmpty)) {
                log.error("微信支付退款回调失败，缺少签名参数:{}", JsonHelper.toJson(header));
                return null;
            }
            log.info("微信退款回调（原始数据）：header={}, body={}", JsonHelper.toJson(requestHeader), refundData);
            WxPayRefundNotifyV3Result response = weChatPayService.parseRefundNotifyV3Result(refundData.toString(), header);
            if (response != null) {
                WxPayRefundNotifyV3Result.DecryptNotifyResult result = response.getResult();
                if (result != null) {
                    return result.getOutRefundNo();
                }
            }
        } catch (Throwable e) {
            log.error("微信退款回调失败", e);
        }
        return null;
    }

}
