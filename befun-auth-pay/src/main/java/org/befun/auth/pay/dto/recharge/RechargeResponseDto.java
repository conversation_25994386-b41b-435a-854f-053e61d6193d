package org.befun.auth.pay.dto.recharge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RechargeResponseDto {

    @Schema(description = "充值id")
    private Long rechargeId;
    @Schema(description = "充值编号")
    private String payNo;
    @Schema(description = "充值名称")
    private String title;
    @Schema(description = "充值金额（包含服务费）（单位：分）")
    private Integer rechargeAmount;
    @Schema(description = "过期时间")
    private Date expireTime;
    @Schema(description = "充值渠道")
    private RechargeType type;
    @Schema(description = "支付二维码")
    private String payUrl;

    public static RechargeResponseDto buildFromRecharge(OrganizationRecharge recharge, Date expireTime, String payUrl) {
        return new RechargeResponseDto(recharge.getId(), recharge.getPayNo(), recharge.getTitle(), recharge.getTotalAmount(), expireTime, recharge.getType(), payUrl);
    }
}
