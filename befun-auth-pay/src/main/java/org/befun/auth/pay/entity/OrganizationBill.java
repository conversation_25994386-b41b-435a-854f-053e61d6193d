package org.befun.auth.pay.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.BillType;
import org.befun.auth.pay.constant.PayType;
import org.befun.auth.pay.dto.ext.OrganizationBillExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_bill")
@DtoClass(includeAllFields = true, superClass = OrganizationBillExtDto.class)
@EntityScopeStrategy
public class OrganizationBill extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "交易名称")
    @Column(name = "title")
    private String title;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "交易类型：短信消费 order_sms（支出），红包消费 order_red_packet（支出），企业升级 order_org_version（支出），余额充值 recharge_wechat|recharge_alipay（收入），红包返还 recharge_red_packet（收入）")
    @Column(name = "type")
    private String type;

    @Schema(description = "订单id：如果是订单产生的账单")
    @Column(name = "order_id")
    private Long orderId;

    @Schema(description = "充值id：如果是充值产生的账单")
    @Column(name = "recharge_id")
    private Long rechargeId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "交易金额（分）")
    @Column(name = "amount")
    private Integer amount;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "账户余额（分）")
    @Column(name = "balance")
    private Integer balance;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "支付方式")
    @Enumerated(EnumType.STRING)
    @Column(name = "pay_type")
    private BillType payType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;
}