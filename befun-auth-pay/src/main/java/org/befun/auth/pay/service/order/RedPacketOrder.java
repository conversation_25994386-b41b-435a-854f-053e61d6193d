package org.befun.auth.pay.service.order;

import org.apache.commons.lang3.StringUtils;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.dto.order.*;
import org.springframework.stereotype.Component;

@Component
public class RedPacketOrder extends OrderImpl<RedPacketOrderAmountRequestDto, RedPacketOrderAmountResponseDto, RedPacketOrderRequestDto, RedPacketOrderRefundRequestDto> {

    @Override
    public OrderType type() {
        return OrderType.order_red_packet;
    }

    @Override
    public RedPacketOrderAmountResponseDto amount(Long orgId, Long userId, RedPacketOrderAmountRequestDto a) {
        return new RedPacketOrderAmountResponseDto(
                a.getRedPacket(),
                getBalance(orgId),
                a.getRechargeType(),
                supportRechargeTypes(),
                (rechargeAmount, rechargeType) -> organizationRechargeService.rechargeAmount(rechargeAmount, rechargeType, PayServiceRate.order_red_packet));
    }

    @Override
    public PlaceOrderResponseDto place(Long orgId, Long userId, RedPacketOrderRequestDto dto) {
        // 检验扣费金额是否一致
        RedPacketOrderAmountResponseDto calcAmount = amount(orgId, userId, new RedPacketOrderAmountRequestDto(dto.getPayType().getRechargeType(), dto.getCostAmount()));
        checkPlaceOrderAmount(calcAmount, dto);
        // 同一个红包，只能有一个订单有效
        cancelOldOrderBySourceId(orgId, dto.getSourceId(), true);
        // 构造下单参数
        PlaceOrderDto placeOrderDto = dto.buildPlaceOrderParam(dto.getSourceId());
        // 下单并支付
        return placeOrder(orgId, userId, type(), PayServiceRate.order_red_packet, placeOrderDto, null);
    }

    @Override
    public Boolean refund(Long orgId, Long userId, RedPacketOrderRefundRequestDto refund) {
        String title = StringUtils.isEmpty(refund.getTitle()) ? "红包返还" : refund.getTitle();
        return refundOrder(orgId,
                userId,
                refund.getOrderId(),
                refund.getRedPacketId(),
                type(),
                refund.getRechargeAmount(),
                refund.getRefundNo(),
                title,
                (order, orderRefund) -> {
                    organizationRechargeService.rechargeByRedPacket(orgId, userId, refund.getRechargeAmount(), title);
                    if (order.getStatus() != OrderStatus.refund) {
                        order.setStatus(OrderStatus.refund);
                        repository.save(order);
                    }
                    return true;
                });
    }
}
