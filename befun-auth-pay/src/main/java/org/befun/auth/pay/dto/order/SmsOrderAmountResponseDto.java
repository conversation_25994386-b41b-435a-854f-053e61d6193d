package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;
import org.befun.auth.utils.NumberHelper;
import org.befun.core.exception.BadRequestException;

import java.util.Set;
import java.util.function.BiFunction;

@Getter
@Setter
@NoArgsConstructor
@Schema(description = "短信充值金额计算结果")
public class SmsOrderAmountResponseDto extends OrderAmountResponseDto {

    @Schema(description = "充值数量")
    private int sms;

    @Schema(description = "每条短信的价格（单位：分）: 8")
    private int price;

    public SmsOrderAmountResponseDto(Integer sms,
                                     RechargeType rechargeType,
                                     Integer price,
                                     Integer walletBalance,
                                     Set<RechargeType> supportRechargeTypes,
                                     BiFunction<Integer, RechargeType, RechargeAmountDto> getRechargeWechatAmount) {
        this.sms = NumberHelper.unbox(sms);
        if (sms < 10 || sms > 100000) {
            throw new BadRequestException("短信充值最低10条，最高不超过10万条");
        }
        this.price = NumberHelper.unbox(price);
        calcAmount(sms * price, walletBalance, rechargeType, supportRechargeTypes, getRechargeWechatAmount);
    }
}
