package org.befun.auth.pay.repository;

import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface OrganizationRechargeRefundRepository extends ResourceRepository<OrganizationRechargeRefund, Long> {

    OrganizationRechargeRefund findFirstByTypeAndRefundNo(RechargeType type, String thirtpartyRefundNo);

    OrganizationRechargeRefund findFirstByOrgIdAndOrderIdAndOrderRefundIdAndRechargeId(Long orgId, Long orderId, Long orderRefundId, Long rechargeId);

    List<OrganizationRechargeRefund> findByStatus(RechargeRefundStatus status, Pageable pageable);
}
