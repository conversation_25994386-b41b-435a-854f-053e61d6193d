package org.befun.auth.pay.dto.order;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.constant.PayType;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;
import org.befun.auth.utils.NumberHelper;

import java.util.Set;
import java.util.function.BiFunction;

@Getter
@Setter
public class AdminxChannelOrderAmountResponseDto extends OrderAmountResponseDto {


    public AdminxChannelOrderAmountResponseDto(int costAmount,
                                               RechargeType rechargeType,
                                               Integer walletBalance,
                                               Set<RechargeType> supportRechargeTypes,
                                               BiFunction<Integer, RechargeType, RechargeAmountDto> getRechargeWechatAmount) {
        this.costAmount = costAmount;
        this.walletBalance = NumberHelper.unbox(walletBalance);
        if (this.costAmount > 0) { // 如果付款金额大于0
            this.walletAmount = 0;
            this.recharge = getRechargeWechatAmount.apply(this.costAmount, rechargeType);
            if (supportRechargeTypes.contains(RechargeType.recharge_wechat)) {
                availablePayTypes.add(PayType.wechat);
            }
            if (supportRechargeTypes.contains(RechargeType.recharge_alipay)) {
                availablePayTypes.add(PayType.alipay);
            }
        }
    }
}
