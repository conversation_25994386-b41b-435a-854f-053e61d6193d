package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.dto.recharge.RechargeResponseDto;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PlaceOrderResponseDto {

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = "订单状态")
    private OrderStatus status;

    @Schema(description = "充值信息")
    private RechargeResponseDto recharge;
}
