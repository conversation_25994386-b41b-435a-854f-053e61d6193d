package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.pay.constant.PayType;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Getter
@Setter
public class VersionOrderRequestDto {

    @NotEmpty
    @Schema(description = "订单名称", required = true)
    private String title;

    @Schema(description = "充值名称，别名")
    private String titleAlias;

    @Min(1)
    @NotNull
    @Schema(description = "付款金额（单位：分", required = true)
    private Integer costAmount;

    @NotNull
    @Schema(description = "版本类型", required = true)
    private AppVersion version;

    @NotNull
    @Schema(description = "开始时间", required = true)
    private LocalDate startDate;

    @NotNull
    @Schema(description = "结束时间", required = true)
    private LocalDate endDate;

    @NotNull
    @Schema(description = "支付方式")
    private PayType payType;

    public PlaceOrderDto buildPlaceOrderParam(Long sourceId) {
        PlaceOrderDto dto = new PlaceOrderDto();
        dto.setSourceId(sourceId);
        dto.setPayType(payType);
        dto.setTitle(title);
        dto.setTitleAlias(titleAlias);
        dto.setCostAmount(costAmount);
        dto.setAmountWallet(0);
        dto.setAmountRecharge(costAmount);
        return dto;
    }

}
