package org.befun.auth.pay.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.pay.constant.PayType;
import org.befun.auth.pay.property.OrderVersionProperties;

import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@Schema(description = "版本金额计算结果")
public class VersionOrderAmountResponseDto {

    @Schema(description = "当前企业名称")
    private String currentOrgName;
    @Schema(description = "当前企业logo")
    private String currentOrgLogo;
    @Schema(description = "当前版本类型")
    private AppVersion currentVersion;
    @Schema(description = "当前版本价格")
    private int currentVersionPrice;
    @Schema(description = "当前版本开始时间")
    private LocalDate currentVersionStartDate;
    @Schema(description = "当前版本结束时间")
    private LocalDate currentVersionEndDate;

    @Schema(description = "每个版本的金额计算结果")
    private Map<AppVersion, AmountInfo> versions = new LinkedHashMap<>();

    @Schema(description = "可用的支付方式")
    private List<PayType> availablePayTypes = new ArrayList<>();

    public enum AmountType {
        upgrade, renew
    }

    @Getter
    @Setter
    public static class AmountInfo {
        @Schema(description = "版本类型")
        private AppVersion version;
        @Schema(description = "此版本是否可购买")
        private boolean available;
        @Schema(description = "用户数：1000000 不限")
        private int limitUsers;
        @Schema(description = "版本价格（单位：分）")
        private int price;
        @Schema(description = "开始时间")
        private LocalDate startDate;
        @Schema(description = "结束时间")
        private LocalDate endDate;
        @Schema(description = "付款金额类型：upgrade 升级；renew 续费")
        private AmountType amountType;
        @Schema(description = "购买时长单位: year | day")
        private String dateUnit;
        @Schema(description = "购买时长数量")
        private int dateRange;
        @Schema(description = "升级费用（单位：分）")
        private int upgradeAmount;
        @Schema(description = "升级费用描述")
        private String upgradeAmountDescribe;
        @Schema(description = "抵扣费用（单位：分）")
        private int deductionAmount;
        @Schema(description = "抵扣费用描述")
        private String deductionAmountDescribe;
        @Schema(description = "付款金额（单位：分）")
        private int costAmount;
    }

    public VersionOrderAmountResponseDto(
            String name,
            String logo,
            AppVersion currentVersion,
            LocalDate startDate, LocalDate endData,
            int currentVersionPrice,
            int daysOfYear, int limitLeftDays,
            List<OrderVersionProperties.Version> supportVersions) {
        this.currentOrgName = name;
        this.currentOrgLogo = logo;
        this.currentVersion = currentVersion;
        this.currentVersionPrice = currentVersionPrice;
        this.availablePayTypes.add(PayType.wechat);
        LocalDate now = LocalDate.now();
        this.currentVersionStartDate = startDate == null ? now.minusDays(1) : startDate;
        this.currentVersionEndDate = endData == null ? now.minusDays(1) : endData;
        supportVersions.forEach(target -> {
            AmountInfo amountInfo = getVersionAmount(currentVersion, endData, now, currentVersionPrice, daysOfYear, limitLeftDays, target);
            this.versions.put(target.getVersion(), amountInfo);
        });
    }

    public static AmountInfo getVersionAmount(
            AppVersion currentVersion,
            LocalDate end, LocalDate now,
            int currentVersionPrice,
            int daysOfYear,
            int limitLeftDays,
            OrderVersionProperties.Version target) {
        AmountInfo amountInfo = new AmountInfo();
        amountInfo.setVersion(target.getVersion());
        amountInfo.setLimitUsers(target.getVersion().getOptionalLimit().getChildUserLimit());
        amountInfo.setPrice(target.getPrice());
        if (target.getVersion().ordinal() < currentVersion.ordinal()) {
            // 不支持购买低版本
            amountInfo.setAvailable(false);
            return amountInfo;
        }
        amountInfo.setAvailable(true);
        if (freeVersion(currentVersion) || end == null || end.isBefore(now)) {
            // 是免费版本，或者版本时间已过(正常情况是，时间已过会转换为免费版，这里多加一层判断)，
            amountInfo.setStartDate(now);
            amountInfo.setEndDate(now.plusYears(1).minusDays(1)); // +1 年 ，不包括明年的今天
            amountInfo.setAmountType(AmountType.upgrade);
            amountInfo.setDateUnit("year");
            amountInfo.setDateRange(1);
            amountInfo.setCostAmount(target.getPrice());
        } else if (currentVersion == target.getVersion()) {
            // 同版本购买
            LocalDate start = end.plusDays(1);
            amountInfo.setStartDate(start);
            amountInfo.setEndDate(start.plusYears(1).minusDays(1));
            amountInfo.setAmountType(AmountType.renew);
            amountInfo.setDateUnit("year");
            amountInfo.setDateRange(1);
            amountInfo.setCostAmount(target.getPrice());
        } else {
            // 购买高版本
            amountInfo.setAmountType(AmountType.upgrade);
            amountInfo.setStartDate(now);
            // 当前版本的剩余天数
            long upgradeDays = days(now, end);
            long price = target.getPrice();
            if (upgradeDays >= limitLeftDays) {
                // 剩余天数大于指定的天数(60)，升级，结束时间不变
                // 升级费用描述 (50000/365)元/天x255天
                amountInfo.setEndDate(end);
                amountInfo.setDateUnit("day");
                amountInfo.setDateRange((int) upgradeDays);
                amountInfo.setUpgradeAmount((int) (price * upgradeDays / daysOfYear));
                amountInfo.setUpgradeAmountDescribe(String.format("(%d/%d)元/天x%d天", price / 100, daysOfYear, upgradeDays));
            } else {
                // 剩余天数不足指定的天数(60)，升级 + 续费
                // 升级费用描述 (50000/365)元/天x255天 + 50000元/年x1年
                amountInfo.setEndDate(end.plusYears(1).minusDays(1));
                amountInfo.setDateUnit("day");
                amountInfo.setDateRange(days(amountInfo.getStartDate(), amountInfo.getEndDate()));
                amountInfo.setUpgradeAmount((int) (price * upgradeDays / daysOfYear + price));
                amountInfo.setUpgradeAmountDescribe(String.format("(%d/%d)元/天x%d天 + %d元/年", price / 100, daysOfYear, upgradeDays, price / 100));
            }
            // 抵扣费用描述 (1699/365)元/天x255天
            amountInfo.setDeductionAmount((int) (currentVersionPrice * upgradeDays / daysOfYear));
            amountInfo.setDeductionAmountDescribe(String.format("(%d/%d)元/天x%d天", currentVersionPrice / 100, daysOfYear, upgradeDays));
            amountInfo.setCostAmount(amountInfo.getUpgradeAmount() - amountInfo.getDeductionAmount());
        }
        return amountInfo;
    }

    private static int days(LocalDate start, LocalDate end) {
        return (int) Duration.between(start.atStartOfDay(), end.atStartOfDay()).toDays() + 1;
    }

    private static boolean freeVersion(AppVersion currentVersion) {
        return currentVersion == null || currentVersion == AppVersion.EMPTY || currentVersion == AppVersion.FREE;
    }

}
