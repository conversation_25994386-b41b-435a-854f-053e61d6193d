package org.befun.auth.pay.thirdpartypay;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.property.RechargeProperties;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.repository.OrganizationRechargeRepository;
import org.befun.auth.utils.GeneratorHelper;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class AuthMockPay implements IAuthThirdPartyPay {

    @Autowired
    private RechargeProperties rechargeProperties;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;
    @Autowired
    private OrganizationRechargeRefundRepository organizationRechargeRefundRepository;

    @Override
    public RechargeType type() {
        return null;
    }

    public boolean isMockRecharge(Long orgId) {
        return Optional.ofNullable(stringRedisTemplate.opsForSet().isMember("recharge-mock-org", orgId.toString())).orElse(false);
    }

    private String mockRechargeKey(String rechargeId) {
        return String.format("recharge-mock:%s", rechargeId);
    }

    public void setMockRecharge(Long rechargeId) {
        stringRedisTemplate.opsForValue().set(mockRechargeKey(rechargeId.toString()), "1", Duration.ofHours(4));
    }

    public boolean isMockRecharge(Object data) {
        if (data instanceof String && NumberUtils.isDigits(data.toString())) {
            return Optional.ofNullable(stringRedisTemplate.hasKey(mockRechargeKey(data.toString()))).orElse(false);
        }
        return false;
    }

    public boolean isMockRecharge(OrganizationRecharge recharge) {
        return recharge != null && recharge.getMockRecharge() != null && recharge.getMockRecharge() == 1;
    }

    @Override
    public String placeOrder(OrganizationRecharge recharge) {
        setMockRecharge(recharge.getId());
        return String.format(rechargeProperties.getMockPayUrl(), recharge.getId(), recharge.getType());
    }

    @Override
    public boolean queryOrder(OrganizationRecharge recharge) {
        String thirdpartyResponse = "模拟支付";
        RechargeStatus status = RechargeStatus.failure;
        String thirdpartyPayNo = "mock-" + RandomStringUtils.random(27, true, true);
        Date payTime = null;
        if ("success".equals(recharge.getThirtpartyStatus())) {
            status = RechargeStatus.success;
            payTime = new Date();
        }
        recharge.setThirtpartyPayNo(thirdpartyPayNo);
        recharge.setThirtpartyResponse(thirdpartyResponse);
        recharge.setPayTime(payTime);
        recharge.setStatus(status);
        return true;
    }

    @Override
    public String placeOrderCallback(Map<String, String> requestHeader, Object data) {
        try {
            Long rechargeId = Long.valueOf(data.toString());
            OrganizationRecharge entity = organizationRechargeRepository.findById(rechargeId).orElse(null);
            if (entity != null) {
                if (!isMockRecharge(entity) && entity.getType() == null || !entity.getType().isEnableMock()) {
                    log.error("模拟支付回调失败，支付类型不支持模拟回调");
                    return null;
                }
                if (entity.getStatus() == RechargeStatus.init) {
                    String thirdpartyStatus;
                    if (Boolean.parseBoolean(requestHeader.getOrDefault("mockPaySuccess", "true"))) {
                        thirdpartyStatus = "success";
                    } else {
                        thirdpartyStatus = "failure";
                    }
                    entity.setThirtpartyStatus(thirdpartyStatus);
                    organizationRechargeRepository.save(entity);
                    return entity.getPayNo();
                } else if (entity.getStatus() == RechargeStatus.failure) {
                    throw new BadRequestException("模拟支付已失败");
                } else if (entity.getStatus() == RechargeStatus.success) {
                    throw new BadRequestException("模拟支付已完成");
                } else if (entity.getStatus() == RechargeStatus.cancel) {
                    throw new BadRequestException("模拟支付已取消");
                }
            }
        } catch (Throwable e) {
            log.error("模拟支付回调失败", e);
            throw e;
        }
        return null;
    }

    @Override
    public void closeOrder(OrganizationRecharge recharge) {

    }

    @Override
    public boolean refundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        refund.setStatus(RechargeRefundStatus.init);
        refund.setThirtpartyRefundNo("mock-" + GeneratorHelper.generatorPayNo());
        return true;
    }

    @Override
    public boolean queryRefundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        String thirdpartyResponse = "模拟退款回调";
        RechargeRefundStatus status;
        Date successTime = null;
        if ("SUCCESS".equalsIgnoreCase(refund.getThirtpartyStatus())) {
            status = RechargeRefundStatus.success;
            successTime = new Date();
        } else {
            status = RechargeRefundStatus.failure;
        }
        refund.setThirtpartyResponse(thirdpartyResponse);
        refund.setStatus(status);
        refund.setSuccessTime(successTime);
        organizationRechargeRefundRepository.save(refund);
        return true;
    }

    @Override
    public String refundOrderCallback(Map<String, String> requestHeader, Object refundData) {
        try {
            Long rechargeId = Long.valueOf(refundData.toString());
            OrganizationRecharge entity = organizationRechargeRepository.findById(rechargeId).orElse(null);
            if (entity != null) {
                if (!isMockRecharge(entity) && entity.getType() == null || !entity.getType().isEnableMock()) {
                    log.error("模拟退款回调失败，支付类型不支持模拟回调");
                    return null;
                }
                String refundNo = requestHeader.get("Mock-Id");
                String thirdpartyStatus = requestHeader.get("Mock-Status");
                OrganizationRechargeRefund refund = organizationRechargeRefundRepository.findFirstByTypeAndRefundNo(entity.getType(), refundNo);
                if (refund != null) {
                    refund.setThirtpartyStatus(thirdpartyStatus);
                    organizationRechargeRefundRepository.save(refund);
                }
                return refundNo;
            }
        } catch (Throwable e) {
            log.error("微信退款回调失败", e);
        }
        return null;
    }
}
