package org.befun.auth.pay.service.order;

import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.PlaceOrderResponseDto;
import org.befun.auth.pay.entity.OrganizationOrder;

public interface IOrder<A1, A2, O, F> {

    OrderType type();

    A2 amount(Long orgId, Long userId, A1 a);

    PlaceOrderResponseDto place(Long orgId, Long userId, O o);

    default void orderCompleted(OrganizationOrder order) {
    }

    boolean cancel(Long orgId, Long userId, Long orderId);

    /**
     * 订单-订单退还
     *
     * @return true 退还成功 false 退款中，需要去支付渠道退款
     */
    default Boolean refund(Long orgId, Long userId, F refund) {
        return null;
    }

    /**
     * 订单-订单退还失败，重新退还
     */
    default Boolean reRefund(Long orgId, Long userId, Long orderRefundId) {
        return null;
    }
}
