package org.befun.auth.pay.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.pay.entity.OrganizationBill;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public abstract class OrganizationBillExtDto extends BaseEntityDTO<OrganizationBill> {

    public OrganizationBillExtDto(OrganizationBill entity) {
        super(entity);
    }

    public OrganizationBillExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    private SimpleUser createUser;

}
