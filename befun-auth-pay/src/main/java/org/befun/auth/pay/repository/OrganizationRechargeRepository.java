package org.befun.auth.pay.repository;

import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.core.repository.ResourceRepository;

import java.util.Optional;

public interface OrganizationRechargeRepository extends ResourceRepository<OrganizationRecharge, Long> {
    Optional<OrganizationRecharge> findFirstByTypeAndPayNo(RechargeType type, String payNo);


}
