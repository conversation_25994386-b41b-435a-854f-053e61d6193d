package org.befun.auth.pay.service.order;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.AiPointRecordStatus;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.dto.order.*;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.property.OrderAiPointProperties;
import org.befun.auth.service.OrganizationAIPointService;
import org.befun.auth.service.OrganizationAiPointRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AiPointOrder extends OrderImpl<AiPointOrderAmountRequestDto, AiPointOrderAmountResponseDto, AiPointOrderRequestDto, Boolean> {

    @Autowired
    private OrderAiPointProperties orderAiPointProperties;
    @Autowired
    private OrganizationAIPointService organizationAIPointService;
    @Autowired
    private OrganizationAiPointRecordService organizationAiPointRecordService;

    @Override
    public OrderType type() {
        return OrderType.order_ai_point;
    }

    @Override
    public AiPointOrderAmountResponseDto amount(Long orgId, Long userId, AiPointOrderAmountRequestDto data) {
        return new AiPointOrderAmountResponseDto(
                data.getAiPoint(),
                data.getRechargeType(),
                orderAiPointProperties.getPrice(),
                getBalance(orgId),
                supportRechargeTypes(),
                (rechargeAmount, rechargeType) -> organizationRechargeService.rechargeAmount(rechargeAmount, rechargeType, PayServiceRate.order_ai_point));
    }

    @Override
    public PlaceOrderResponseDto place(Long orgId, Long userId, AiPointOrderRequestDto dto) {
        // 检验扣费金额是否一致
        AiPointOrderAmountResponseDto calcAmount = amount(orgId, userId, new AiPointOrderAmountRequestDto(dto.getAiPoint(), dto.getPayType().getRechargeType()));
        checkPlaceOrderAmount(calcAmount, dto);
        // 添加 ai 点数 充值记录
        OrganizationAiPointRecord record = organizationAiPointRecordService.addByRecharge(orgId, userId, dto.getAiPoint());
        // 构造下单参数
        PlaceOrderDto placeOrderDto = dto.buildPlaceOrderParam(record.getId());
        // 下单并支付
        return placeOrder(orgId, userId, OrderType.order_ai_point, PayServiceRate.order_ai_point, placeOrderDto, null);
    }

    /**
     * AI点数订单-订单结束回调
     * 如果订单成功了，添加点数，添加购买记录
     */
    @Override
    public void orderCompleted(OrganizationOrder order) {
        OrganizationAiPointRecord record = organizationAiPointRecordService.get(order.getSourceId());
        if (record == null || record.getStatus() != AiPointRecordStatus.init) {
            return;
        }
        if (order.getStatus() == OrderStatus.success) {
            if (record.getAmount() > 0) {
                organizationAIPointService.recharge(order.getOrgId(), record.getAmount());
                organizationAiPointRecordService.rechargeSuccess(record.getId());
            } else {
                organizationAiPointRecordService.rechargeFailure(record.getId());
            }
        } else if (order.getStatus() == OrderStatus.failure) {
            organizationAiPointRecordService.rechargeFailure(record.getId());
        } else if (order.getStatus() == OrderStatus.cancel) {
            organizationAiPointRecordService.rechargeCancel(record.getId());
        }
    }


}
