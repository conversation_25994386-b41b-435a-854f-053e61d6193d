package org.befun.auth.pay.repository;

import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

public interface OrganizationOrderRepository extends ResourceRepository<OrganizationOrder, Long> {
    List<OrganizationOrder> findByOrgIdAndTypeAndSourceId(Long orgId, OrderType type, Long sourceId);
    OrganizationOrder findByIdAndOrgIdAndType(Long id, Long orgId, OrderType type);


}
