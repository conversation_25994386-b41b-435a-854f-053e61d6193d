package org.befun.auth.pay.service.order;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.dto.order.*;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
public class AdminxChannelOrder extends OrderImpl<AdminxChannelOrderAmountRequestDto, AdminxChannelOrderAmountResponseDto, AdminxChannelOrderRequestDto, AdminxChannelOrderRefundRequestDto> {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;

    @Override
    public OrderType type() {
        return OrderType.order_adminx_channel;
    }

    @Override
    public AdminxChannelOrderAmountResponseDto amount(Long orgId, Long userId, AdminxChannelOrderAmountRequestDto request) {
        int amount = requireAmountByChannel(orgId, userId, request.getChannelId());
        return new AdminxChannelOrderAmountResponseDto(
                amount,
                request.getRechargeType(),
                getBalance(orgId),
                supportRechargeTypes(),
                (rechargeAmount, rechargeType) -> organizationRechargeService.rechargeAmount(rechargeAmount, rechargeType, PayServiceRate.order_adminx_channel));
    }

    @Override
    public PlaceOrderResponseDto place(Long orgId, Long userId, AdminxChannelOrderRequestDto dto) {
        // 检验扣费金额是否一致
        AdminxChannelOrderAmountResponseDto calcAmount = amount(orgId, userId, new AdminxChannelOrderAmountRequestDto(dto.getChannelId(), dto.getPayType().getRechargeType()));
        checkPlaceOrderAmount(calcAmount, dto);
        // 关闭旧的订单
        cancelOldOrderBySourceId(orgId, dto.getChannelId(), true);
        // 构造下单参数
        PlaceOrderDto placeOrderDto = dto.buildPlaceOrderParam(dto.getChannelId());
        // 下单并支付
        PlaceOrderResponseDto order = placeOrder(orgId, userId, type(), PayServiceRate.order_adminx_channel, placeOrderDto, null);
        // 订单id 写入渠道
        updateOrderIdInChannel(dto.getChannelId(), order.getOrderId());
        return order;
    }

    @Override
    public Boolean refund(Long orgId, Long userId, AdminxChannelOrderRefundRequestDto refund) {
        String title = StringUtils.isEmpty(refund.getTitle()) ? "" : refund.getTitle();
        return refundOrder(orgId, userId, refund.getOrderId(),
                refund.getChannelId(), type(), refund.getRefundAmount(), refund.getRefundNo(), title,
                (order, orderRefund) -> {
                    // 订单钱包支付金额
                    int orderAmountWallet = order.getAmountWallet() == null ? 0 : order.getAmountWallet();
                    // 订单充值支付金额
                    int orderAmountRecharge = order.getAmountRecharge() == null ? 0 : order.getAmountRecharge();
                    // 退款金额和钱包支付金额的差值
                    int refundAmountRecharge = orderRefund.getAmountRefund() - orderAmountWallet;
                    if (orderAmountWallet > 0 && refundAmountRecharge <= 0) {
                        // 如果退款金额小于钱包支付的金额，直接退钱包
                        organizationRechargeService.rechargeByPlatform(orgId, userId, orderRefund.getAmountRefund(), title);
                        order.setStatus(OrderStatus.refund);
                        repository.save(order);
                        return true;
                    } else if (refundAmountRecharge > 0 && refundAmountRecharge <= orderAmountRecharge
                            && order.getRechargeId() != null && order.getRechargeId() > 0) {
                        // 如果退款金额大于钱包支付的金额，则先从第三方开始退款（钱包支付金额不足的差值部分），退款成功后，再退钱包的支付金额
                        OrganizationRechargeRefund rechargeRefund = organizationRechargeService.refund(orgId, userId, order.getRechargeId(), title,
                                refundAmountRecharge, order.getId(), orderRefund.getId());
                        if (rechargeRefund.getStatus() == RechargeRefundStatus.failure) {
                            order.setStatus(OrderStatus.refund_failure);
                            repository.save(order);
                            return null;
                        } else if (rechargeRefund.getStatus() == RechargeRefundStatus.success) {
                            order.setStatus(OrderStatus.refund);
                            repository.save(order);
                            return true;
                        } else if (rechargeRefund.getStatus() == RechargeRefundStatus.init) {
                            order.setStatus(OrderStatus.refund_pending);
                            repository.save(order);
                            authEventTrigger.rechargeRefundQuery(orgId, userId, rechargeRefund.getRechargeId(), rechargeRefund.getId(), Duration.ofSeconds(10));
                        }
                        return false;
                    } else {
                        throw new BadRequestException(String.format("退款金额错误，orderId=%s, refundAmount=%s", order.getId(), orderRefund.getAmountRefund()));
                    }
                });
    }

    private int requireAmountByChannel(Long orgId, Long userId, Long channelId) {
        String sql = "select sc.id, sc.order_id orderId, sc.order_amount orderAmount from survey_channel sc " +
                " inner join survey s on s.id=sc.s_id " +
                " where s.org_id = " + orgId + " and sc.id = " + channelId +
                " and sc.type = 5 and sc.deleted = 0 limit 1";
        ChannelOrderInfo orderInfo = nativeSqlHelper.queryObject(sql, ChannelOrderInfo.class);
        if (orderInfo != null) {
            if (orderInfo.getOrderAmount() == null || orderInfo.getOrderAmount() < 0) {
                throw new BadRequestException("订单未报价");
            } else {
                return orderInfo.getOrderAmount();
            }
        }
        throw new BadRequestException("订单不存在");
    }

    private void updateOrderIdInChannel(Long channelId, Long orderId) {
        String sql = "update survey_channel set `status` = 4, order_id = " + orderId + " where id = " + channelId;
        nativeSqlHelper.update(sql);
    }

    @Getter
    @Setter
    public static class ChannelOrderInfo {
        private Long id;
        private Long orderId;
        private Integer orderAmount;
    }
}
