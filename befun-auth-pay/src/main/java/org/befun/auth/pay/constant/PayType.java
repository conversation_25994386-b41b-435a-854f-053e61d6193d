package org.befun.auth.pay.constant;

import lombok.Getter;

@Getter
public enum PayType {

    wallet(false, null),
    wechat(true, RechargeType.recharge_wechat),
    alipay(true, RechargeType.recharge_alipay),
    mixed_wallet_wechat(true, RechargeType.recharge_wechat),
    mixed_wallet_alipay(true, RechargeType.recharge_alipay),

    ;

    private final boolean recharge;
    private final RechargeType rechargeType;

    PayType(boolean recharge, RechargeType rechargeType) {
        this.recharge = recharge;
        this.rechargeType = rechargeType;
    }
}
