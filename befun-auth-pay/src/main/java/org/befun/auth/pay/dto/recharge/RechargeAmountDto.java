package org.befun.auth.pay.dto.recharge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.core.exception.BadRequestException;

import javax.validation.constraints.NotNull;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
public class RechargeAmountDto {

    @NotNull
    @Schema(description = "充值方式")
    private RechargeType rechargeType;

    @NotNull
    @Schema(description = "服务费率")
    private Float serviceRate;

    @NotNull
    @Schema(description = "充值金额")
    private Integer rechargeAmount;

    @NotNull
    @Schema(description = "服务费（单位：分）")
    private Integer serviceAmount;

    @NotNull
    @Schema(description = "总金额（单位：分）")
    private Integer totalAmount;

    public RechargeAmountDto(RechargeType rechargeType) {
        this.rechargeType = rechargeType;
        this.serviceRate = 0f;
        this.rechargeAmount = 0;
        this.serviceAmount = 0;
        this.totalAmount = 0;
    }

    public RechargeAmountDto(RechargeType rechargeType, Float serviceRate, Integer rechargeAmount) {
        checkRechargeAmount(rechargeType, rechargeAmount, serviceRate);
        this.rechargeType = rechargeType;
        this.serviceRate = serviceRate == null ? 0f : serviceRate;
        this.rechargeAmount = rechargeAmount;
        this.serviceAmount = Float.valueOf(100 * this.serviceRate).intValue() * this.rechargeAmount / 100;
        this.totalAmount = this.rechargeAmount + this.serviceAmount;
    }

    private void checkRechargeAmount(RechargeType rechargeType, Integer rechargeAmount, Float serviceRate) {
        if (rechargeAmount == null || rechargeAmount <= 0 || rechargeAmount > 10000000) {
            throw new BadRequestException("充值金额必须大于0，小于100000");
        }
//        if (serviceRate != null && serviceRate > 0 && rechargeType.isCheckMinimumAmount() && rechargeAmount % 100 != 0) {
//            // 有服务费的时候，需要充值金额为100的倍数
//            throw new BadRequestException("充值金额最小单位为元");
//        }
    }

    public boolean compare(RechargeAmountDto rechargeAmountDto) {
        if (rechargeType == null || rechargeAmountDto == null || rechargeAmountDto.rechargeType == null) {
            return false;
        }
        return this.rechargeType == rechargeAmountDto.rechargeType
                && Objects.equals(this.serviceRate, rechargeAmountDto.serviceRate)
                && Objects.equals(this.rechargeAmount, rechargeAmountDto.rechargeAmount)
                && Objects.equals(this.serviceAmount, rechargeAmountDto.serviceAmount)
                && Objects.equals(this.totalAmount, rechargeAmountDto.totalAmount);
    }
}
