package org.befun.auth.pay.dto.recharge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeType;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
public class RechargeRequestDto {

    @NotNull
    @Schema(description = "充值方式", required = true)
    private RechargeType rechargeType;

    @Min(1)
    @NotNull
    @Schema(description = "充值金额（单位：分）", required = true)
    private Integer rechargeAmount;

    @NotEmpty
    @Schema(description = "充值名称", required = true)
    private String title = "账户充值";

    @Schema(description = "充值名称，别名")
    private String titleAlias;

    public RechargeRequestDto(RechargeType rechargeType, Integer rechargeAmount, String title) {
        this.rechargeType = rechargeType;
        this.rechargeAmount = rechargeAmount;
        this.title = title;
    }

    public RechargeRequestDto(RechargeType rechargeType, Integer rechargeAmount, String title, String titleAlias) {
        this.rechargeType = rechargeType;
        this.rechargeAmount = rechargeAmount;
        this.title = title;
        this.titleAlias = titleAlias;
    }
}
