package org.befun.auth.pay.thirdpartypay;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

public interface IAuthThirdPartyPay {

    RechargeType type();

    /**
     * 下单
     */
    String placeOrder(OrganizationRecharge recharge);

    /**
     * 充值回调，解析回调中 订单id
     */
    String placeOrderCallback(Map<String, String> requestHeader, Object data);

    default boolean queryOrder(OrganizationRecharge recharge) {
        return false;
    }

    void closeOrder(OrganizationRecharge recharge);

    /**
     * 发起退款的时候，不处理是否成功，等到接受到回调通知，或者定时扫描退款中的记录时，再处理退款结果
     */
    default boolean refundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        return false;
    }

    /**
     * @return 查到了结果：true；没有查到结果 false
     */
    default boolean queryRefundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        return false;
    }

    default String refundOrderCallback(Map<String, String> requestHeader, Object refundData) {
        return null;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class PlaceOrderCallback {
        private OrganizationRecharge recharge;
        private String thirdpartyResponse;
        private String thirdpartyStatus;
        private String thirdpartyPayNo;
        private Date payTime;
        private RechargeStatus status;

        public void updateRecharge() {
            recharge.setThirtpartyResponse(thirdpartyResponse);
            recharge.setThirtpartyStatus(thirdpartyStatus);
            recharge.setThirtpartyPayNo(thirdpartyPayNo);
            recharge.setPayTime(payTime);
            recharge.setStatus(status);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class RefundOrderCallback {
        private String refundNo;
        private String thirdpartyResponse;
        private String thirdpartyStatus;
        private RechargeRefundStatus status;
        private Date successTime;

    }
}
