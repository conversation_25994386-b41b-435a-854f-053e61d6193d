package org.befun.auth.pay.thirdpartypay;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.repository.OrganizationRechargeRepository;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

@Slf4j
@Service
public class AuthRedPacketPay implements IAuthThirdPartyPay {

    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;

    @Override
    public RechargeType type() {
        return RechargeType.recharge_red_packet;
    }

    @Override
    public String placeOrder(OrganizationRecharge recharge) {
        return null;
    }

    @Override
    public boolean queryOrder(OrganizationRecharge recharge) {
        if (recharge.getStatus() == RechargeStatus.init) {
            String thirdpartyResponse = "红包返还支付";
            String thirdpartyStatus = "success";
            RechargeStatus status = RechargeStatus.success;
            String thirdpartyPayNo = "red-packet-" + RandomStringUtils.random(11, true, true);
            Date payTime = new Date();
            recharge.setThirtpartyPayNo(thirdpartyPayNo);
            recharge.setThirtpartyStatus(thirdpartyStatus);
            recharge.setThirtpartyResponse(thirdpartyResponse);
            recharge.setPayTime(payTime);
            recharge.setStatus(status);
            return true;
        }
        return false;
    }

    @Override
    public String placeOrderCallback(Map<String, String> requestHeader, Object data) {
        try {
            if (data instanceof String) {
                Long rechargeId = Long.valueOf(data.toString());
                OrganizationRecharge entity = organizationRechargeRepository.findById(rechargeId).orElse(null);
                if (entity != null) {
                    if (entity.getType() == null || entity.getType() != type()) {
                        log.error("红包返还支付回调失败，支付类型不匹配");
                        return null;
                    }
                    return entity.getPayNo();
                }
            }
        } catch (Throwable e) {
            log.error("红包返还支付回调失败", e);
            throw new BadRequestException("红包返还支付回调失败");
        }
        return null;
    }

    @Override
    public void closeOrder(OrganizationRecharge recharge) {

    }
}
