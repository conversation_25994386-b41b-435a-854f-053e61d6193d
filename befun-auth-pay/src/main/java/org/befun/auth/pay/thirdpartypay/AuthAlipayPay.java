package org.befun.auth.pay.thirdpartypay;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.repository.OrganizationRechargeRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.AlipayPlaceOrderResponseDto;
import org.befun.extension.dto.AlipayQueryOrderResponseDto;
import org.befun.extension.dto.AlipayQueryRefundOrderResponseDto;
import org.befun.extension.dto.AlipayRefundOrderResponseDto;
import org.befun.extension.service.AlipayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@ConditionalOnProperty(name = "befun.auth.recharge.enabled.recharge_alipay", havingValue = "true")
public class AuthAlipayPay implements IAuthThirdPartyPay {

    @Autowired
    private AlipayService alipayService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;

    @Override
    public RechargeType type() {
        return RechargeType.recharge_alipay;
    }

    public boolean isSandboxRecharge(Long orgId) {
        return Optional.ofNullable(stringRedisTemplate.opsForSet().isMember("recharge-sandbox-org", orgId.toString())).orElse(false);
    }

    @Override
    public String placeOrder(OrganizationRecharge recharge) {
        AlipayPlaceOrderResponseDto response = alipayService.webPlaceOrder(
                isSandboxRecharge(recharge.getOrgId()),
                recharge.getPayNo(),
                recharge.getRechargeAmount(),
                recharge.getTitleAlias(),
                recharge.getExpireTime(),
                ""
        );
        return response.getPayUrl();
    }

    @Override
    @SuppressWarnings({"rawtypes", "unchecked"})
    public String placeOrderCallback(Map<String, String> requestHeader, Object data) {
        try {
            Map<String, String> callbackData = new HashMap<>();
            if (!(data instanceof Map)) {
                log.error("支付宝支付回调失败，参数错误:{}", JsonHelper.toJson(data));
                return null;
            } else {
                ((Map) data).forEach((k, v) -> {
                    if (k instanceof String && v instanceof String) {
                        callbackData.put(k.toString(), v.toString());
                    }
                });
            }
            boolean sandbox = requestHeader.containsKey("sandbox");
            log.info("支付宝支付回调（原始数据）：sandbox={}, body={}", sandbox, JsonHelper.toJson(data));
            String payNo = alipayService.parsePlaceOrderCallback(sandbox, callbackData);
            if (StringUtils.isEmpty(payNo)) {
                return null;
            }
            return payNo;
        } catch (Throwable e) {
            log.error("支付宝支付回调失败", e);
            throw new BadRequestException("支付宝支付回调失败");
        }
    }

    @Override
    public void closeOrder(OrganizationRecharge recharge) {
        try {
            // 如果第三方支付订单是未支付状态，则可以取消订单
            // 交易状态：
            // WAIT_BUYER_PAY（交易创建，等待买家付款）、
            // TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、
            // TRADE_SUCCESS（交易支付成功）、
            // TRADE_FINISHED（交易结束，不可退款
            if ("WAIT_BUYER_PAY".equals(recharge.getThirtpartyStatus())) {
                alipayService.closeOrder(
                        isSandboxRecharge(recharge.getOrgId()),
                        recharge.getPayNo(), null);
            }
        } catch (Throwable e) {
            log.error("支付宝支付取消订单失败", e);
            throw new BadRequestException("微信支付取消订单失败");
        }
    }

    @Override
    public boolean queryOrder(OrganizationRecharge recharge) {
        try {
            if (recharge.getStatus() == RechargeStatus.init) {
                AlipayQueryOrderResponseDto response = alipayService.queryOrder(
                        isSandboxRecharge(recharge.getOrgId()),
                        recharge.getPayNo(), null);
                String status = response.getPayStatus();
                recharge.setThirtpartyResponse(JsonHelper.toJson(response));
                recharge.setThirtpartyStatus(status);
                recharge.setThirtpartyPayNo(response.getAlipayNo());
                if (response.isSuccess()) {
                    recharge.setPayTime(response.getPayTime());
                    recharge.setStatus(RechargeStatus.success);
                }
                return true;
            }
        } catch (Throwable e) {
            log.error("支付宝支付查询订单失败", e);
            throw new BadRequestException("支付宝支付查询订单失败");
        }
        return false;
    }

    @Override
    public boolean refundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        try {
            AlipayRefundOrderResponseDto response = alipayService.refundOrder(
                    isSandboxRecharge(recharge.getOrgId()),
                    recharge.getPayNo(),
                    recharge.getThirtpartyPayNo(),
                    refund.getRefundNo(),
                    refund.getAmount()
            );
            String thirdpartyResponse = JsonHelper.toJson(response);
            String thirdpartyStatus = response.getRefundStatus();
            refund.setThirtpartyResponse(thirdpartyResponse);
            refund.setThirtpartyStatus(thirdpartyStatus);
            refund.setStatus(RechargeRefundStatus.init);
            return true;
        } catch (Throwable e) {
            log.error("支付宝支付退款失败", e);
            refund.setThirtpartyResponse(e.getMessage());
            refund.setStatus(RechargeRefundStatus.failure);
        }
        return false;
    }

    @Override
    public boolean queryRefundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        try {
            AlipayQueryRefundOrderResponseDto response = alipayService.queryRefundOrder(
                    isSandboxRecharge(recharge.getOrgId()),
                    recharge.getPayNo(),
                    refund.getThirtpartyRefundNo(),
                    refund.getRefundNo()
            );
            refund.setThirtpartyResponse(JsonHelper.toJson(response.getResponse()));
            refund.setThirtpartyStatus(response.getRefundStatus());
            if (response.isSuccess()) {
                refund.setStatus(RechargeRefundStatus.success);
                refund.setSuccessTime(response.getRefundTime());
                return true;
            }
            // 支付宝没有明确的字段标识失败了
        } catch (Throwable e) {
            log.error("支付宝支付查询退款失败", e);
        }
        return false;
    }
}
