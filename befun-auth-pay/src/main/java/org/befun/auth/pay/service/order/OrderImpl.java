package org.befun.auth.pay.service.order;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.pay.constant.*;
import org.befun.auth.pay.dto.order.OrderAmountResponseDto;
import org.befun.auth.pay.dto.order.OrderRequestDto;
import org.befun.auth.pay.dto.order.PlaceOrderDto;
import org.befun.auth.pay.dto.order.PlaceOrderResponseDto;
import org.befun.auth.pay.dto.recharge.RechargeRequestDto;
import org.befun.auth.pay.dto.recharge.RechargeResponseDto;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationOrderRefund;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.property.PayProperties;
import org.befun.auth.pay.property.RechargeProperties;
import org.befun.auth.pay.repository.OrganizationOrderRefundRepository;
import org.befun.auth.pay.repository.OrganizationOrderRepository;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.service.OrganizationBillService;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.auth.service.OrganizationSmsRecordService;
import org.befun.auth.service.OrganizationWalletService;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.service.LockRunnableHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;

public abstract class OrderImpl<A1, A2, O, F> implements IOrder<A1, A2, O, F> {

    @Autowired
    protected PayProperties payProperties;
    @Autowired
    protected RechargeProperties rechargeProperties;
    @Autowired
    protected OrganizationOrderRepository repository;
    @Autowired
    protected OrganizationOrderRefundRepository organizationOrderRefundRepository;
    @Autowired
    protected OrganizationWalletService organizationWalletService;
    @Autowired
    protected OrganizationBillService organizationBillService;
    @Autowired
    protected OrganizationRechargeService organizationRechargeService;
    @Autowired
    protected OrganizationRechargeRefundRepository organizationRechargeRefundRepository;
    @Autowired
    protected OrganizationSmsRecordService organizationSmsRecordService;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;
    @Autowired
    protected IAuthEventTrigger authEventTrigger;
    @Autowired
    private LockRunnableHelper lockRunnableHelper;

    protected int getBalance(Long orgId) {
        return organizationWalletService.balance(orgId);
    }

    protected Set<RechargeType> supportRechargeTypes() {
        Set<RechargeType> supportRechargeTypes = new HashSet<>();
        rechargeProperties.getEnabled().forEach((k, v) -> {
            if (k != null && v != null && v) {
                supportRechargeTypes.add(k);
            }
        });
        return supportRechargeTypes;
    }

    @Override
    public boolean cancel(Long orgId, Long userId, Long orderId) {
        OrganizationOrder order = repository.findByIdAndOrgIdAndType(orderId, orgId, type());
        if (order != null) {
            cancel(order, true);
        }
        return true;
    }

    private void cancel(OrganizationOrder order, boolean throwableIfSuccess) {
        if (order.getStatus() == OrderStatus.init) {
            RechargeStatus rechargeStatus = organizationRechargeService.rechargeCancel(order.getRechargeId());
            if (rechargeStatus == RechargeStatus.success) {
                throw new BadRequestException("请勿重复支付");
            }
        } else if (order.getStatus().isPaid()) {
            if (throwableIfSuccess) {
                throw new BadRequestException("请勿重复支付");
            }
        }
    }

    /**
     * 关闭旧的订单，
     */
    public void cancelOldOrderBySourceId(Long orgId, Long sourceId, boolean throwableIfSuccess) {
        List<OrganizationOrder> orders = repository.findByOrgIdAndTypeAndSourceId(orgId, type(), sourceId);
        if (!orders.isEmpty()) {
            for (OrganizationOrder oldOrder : orders) {
                cancel(oldOrder, throwableIfSuccess);
            }
        }
    }

    protected void checkPlaceOrderAmount(OrderAmountResponseDto calcAmount, OrderRequestDto requestAmount) {
        if (calcAmount.getCostAmount() != requestAmount.getCostAmount()
                || calcAmount.getWalletAmount() != requestAmount.getWalletAmount()
                || !calcAmount.getAvailablePayTypes().contains(requestAmount.getPayType())) {
            throw new BadRequestException("订单金额已变动，请重试");
        }
        if (requestAmount.getPayType() != PayType.wallet && calcAmount.getRecharge() != null) {
            if (requestAmount.getRecharge() == null || !calcAmount.getRecharge().compare(requestAmount.getRecharge())) {
                throw new BadRequestException("订单金额已变动，请重试");
            }
        }
    }

    /**
     * 下单
     * 1 如果只用钱包付款，直接扣除钱包金额，写入账单返回订单成功结果
     * 2 如果需要第三方支付，则返回init状态，并返回支付信息
     */
    protected PlaceOrderResponseDto placeOrder(Long orgId, Long userId, OrderType orderType, PayServiceRate payServiceRate, PlaceOrderDto dto, Consumer<OrganizationOrder> successCallback) {
        OrganizationOrder entity = new OrganizationOrder();
        entity.setOrgId(orgId);
        entity.setSourceId(dto.getSourceId());
        entity.setCreateUserId(userId);
        entity.setTitle(dto.getTitle());
        entity.setType(orderType);
        entity.setPayType(dto.getPayType());
        entity.setAmount(dto.getCostAmount());
        if (dto.getPayType() == PayType.wallet) {
            checkWalletAmount(orgId, dto.getAmountWallet());
            entity.setAmountWallet(dto.getAmountWallet());
            entity.setAmountRecharge(0);
            entity.setStatus(OrderStatus.success);
        } else if (dto.getPayType() == PayType.mixed_wallet_wechat || dto.getPayType() == PayType.mixed_wallet_alipay) {
            checkWalletAmount(orgId, dto.getAmountWallet());
            entity.setAmountWallet(dto.getAmountWallet());
            entity.setAmountRecharge(dto.getAmountRecharge());
        } else if (dto.getPayType() == PayType.wechat || dto.getPayType() == PayType.alipay) {
            entity.setAmountWallet(0);
            entity.setAmountRecharge(dto.getAmountRecharge());
        } else {
            throw new BadRequestException("不支持的支付方式");
        }
        repository.save(entity);
        PlaceOrderResponseDto response = new PlaceOrderResponseDto();
        response.setOrderId(entity.getId());
        response.setStatus(entity.getStatus());
        if (entity.getPayType().isRecharge()) {
            RechargeResponseDto recharge = rechargeByOrder(entity, payServiceRate, dto.getTitleAlias());
            response.setRecharge(recharge);
            // 把充值id写入订单
            entity.setRechargeId(recharge.getRechargeId());
            repository.save(entity);
        } else {
            // 只用钱包付款，直接扣除钱包金额，写入账单，发送订单成功事件
            int balance = organizationWalletService.consumer(orgId, entity.getAmountWallet());
            organizationBillService.addByOrder(balance, entity, BillType.wallet);
            Optional.ofNullable(successCallback).ifPresent(i -> i.accept(entity));
            orderCompleted(entity);
            authEventTrigger.orderCompleted(entity.getOrgId(), entity.getCreateUserId(), entity.getId(), entity.getStatus().name());
        }
        cacheOrderInfo(entity.getId(), dto.getSourceId(), orderType, entity.getStatus(), entity.getAmount());
        return response;
    }

    /**
     * 校验余额
     */
    private void checkWalletAmount(Long orgId, Integer cost) {
        if (!organizationWalletService.hasBalance(orgId, cost)) {
            throw new BadRequestException("余额不足");
        }
    }

    /**
     * 缓存订单信息
     */
    private void cacheOrderInfo(Long orderId, Long sourceId, OrderType orderType, OrderStatus status, Integer amount) {
        String orderKey = OrganizationOrderService.orderKey(orderId);
        Map<String, String> cache = Map.of("status", status.name(), "amount", amount.toString(), "sourceId", Optional.ofNullable(sourceId).orElse(0L).toString(), "orderType", Optional.ofNullable(orderType).map(Enum::name).orElse("null"));
        stringRedisTemplate.opsForHash().putAll(orderKey, cache);
        stringRedisTemplate.expire(orderKey, Duration.ofHours(4));
    }

    /**
     * 订单付款方式包含充值付款
     */
    private RechargeResponseDto rechargeByOrder(OrganizationOrder order, PayServiceRate payServiceRate, String titleAlias) {
        if (order.getAmountRecharge() > 0) {
            RechargeRequestDto rechargeDto;
            if (order.getPayType() == PayType.mixed_wallet_wechat || order.getPayType() == PayType.wechat) {
                rechargeDto = new RechargeRequestDto(RechargeType.recharge_wechat, order.getAmountRecharge(), order.getTitle(), titleAlias);
            } else if (order.getPayType() == PayType.mixed_wallet_alipay || order.getPayType() == PayType.alipay) {
                rechargeDto = new RechargeRequestDto(RechargeType.recharge_alipay, order.getAmountRecharge(), order.getTitle(), titleAlias);
            } else {
                throw new BadRequestException("不支持的支付方式");
            }
            // 订单发起的支付
            return organizationRechargeService.recharge(order.getOrgId(), order.getCreateUserId(), order.getId(), rechargeDto, payServiceRate);
        }
        throw new BadRequestException("充值金额必须大于0");
    }

    protected Boolean refundOrder(Long orgId, Long userId, Long orderId, Long sourceId, OrderType orderType, Integer refundAmount, String refundNo, String refundTitle, BiFunction<OrganizationOrder, OrganizationOrderRefund, Boolean> refundApply) {
        if (refundAmount == null || refundAmount <= 0) {
            throw new BadRequestException("退还金额错误");
        }
        if (StringUtils.isEmpty(refundNo)) {
            throw new BadRequestException("退还单号不能为空");
        }
        OrganizationOrder order = repository.findById(orderId).orElse(null);
        if (order == null || orderType == null || order.getType() != orderType || sourceId == null || !sourceId.equals(order.getSourceId())) {
            throw new BadRequestException("订单不存在");
        }
        if (!order.getStatus().isPaid()) {
            throw new BadRequestException("订单未支付");
        } else if (refundAmount > order.getAmount()) {
            throw new BadRequestException("订单退还金额错误");
        }
        return lockRunnableHelper.runOrThrow(LockKeys.order_refund, List.of(orderId, refundNo), () -> {
            if (organizationOrderRefundRepository.existsByOrderIdAndRefundNo(orderId, refundNo)) {
                throw new BadRequestException("已退还，禁止重复退还");
            }
            checkOrderRefundAmount(order, refundAmount);
            // add record
            OrganizationOrderRefund refund = new OrganizationOrderRefund();
            refund.setOrgId(orgId);
            refund.setOrderId(orderId);
            refund.setCreateUserId(userId);
            refund.setRefundNo(refundNo);
            refund.setTitle(refundTitle);
            refund.setType(orderType);
            refund.setAmountRefund(refundAmount);
            organizationOrderRefundRepository.save(refund);
            return refundApply.apply(order, refund);
        });
    }

    /**
     * 校验退款金额
     * 1 如果订单类型不支持退款，直接失败
     * 2 如果订单类型支持全额退款，则校验还没有退款记录，并且退款金额和订单金额一致
     * 3 如果订单类型支持单次部分退款，则校验还没有退款记录，并且退款金额小于等于订单金额
     * 4 如果订单类型支持多次部分退款，则校验退款总金额小于等于订单金额
     */
    private void checkOrderRefundAmount(OrganizationOrder order, Integer refundAmount) {
        Integer orderAmount = order.getAmount();
        OrderRefundType refundType = order.getType().getRefundType();
        if (refundType == OrderRefundType.none) {
            throw new BadRequestException("订单类型不支持退款");
        }
        List<OrganizationOrderRefund> refunds = organizationOrderRefundRepository.findByOrderId(order.getId());
        if (refundType == OrderRefundType.full_refund) {
            // 如果订单类型支持全额退款，则校验还没有退款记录，并且退款金额和订单金额一致
            if (CollectionUtils.isNotEmpty(refunds)) {
                throw new BadRequestException("订单类型支持单次全额退款，但是已经有退款记录了");
            } else if (!orderAmount.equals(refundAmount)) {
                throw new BadRequestException("订单类型支持单次全额退款，但是退款金额和订单金额不一致");
            }
        } else if (refundType == OrderRefundType.sub_refund_single) {
            // 如果订单类型支持单次部分退款，则校验还没有退款记录，并且退款金额小于等于订单金额
            if (CollectionUtils.isNotEmpty(refunds)) {
                throw new BadRequestException("订单类型支持单次部分退款，但是已经有退款记录了");
            } else if (refundAmount > orderAmount) {
                throw new BadRequestException("订单类型支持单次部分退款，但是退款金额大于订单金额");
            }
        } else if (refundType == OrderRefundType.sub_refund_multi) {
            // 如果订单类型支持多次部分退款，则校验退款总金额小于等于订单金额
            int totalRefundAmount = refunds.stream().map(OrganizationOrderRefund::getAmountRefund).filter(Objects::nonNull).mapToInt(i -> i).sum();
            if (totalRefundAmount + refundAmount > orderAmount) {
                throw new BadRequestException("订单类型支持多次部分退款，但是订单退还总金额不能大于订单金额");
            }
        } else {
            // 未知的退款类型
            throw new BadRequestException("订单类型不支持退款");
        }
    }


    /**
     * 重新退款，这里只处理涉及到第三方支付的订单
     * 1 查询退款订单号
     * 2 查询订单，充值记录，退款记录
     */
    @Override
    public Boolean reRefund(Long orgId, Long userId, Long orderRefundId) {
        OrganizationOrderRefund orderRefund = null;
        if (orderRefundId != null && orderRefundId > 0) {
            orderRefund = organizationOrderRefundRepository.findById(orderRefundId).orElse(null);
        }
        if (orderRefund == null) {
            throw new BadRequestException("退款订单号不存在");
        }
        OrganizationOrder order = null;
        if (orderRefund.getOrderId() != null && orderRefund.getOrderId() > 0) {
            order = repository.findById(orderRefund.getOrderId()).orElse(null);
        }
        if (order == null) {
            throw new BadRequestException("退款订单号对应的订单不存在");
        }
        if (order.getStatus() != OrderStatus.refund_failure) {
            throw new BadRequestException("退款订单号对应的订单必须是失败状态");
        }
        Long orderId = order.getId();
        Long rechargeId = order.getRechargeId();
        final OrganizationOrder order1 = order;
        return lockRunnableHelper.runOrThrow(LockKeys.order_re_refund, List.of(orderId, orderRefundId), () -> {
            OrganizationRecharge recharge = organizationRechargeService.get(rechargeId);
            if (recharge == null) {
                throw new BadRequestException("退款订单号对应的订单充值记录不存在");
            }
            OrganizationRechargeRefund rechargeRefund = organizationRechargeRefundRepository.findFirstByOrgIdAndOrderIdAndOrderRefundIdAndRechargeId(orgId, orderId, orderRefundId, rechargeId);
            if (rechargeRefund == null) {
                throw new BadRequestException("退款订单号对应的订单充值记录的退款记录不存在");
            }
            if (rechargeRefund.getStatus() == RechargeRefundStatus.failure) {
                // 退款单是失败状态
                // 重新退款
                organizationRechargeService.refund(recharge, rechargeRefund);
                if (rechargeRefund.getStatus() == RechargeRefundStatus.init) {
                    // 退款等待中，修改订单状态，添加10秒后查询退款状态的延时任务
                    order1.setStatus(OrderStatus.refund_pending);
                    repository.save(order1);
                    authEventTrigger.rechargeRefundQuery(orgId, userId, rechargeRefund.getRechargeId(), rechargeRefund.getId(), Duration.ofSeconds(10));
                    return false;
                } else if (rechargeRefund.getStatus() == RechargeRefundStatus.success) {
                    // 退款成功，修改订单状态
                    order1.setStatus(OrderStatus.refund);
                    repository.save(order1);
                    return true;
                }
            }
            return null;
        });
    }
}
