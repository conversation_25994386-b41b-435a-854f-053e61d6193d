package org.befun.auth.pay.constant;

import lombok.Getter;

@Getter
public enum OrderType {

    order_sms(true, OrderRefundType.none),
    order_red_packet(true, OrderRefundType.sub_refund_multi),
    order_org_version(false, OrderRefundType.none),
    order_adminx_channel(false, OrderRefundType.sub_refund_single),
    order_ai_point(true, OrderRefundType.none),
    ;

    private final boolean useWallet;
    private final OrderRefundType refundType;

    OrderType(boolean useWallet, OrderRefundType refundType) {
        this.useWallet = useWallet;
        this.refundType = refundType;
    }
}
