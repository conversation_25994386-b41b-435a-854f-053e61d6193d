package org.befun.auth.pay.thirdpartypay;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class AuthPayHandler implements ApplicationRunner {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired(required = false)
    private List<IAuthThirdPartyPay> thirdPartyPayList;
    private final Map<RechargeType, IAuthThirdPartyPay> thirdPartyPayMap = new HashMap<>();
    private AuthMockPay authMockPay;

    @Override
    public void run(ApplicationArguments args) {
        Optional.ofNullable(thirdPartyPayList).ifPresent(l -> l.forEach(i -> {
            if (i.type() == null) {
                if (i instanceof AuthMockPay) {
                    authMockPay = (AuthMockPay) i;
                }
            } else {
                thirdPartyPayMap.put(i.type(), i);
            }
        }));
    }

    private IAuthThirdPartyPay getAuthThirdPartyPay(RechargeType rechargeType) {
        return Optional.ofNullable(thirdPartyPayMap.get(rechargeType)).orElseThrow(() -> new BadRequestException("不支持的充值方式"));
    }

    public String placeOrder(RechargeType rechargeType, OrganizationRecharge recharge) {
        if (rechargeType.isEnableMock() && authMockPay != null && authMockPay.isMockRecharge(recharge.getOrgId())) {
            recharge.setMockRecharge(1);
            return authMockPay.placeOrder(recharge);
        } else {
            return getAuthThirdPartyPay(rechargeType).placeOrder(recharge);
        }
    }

    public String placeOrderCallback(RechargeType rechargeType, Map<String, String> requestHeader, Object data) {
        if (rechargeType.isEnableMock() && authMockPay != null && authMockPay.isMockRecharge(data)) {
            return authMockPay.placeOrderCallback(requestHeader, data);
        } else {
            return getAuthThirdPartyPay(rechargeType).placeOrderCallback(requestHeader, data);
        }
    }

    public void closeOrder(RechargeType rechargeType, OrganizationRecharge recharge) {
        if (recharge.getMockRecharge() != null && recharge.getMockRecharge() == 0) {
            getAuthThirdPartyPay(rechargeType).closeOrder(recharge);
        }
    }

    public boolean queryOrder(RechargeType rechargeType, OrganizationRecharge recharge) {
        if (authMockPay != null && recharge.getMockRecharge() != null && recharge.getMockRecharge() == 1) {
            return authMockPay.queryOrder(recharge);
        } else {
            return getAuthThirdPartyPay(rechargeType).queryOrder(recharge);
        }
    }

    public boolean refundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        if (authMockPay != null && recharge.getMockRecharge() != null && recharge.getMockRecharge() == 1) {
            return authMockPay.refundOrder(recharge, refund);
        } else {
            return getAuthThirdPartyPay(recharge.getType()).refundOrder(recharge, refund);
        }
    }

    public boolean queryRefundOrder(OrganizationRecharge recharge, OrganizationRechargeRefund refund) {
        if (authMockPay != null && recharge.getMockRecharge() != null && recharge.getMockRecharge() == 1) {
            return authMockPay.queryRefundOrder(recharge, refund);
        } else {
            return getAuthThirdPartyPay(recharge.getType()).queryRefundOrder(recharge, refund);
        }
    }

    public String refundOrderCallback(RechargeType rechargeType, Map<String, String> requestHeader, Object refundData) {
        if (rechargeType.isEnableMock() && authMockPay != null && authMockPay.isMockRecharge(refundData)) {
            return authMockPay.refundOrderCallback(requestHeader, refundData);
        } else {
            return getAuthThirdPartyPay(rechargeType).refundOrderCallback(requestHeader, refundData);
        }
    }

}
