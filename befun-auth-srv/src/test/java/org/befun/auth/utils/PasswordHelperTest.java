package org.befun.auth.utils;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class PasswordHelperTest {

    @Test
    void encrypt() {
        String s = PasswordHelper.encrypt("tree123456");
        System.out.println(s);
        System.out.println(PasswordHelper.verify("tree123456", s));
        System.out.println(PasswordHelper.verify("tree123456", "$2a$10$5LbFWYJ5dRycDHfpkd4ri.ivnw4dVpCDy4BMUhMo3.of8FhvtgcPq"));
        System.out.println(PasswordHelper.verify("tree123456", "$2a$10$uXzifzfL8ppaHbi4WIQuieNPbNw5Ysznx/2NwWMeqbvwAH7v3bElK"));
    }

    @Test
    void encrypt2() {
        System.out.println(RandomStringUtils.random(64));
        System.out.println(RandomStringUtils.random(64,true,false));
        System.out.println(RandomStringUtils.random(64,true,true));
        System.out.println(RandomStringUtils.random(64,false,true));
        System.out.println(RandomStringUtils.random(64,false,false));
    }

//    @Test
    void passwordStrength() {
        Assertions.assertEquals(4, PasswordHelper.passwordStrength("1a3A@"));
        Assertions.assertEquals(3, PasswordHelper.passwordStrength("1a3A"));
        Assertions.assertEquals(2, PasswordHelper.passwordStrength("1a3"));
        Assertions.assertEquals(1, PasswordHelper.passwordStrength("13"));
        Assertions.assertEquals(4, PasswordHelper.passwordStrength("1a3A2"));
        Assertions.assertEquals(5, PasswordHelper.passwordStrength("1aav3A2"));
        Assertions.assertEquals(7, PasswordHelper.passwordStrength("1aav3#@$A2"));
        Assertions.assertEquals(10, PasswordHelper.passwordStrength("1aav3#@$ACV2"));
    }
}