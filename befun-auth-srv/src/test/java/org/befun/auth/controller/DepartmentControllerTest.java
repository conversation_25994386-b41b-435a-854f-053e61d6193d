package org.befun.auth.controller;

import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.dto.DepartmentRequestDto;
import org.befun.auth.entity.Department;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.core.constant.ErrorCode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class DepartmentControllerTest extends AssertController {

    private static final String URL_TREE = "/departments/tree";
    private static final String URL_SAVE = "/departments/save";

    @Autowired
    private DepartmentRepository departmentRepository;

    @Test
    @Sql(statements = INIT_SQL)
    void tree() throws Exception {
        treeOk1(); // token1 时 返回 广东-深圳-南山
        treeOk2(); // token2 时 返回 江西-九江
        tree401(); // 无token 时 403
    }

    void treeOk1() throws Exception {
        getOk(URL_TREE, mock_token_org1_user1,
                actions -> actions.andExpect(jsonPath("$.data[0].title").value("广东"))
                        .andExpect(jsonPath("$.data[0].subDepartments[0].title").value("深圳"))
                        .andExpect(jsonPath("$.data[0].subDepartments[0].subDepartments[0].title").value("南山"))
        );
    }

    void treeOk2() throws Exception {
        getOk(URL_TREE, mock_token_org2_user1,
                actions -> actions.andExpect(jsonPath("$.data[0].title").value("江西"))
                        .andExpect(jsonPath("$.data[0].subDepartments[0].title").value("九江"))
        );
    }

    void tree401() throws Exception {
        get401(URL_TREE);
    }

//    @Test
//    @Sql(statements = INIT_SQL)
    void save() throws Exception {

        // 更新一级部门
        DepartmentRequestDto dto = new DepartmentRequestDto();
        dto.setId(1L);
        dto.setTitle("test11");
        dto.setPid(0L);
        postOk(URL_SAVE, dto, mock_token_org1_user1, actions -> actions.andExpect(jsonPath("$.data.pid").value(0))
                .andExpect(jsonPath("$.data.title").value("test11"))
                .andExpect(jsonPath("$.data.id").value(1L)));

        // 添加二级部门
        dto = new DepartmentRequestDto();
        dto.setTitle("test22");
        dto.setPid(1L);
        postOk(URL_SAVE, dto, mock_token_org1_user1, actions -> actions.andExpect(jsonPath("$.data.pid").value(1))
                .andExpect(jsonPath("$.data.title").value("test2")));
    }

//    @Test
    void save2() throws Exception {
        // 添加一级部门 不存在的pid 400 ENTITY_NOT_FOUND
        DepartmentRequestDto dto = new DepartmentRequestDto();
        dto.setTitle("test1");
        dto.setPid(1L);
        post400(URL_SAVE, dto, mock_token_org1_user1, ErrorCode.ENTITY_NOT_FOUND);
    }

//    @Test
//    @Sql(statements = INIT_SQL2)
    void save3() throws Exception {
        // 部门可以移动到同一级别的其他部门下面
        DepartmentRequestDto dto = new DepartmentRequestDto();
        dto.setId(5L);
        dto.setTitle("宝安");
        dto.setPid(2L);
        postOk(URL_SAVE, dto, mock_token_org1_user1, actions -> actions.andExpect(jsonPath("$.data.pid").value(2L))
                .andExpect(jsonPath("$.data.title").value("宝安")));
    }


    private static final String INIT_SQL = "INSERT INTO `department` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `pid`, `title`) VALUES" +
            "('1', now(), now(), '1', 0, '广东')," +
            "('2', now(), now(), '1', 1, '深圳')," +
            "('3', now(), now(), '1', 2, '南山')," +
            "('4', now(), now(), '2', 0, '江西')," +
            "('5', now(), now(), '2', 4, '九江');";

    private static final String INIT_SQL2 = "INSERT INTO `department` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `pid`, `title`) VALUES" +
            "('1', now(), now(), '1', 0, '广东')," +
            "('2', now(), now(), '1', 1, '深圳')," +
            "('3', now(), now(), '1', 2, '南山')," +
            "('4', now(), now(), '1', 1, '广州')," +
            "('5', now(), now(), '1', 4, '宝安');";
}