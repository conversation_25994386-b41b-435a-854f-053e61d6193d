package org.befun.auth.controller;

import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.entity.ApiKey;
import org.befun.auth.repository.ApiKeyRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Example;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class ApiKeyControllerTest extends AssertController {

    @Autowired
    private ApiKeyRepository apiKeyRepository;

    private static final String URL_REFRESH = "/api-keys/refresh";

    @Test
    void refreshAutoCreateOk() throws Exception {

        postOk(URL_REFRESH, null, mock_token_org1_user1, null);

        ApiKey now1 = findFormDb(1L, 1L);
        Assertions.assertNotNull(now1.getApiKey(), now1.getApiSecret());
    }

    @Test
    @Sql(statements = "INSERT INTO `api_key` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `user_id`, `api_key`, `api_secret`, `deleted`) VALUES" +
            "('1', now(), now(), '1', '1', '1', '1', '0')," +
            "('2', now(), now(), '2', '2', '1', '1', '0');")
    void refresh1Ok() throws Exception {
        // org1 user1 refresh 时
        // 前后 apiKey1 apiSecret1 不同
        // 前后 apiKey2 apiSecret2 相同
        ApiKey old1 = findFormDb(1L, 1L);
        ApiKey old2 = findFormDb(2L, 2L);

        postOk(URL_REFRESH, null, mock_token_org1_user1, null);

        ApiKey now1 = findFormDb(1L, 1L);
        ApiKey now2 = findFormDb(2L, 2L);

        Assertions.assertNotEquals(old1.getApiKey(), now1.getApiKey());
        Assertions.assertNotEquals(old1.getApiSecret(), now1.getApiSecret());
        Assertions.assertEquals(old2.getApiKey(), now2.getApiKey());
        Assertions.assertEquals(old2.getApiSecret(), now2.getApiSecret());
    }

    @Test
    @Sql(statements = "INSERT INTO `api_key` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `user_id`, `api_key`, `api_secret`, `deleted`) VALUES" +
            "('1', now(), now(), '1', '1', '1', '1', '0')," +
            "('2', now(), now(), '2', '2', '1', '1', '0');")
    void refresh2Ok() throws Exception {
        // org2 user1 refresh 时
        // 前后 apiKey1 apiSecret1 相同
        // 前后 apiKey2 apiSecret2 不同
        ApiKey old1 = findFormDb(1L, 1L);
        ApiKey old2 = findFormDb(2L, 2L);

        postOk(URL_REFRESH, null, mock_token_org2_user1, null);

        ApiKey now1 = findFormDb(1L, 1L);
        ApiKey now2 = findFormDb(2L, 2L);

        Assertions.assertEquals(old1.getApiKey(), now1.getApiKey());
        Assertions.assertEquals(old1.getApiSecret(), now1.getApiSecret());
        Assertions.assertNotEquals(old2.getApiKey(), now2.getApiKey());
        Assertions.assertNotEquals(old2.getApiSecret(), now2.getApiSecret());
    }

    @Test
    @Sql(statements = "INSERT INTO `api_key` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `user_id`, `api_key`, `api_secret`, `deleted`) VALUES" +
            "('1', now(), now(), '1', '1', '1', '1', '0')," +
            "('2', now(), now(), '2', '2', '1', '1', '0');")
    void refresh403() throws Exception {
        // refresh 没有 token 时 403
        // 前后 apiKey1 apiSecret1 相同
        // 前后 apiKey2 apiSecret2 相同
        ApiKey old1 = findFormDb(1L, 1L);
        ApiKey old2 = findFormDb(2L, 2L);

        post401(URL_REFRESH, null);

        ApiKey now1 = findFormDb(1L, 1L);
        ApiKey now2 = findFormDb(2L, 2L);

        Assertions.assertEquals(old1.getApiKey(), now1.getApiKey());
        Assertions.assertEquals(old1.getApiSecret(), now1.getApiSecret());
        Assertions.assertEquals(old2.getApiKey(), now2.getApiKey());
        Assertions.assertEquals(old2.getApiSecret(), now2.getApiSecret());
    }


    private ApiKey findFormDb(Long userId, Long orgId) {
        ApiKey apiKey = new ApiKey();
        apiKey.setOrgId(orgId);
        apiKey.setUserId(userId);
        apiKey = apiKeyRepository.findAll(Example.of(apiKey)).stream().findFirst().orElse(null);
        Assertions.assertNotNull(apiKey);
        return apiKey;
    }
}