package org.befun.auth.controller;

import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.constant.AppType;
import org.befun.auth.constant.IndustryType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.RegisterInfoDto;
import org.befun.auth.entity.Industry;
import org.befun.auth.provider.local.MobileVerifyCodeProvider;
import org.befun.auth.repository.IndustryRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;

import java.time.Duration;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class RegisterControllerTest extends AssertController {

    @Autowired
    IndustryRepository industryRepository;

    @Autowired
    StringRedisTemplate redisTemplate;

    @Autowired
    MobileVerifyCodeProvider mobileVerifyCodeProvider;

    @Test
    @Sql(statements = "create table worker_event (" +
            "id INT IDENTITY PRIMARY KEY, " +
            "consumer_status varchar(255), " +
            "content varchar(1000), " +
            "counts integer, " +
            "create_time TIMESTAMP, " +
            "delay varchar(255), " +
            "modify_time TIMESTAMP, " +
            "org_id bigint, " +
            "response varchar(255), " +
            "source varchar(255), " +
            "status varchar(255), " +
            "user_id bigint, " +
            "type varchar(255), " +
            "primary key (id))")
    void register() throws Exception {

        String code = "123456";
        String mobile = "13333333333";
        String name = "name";
        String email = "<EMAIL>";
        String password = "123456";
        String companyName = "companyName";

        Industry industry = new Industry();
        industry.setCode(IndustryType.OTHERS.getCode());
        industry.setName(IndustryType.OTHERS.getText());
        industryRepository.save(industry);

        String key = mobileVerifyCodeProvider.redisKey(VerifyCodeUseFor.REGISTER, mobileVerifyCodeProvider.getVerifyCodeType(), mobile);
        redisTemplate.opsForValue().set(key, code, Duration.ofMinutes(5));

        RegisterInfoDto registerInfoDto = new RegisterInfoDto();

        registerInfoDto.setApp(AppType.cem);
        registerInfoDto.setMobile(mobile);
        registerInfoDto.setVerifyCode(code);
        registerInfoDto.setEmail(email);
        registerInfoDto.setName(name);
        registerInfoDto.setPassword(password);
        registerInfoDto.setCompanyName(companyName);

        postOk("/register", registerInfoDto, "", actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.mobile").value(mobile))
                        .andExpect(jsonPath("$.data.email").value(email))
        );
    }
}