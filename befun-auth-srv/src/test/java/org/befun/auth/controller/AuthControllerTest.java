package org.befun.auth.controller;

import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.constant.VerifyCodeType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.*;
import org.befun.core.constant.ErrorCode;
import org.befun.extension.service.MailService;
import org.befun.extension.service.SmsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class AuthControllerTest extends AssertController {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @MockBean
    private MailService mailService;
    @MockBean
    private SmsService smsService;

    @BeforeEach
    public void mock() {
        doNothing().when(mailService).sendMessageByTemplate(any(), any(), any());
        given(smsService.sendMessageByTemplate(any(), any(), any())).willReturn(true);
    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    void loginPassword() throws Exception {
        String url = "/login/password/cem";
        // 登录成功
        LoginPasswordRequestDto dto = new LoginPasswordRequestDto();
        dto.setUsername("<EMAIL>");
        dto.setPassword("yeyuming1");
        postOk(url, dto, "", actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.token").isNotEmpty())
        );

        dto.setUsername("***********");
        dto.setPassword("yeyuming1");
        postOk(url, dto, "", actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.token").isNotEmpty())
        );

        // 用户名或密码错误
        dto.setPassword("yeyuming");
        post400(url, dto, "", ErrorCode.BAD_PARAMETER);

    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    void loginMobileVerifyCode() throws Exception {

        String source = "mobile";
        String account = "***********";
        VerifyCodeUseFor useFor = VerifyCodeUseFor.LOGIN;

        sendVerifyCode(useFor, source, account);

        String url = "/login/mobile/cem";

        LoginVerifyCodeRequestDto dto = new LoginVerifyCodeRequestDto();
        dto.setAccount(account);
        dto.setCode(getCode(useFor, VerifyCodeType.MOBILE, dto.getAccount()));
        postOk(url, dto, "", actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.token").isNotEmpty())
        );

    }

    private String getCode(VerifyCodeUseFor useFor, VerifyCodeType type, String account) {

        // verify_code:login:orgCode:mobile:***********
        // verify_code:bind:orgId:mobile:***********
        String key = String.format("verify_code:%s:%s:%s", useFor.getValue(), type.name(), account);
        return redisTemplate.opsForValue().get(key);
    }

    void sendVerifyCode(VerifyCodeUseFor useFor, String source, String account) throws Exception {
        String url = "/send/verify-code/" + useFor.getValue() + "/" + source + "/cem";

        SendVerifyCodeRequestDto dto = new SendVerifyCodeRequestDto();
        dto.setAccount(account);
        postOk(url, dto, "", actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data").value(true))
        );
    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    void loginEmailVerifyCode() throws Exception {

        String source = "email";
        String account = "<EMAIL>";
        VerifyCodeUseFor useFor = VerifyCodeUseFor.LOGIN;

        sendVerifyCode(useFor, source, account);

        String url = "/login/email/cem";

        LoginVerifyCodeRequestDto dto = new LoginVerifyCodeRequestDto();
        dto.setAccount(account);
        dto.setCode(getCode(useFor, VerifyCodeType.EMAIL, dto.getAccount()));
        postOk(url, dto, "", actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.token").isNotEmpty())
        );

    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    void bindMobile() throws Exception {

        String source = "mobile";
        String account = "***********";
        VerifyCodeUseFor useFor = VerifyCodeUseFor.BIND;

        sendVerifyCode(useFor, source, account);

        String url = "/bind/account/" + source + "/cem";

        BindVerifyCodeRequestDto dto = new BindVerifyCodeRequestDto();
        dto.setAccount(account);
        dto.setCode(getCode(useFor, VerifyCodeType.MOBILE, dto.getAccount()));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data").value(true))
        );
    }


    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    void bindEmail() throws Exception {

        String source = "email";
        String account = "<EMAIL>";
        VerifyCodeUseFor useFor = VerifyCodeUseFor.BIND;

        sendVerifyCode(useFor, source, account);

        String url = "/bind/account/" + source + "/cem";

        BindVerifyCodeRequestDto dto = new BindVerifyCodeRequestDto();
        dto.setAccount("<EMAIL>");
        dto.setCode(getCode(useFor, VerifyCodeType.EMAIL, dto.getAccount()));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data").value(true))
        );

    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    void resetPassword() throws Exception {

        String source = "email";
        String account = "<EMAIL>";
        String url = "/reset-password/" + source + "/cem";

        VerifyCodeUseFor useFor = VerifyCodeUseFor.RESET_PASSWORD;

        sendVerifyCode(useFor, source, account);

        ResetPasswordRequestDto dto = new ResetPasswordRequestDto();
        dto.setAccount("<EMAIL>");
        dto.setNewPassword("123456");
        dto.setCode(getCode(useFor, VerifyCodeType.EMAIL, dto.getAccount()));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data").value(true))
        );

        source = "mobile";
        account = "***********";
        url = "/reset-password/" + source + "/cem";

        sendVerifyCode(useFor, source, account);

        dto = new ResetPasswordRequestDto();
        dto.setAccount("***********");
        dto.setNewPassword("123456");
        dto.setCode(getCode(useFor, VerifyCodeType.MOBILE, dto.getAccount()));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data").value(true))
        );

    }

    private static final String INIT_SQL_ORG = "INSERT INTO organization (id,name,industry_id,owner_id,max_users,is_block,code,is_template,available_date_begin,available_date_end,created,updated,version,wework_code,wework_corp_id,wework_agent_id,wework_expire,modify_time,create_time) VALUES" +
            " (1,'test_yuming',6,1,10,0,'test_yuming',0,'2021-08-19 00:00:00','2033-01-01 00:00:00','2021-08-19 16:56:31',NULL,'{\"surveyplus_version\":\"base\",\"cem_version\":\"profession\"}',NULL,NULL,NULL,NULL,NULL,NULL);";

    private static final String INIT_SQL_USER = "INSERT INTO `user` (id,org_id,is_admin,department_ids,role_id,username,password,password_strength,truename,mobile,email,wx_cem_openid,wx_cem_info,avatar,nickname,available_systems,status,platform,is_finished_guide,created,updated,is_delete,latest_login,create_time,modify_time) VALUES" +
            " (1,1,1,'[[23084]]','3179','test_yuming','$2y$10$piPZMLnbUw2N3LnJFRZTw.mxdTM5lu6hpe65G/6n9Q0VM2cW/QMyG',5,'yym','***********','<EMAIL>',NULL,NULL,NULL,NULL,'{\"login_surveyplus\":1,\"login_cem\":1}',1,'','N',1629363392,1631241056,0,1632360920,NULL,'2021-09-17 16:21:23.414000000');";
}