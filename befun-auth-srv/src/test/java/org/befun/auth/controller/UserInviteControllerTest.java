package org.befun.auth.controller;

import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.constant.UserInvitationStatus;
import org.befun.auth.dto.UserInviteActiveRequestDto;
import org.befun.auth.dto.UserInviteNotifyRequestDto;
import org.befun.auth.dto.UserInviteRequestDto;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserInvitation;
import org.befun.auth.repository.UserInvitationRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.AsyncService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.constant.ErrorCode;
import org.befun.extension.service.MailService;
import org.befun.extension.service.SmsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class UserInviteControllerTest extends AssertController {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserInvitationRepository userInvitationRepository;

    @MockBean
    private MailService mailService;
    @MockBean
    private SmsService smsService;
    @MockBean
    private AsyncService asyncService;

    @BeforeEach
    public void mock() {
        doNothing().when(mailService).sendMessageByTemplate(any(), any(), any());
        doNothing().when(asyncService).inviteNotify(any(), any(), any(), any());
        given(smsService.sendMessageByTemplate(any(), any(), any())).willReturn(true);
    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    void inviteMembers() throws Exception {
        String url = "/users/invite/email";
        UserInviteRequestDto dto = new UserInviteRequestDto();
        dto.setEmails(List.of(
                "<EMAIL>",
                "<EMAIL>"
        ));
        // 邮箱地址重复
        post400(url, dto, mock_token_org1_user1, ErrorCode.BAD_PARAMETER);

        dto.setEmails(List.of(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        ));
        // 邮箱地址已存在
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.success").value(false))
                        .andExpect(jsonPath("$.data.existsEmail[0]").value("<EMAIL>"))
        );

        dto.setEmails(List.of(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        ));
        // 邮箱地址满额
//        postOk(url, dto, mock_token_org1_user1, actions ->
//                actions.andExpect(jsonPath("$.code").value(200))
//                        .andExpect(jsonPath("$.data.success").value(false))
//                        .andExpect(jsonPath("$.data.overSize").value("1"))
//        );

        dto.setEmails(List.of(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        ));
        // 成功
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.success").value(true))
        );

    }

    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    void inviteMemberNotify() throws Exception {
        String url = "/users/invite/email";
        UserInviteRequestDto dto = new UserInviteRequestDto();
        dto.setEmails(List.of(
                "<EMAIL>"
        ));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.success").value(true))
        );
        UserInvitation invitation = userInvitationRepository.findAll().get(0);
        Assertions.assertTrue(UserInvitationStatus.canNotify(invitation.getStatus()));

        url = "/users/invite/email/notify";
        UserInviteNotifyRequestDto dto2 = new UserInviteNotifyRequestDto();
        dto2.setInviteId(invitation.getId());
        postOk(url, dto2, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.id").value(invitation.getId()))
        );
    }


    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    void inviteActiveStatus() throws Exception {
        String url = "/users/invite/email";
        UserInviteRequestDto dto = new UserInviteRequestDto();
        dto.setEmails(List.of(
                "<EMAIL>"
        ));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.success").value(true))
        );
        UserInvitation invitation = userInvitationRepository.findAll().get(0);
        Assertions.assertTrue(UserInvitationStatus.canNotify(invitation.getStatus()));


        url = "/users/invite/active/status?code=" + invitation.getCode();
        getOk(url, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.type").value(1))
                        .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
        );
    }


    @Test
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    void inviteActive() throws Exception {
        String url = "/users/invite/email";
        UserInviteRequestDto dto = new UserInviteRequestDto();
        dto.setEmails(List.of(
                "<EMAIL>"
        ));
        postOk(url, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.success").value(true))
        );
        UserInvitation invitation = userInvitationRepository.findAll().get(0);
        Assertions.assertTrue(UserInvitationStatus.canNotify(invitation.getStatus()));

        url = "/users/invite/active";
        UserInviteActiveRequestDto dto2 = new UserInviteActiveRequestDto();
        dto2.setCode(invitation.getCode());
        dto2.setPassword("yymyym");
        dto2.setTruename("yym");
        postOk(url, dto2, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data").value(true))
        );
        User user = userRepository.findAll().get(1);
        Assertions.assertTrue(PasswordHelper.verify(dto2.getPassword(), user.getPassword()));
        Assertions.assertEquals(dto2.getTruename(), user.getTruename());
    }

    private static final String INIT_SQL_ORG = "INSERT INTO organization (id,name,industry_id,owner_id,max_users,is_block,code,is_template,available_date_begin,available_date_end,created,updated,version,wework_code,wework_corp_id,wework_agent_id,wework_expire,modify_time,create_time) VALUES" +
            " (1,'test_yuming',6,1,4,0,'test_yuming',0,'2021-08-19 00:00:00','2023-01-01 00:00:00','2021-08-19 16:56:31',NULL,'{\"surveyplus_version\":\"base\",\"cem_version\":\"profession\"}',NULL,NULL,NULL,NULL,NULL,NULL);";

    private static final String INIT_SQL_USER = "INSERT INTO `user` (id,org_id,is_admin,department_ids,role_id,username,password,password_strength,truename,mobile,email,wx_cem_openid,wx_cem_info,avatar,nickname,available_systems,status,platform,is_finished_guide,created,updated,is_delete,latest_login,create_time,modify_time) VALUES" +
            " (1,1,1,'[[23084]]','3179','test_yuming','$2y$10$piPZMLnbUw2N3LnJFRZTw.mxdTM5lu6hpe65G/6n9Q0VM2cW/QMyG',5,'yym','18600000000','<EMAIL>',NULL,NULL,NULL,NULL,'{\"login_surveyplus\":1,\"login_cem\":1}',1,'','N',1629363392,1631241056,0,1632360920,NULL,'2021-09-17 16:21:23.414000000');";

    private static final String INIT_SQL_DEPARTMENT = "INSERT INTO `department` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `pid`, `title`) VALUES" +
            "('1', now(), now(), '1', 0, '广东')," +
            "('2', now(), now(), '1', 1, '深圳')," +
            "('3', now(), now(), '1', 2, '南山')," +
            "('4', now(), now(), '2', 0, '江西')," +
            "('5', now(), now(), '2', 4, '九江');";
}