package org.befun.auth.controller;

import com.google.common.collect.Lists;
import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.constant.RoleType;
import org.befun.auth.dto.RoleRequestDto;
import org.befun.auth.dto.RoleUserRequestDto;
import org.befun.auth.dto.role.MenuTreeDto;
import org.befun.auth.dto.role.RoleSimpleDto;
import org.befun.auth.entity.Menu;
import org.befun.auth.entity.Permission;
import org.befun.auth.entity.Role;
import org.befun.auth.entity.UserRole;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.repository.UserRoleRepository;
import org.befun.core.constant.ErrorCode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class RoleControllerTest extends AssertController {

    private static final String URL_TEMPLATE = "/roles/template";
    private static final String URL_DETAIL = "/roles/%d/detail";
    private static final String URL_SAVE = "/roles/save";
    private static final String URL_ADD_USER = "/roles/%d/add-user";
    private static final String URL_REMOVE_USER = "/roles/%d/remove-user";

    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private UserRoleRepository userRoleRepository;

    @Test
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_MENU)
    @Sql(statements = INIT_SQL_PERMISSION)
    void template() throws Exception {
        // token1 查模板时，root menu = 2
        // token1 查模板时，root menu = 1
        getOk(URL_TEMPLATE, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data.menus.length()").value(2))
        );
        // token2 查模板时，root menu = 2
        // token2 查模板时，root menu = 1
        getOk(URL_TEMPLATE, mock_token_org2_user1, actions ->
                actions.andExpect(jsonPath("$.data.menus.length()").value(2))
        );
        // 无token时，返回403
        get401(URL_TEMPLATE);
    }

    @Test
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_MENU)
    @Sql(statements = INIT_SQL_PERMISSION)
    void detail() throws Exception {
        // 角色详情
        // token1时，角色id=1时，两个菜单的都有权限
        detailOk(1L, mock_token_org1_user1, true, true);
        // token1时，角色id=2时，两个菜单的都有没有权限
        detailOk(2L, mock_token_org1_user1, false, false);
        // token1时，角色id=4时，返回400，找不到实体，id=4是token2的数据
        get400(String.format(URL_DETAIL, 4), mock_token_org1_user1, ErrorCode.ENTITY_NOT_FOUND);
        // 无token时，返回403
        get401(String.format(URL_DETAIL, 1));

        // token2时，角色id=4时，菜单1有权限，菜单2没权限
        detailOk(4L, mock_token_org2_user1, true, true);
        // token2时，角色id=5时，菜单2有权限，菜单1没权限
        detailOk(5L, mock_token_org2_user1, false, true);

    }

    void detailOk(Long id, String token, boolean menu1, boolean menu2) throws Exception {
        getOk(String.format(URL_DETAIL, id), token, actions ->
                actions.andExpect(jsonPath("$.data.role.id").value(id))
                        .andExpect(jsonPath("$.data.menus.length()").value(2))
                        .andExpect(jsonPath("$.data.menus[0].selected").value(menu1))
                        .andExpect(jsonPath("$.data.menus[1].selected").value(menu2))
        );
    }


    @Test
    @Sql(statements = INIT_SQL_MENU)
    void save() throws Exception {
        Role superAdminRole1 = RoleType.SUPER_ADMIN.createRole(1L);
        Role superAdminRole2 = RoleType.SUPER_ADMIN.createRole(2L);
        roleRepository.saveAll(List.of(superAdminRole1,superAdminRole2));
        permissionRepository.saveAll(List.of(
                new Permission(superAdminRole1.getId(),"Action","/p1/p2/view"),
                new Permission(superAdminRole1.getId(),"Action","/p1/p2/edit"),
                new Permission(superAdminRole2.getId(),"Action","/p1/p2/view"),
                new Permission(superAdminRole2.getId(),"Action","/p1/p2/edit")
        ));

        // add
        RoleRequestDto dto = new RoleRequestDto();
        Role role = RoleType.OTHER.createRole(null);
        role.setName("角色1");
        dto.setRole(RoleSimpleDto.map(role));
        dto.setMenus(Lists.newArrayList(
                newMenu("/p1/p2/view", true),
                newMenu("/p1/p2/edit", false)
        ));

        // token1 添加角色
        addOk(dto, mock_token_org1_user1);
        // token2 添加角色
        addOk(dto, mock_token_org2_user1);


        List<Role> roles = roleRepository.findAll();
        Assertions.assertNotNull(roles);
        Assertions.assertEquals(roles.size(), 4);//有4个角色了
        Role role1 = roles.stream().filter(i -> i.getOrgId() == 1 && "角色1".equals(i.getName())).findFirst().orElse(null);
        Role role2 = roles.stream().filter(i -> i.getOrgId() == 2 && "角色1".equals(i.getName())).findFirst().orElse(null);
        Assertions.assertNotNull(role1);
        Assertions.assertNotNull(role2);
        // token1 添加一个角色时，通过detail检查该角色会存在，并且权限数量也一致
        detailOk(role1.getId(), mock_token_org1_user1, true, false);
        // token2 添加一个角色时，通过detail检查该角色会存在，并且权限数量也一致
        detailOk(role2.getId(), mock_token_org2_user1, true, false);

        // test update
        // role1 取消所有权限
        dto = new RoleRequestDto();
        role.setName("角色2");
        role.setId(role1.getId());
        dto.setRole(RoleSimpleDto.map(role));
        dto.setMenus(Lists.newArrayList(
                newMenu("/p1/p2/view", false),
                newMenu("/p1/p2/edit", false)
        ));
        addOk(dto, mock_token_org1_user1);
        detailOk(role1.getId(), mock_token_org1_user1, false, false);

        // 无 token 返回 401
        addNoToken(dto);
    }

    @Test
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_MENU)
    @Sql(statements = INIT_SQL_USER)
    void roleAddRemoveUser() throws Exception {
        // token1 角色1增加 1,2,3 这3个用户
        RoleUserRequestDto dto = new RoleUserRequestDto();
        dto.setUserIds("1,2,3");
        postOk(String.format(URL_ADD_USER, 1), dto, mock_token_org1_user1, null);

        // token1 校验这3个用户
        List<UserRole> list = userRoleRepository.findAll();
        Assertions.assertEquals(list.size(), 3);
        list.forEach(i -> Assertions.assertEquals(i.getRole().getId(), 1));
        String ids = list.stream().map(i -> i.getUser().getId()).sorted().map(Object::toString).collect(Collectors.joining(","));
        Assertions.assertEquals(ids, "1,2,3");

        // 添加了其他的 未知 的用户，会400
        dto.setUserIds("1,2,7");
        post400(String.format(URL_ADD_USER, 1), dto, mock_token_org1_user1, ErrorCode.BAD_PARAMETER);

        // token1 删除 1,3 这两个用户
        dto.setUserIds("1,3");
        postOk(String.format(URL_REMOVE_USER, 1), dto, mock_token_org1_user1, null);

        // token1 校验剩下的 2 这个用户
        list = userRoleRepository.findAll();
        Assertions.assertEquals(list.size(), 1);
        list.forEach(i -> Assertions.assertEquals(i.getRole().getId(), 1));
        ids = list.stream().map(i -> i.getUser().getId()).sorted().map(Object::toString).collect(Collectors.joining(","));
        Assertions.assertEquals(ids, "2");
    }


    private void addOk(RoleRequestDto dto, String token) throws Exception {
        postOk(URL_SAVE, dto, token, actions -> actions.andExpect(jsonPath("$.data").value(true)));
    }

    private void addNoToken(RoleRequestDto dto) throws Exception {
        post401(URL_SAVE, dto);
    }

    private MenuTreeDto newMenu(String fullPath, boolean select) {
        MenuTreeDto menu = new MenuTreeDto();
        menu.setFullPath(fullPath);
        menu.setSelected(select);
        return menu;
    }

    private static final String INIT_SQL_ROLE = "INSERT INTO `role` " +
            "(`id`,`create_time`, `modify_time`, `org_id`, `description`, `name`, `platform`, `editable`, `type`) VALUES" +
            "('1', now(), now(), '1', 'role1', '超级管理员', 'cem', 0, 1)," +
            "('2', now(), now(), '1', 'role1', '客户管理', 'cem', 0, 4)," +
            "('3', now(), now(), '1', 'role1', '普通用户', 'cem', 1, 4)," +

            "('4', now(), now(), '2', 'role4', '超级管理员', 'cem', 0, 1)," +
            "('5', now(), now(), '2', 'role5', '客户管理', 'cem', 0, 4)," +
            "('6', now(), now(), '2', 'role6', '普通用户', 'cem', 1, 4);";

    private static final String INIT_SQL_PERMISSION = "INSERT INTO `permission` " +
            "(`id`,`create_time`,`modify_time`,`module`,`permission`,`role_id`) VALUES" +
            "(1,now(),now(),'Action','/p1/p2/edit',1)," +
            "(2,now(),now(),'Action','/p1/p2/view',1)," +
            "(3,now(),now(),'Action','/p1/p2/view',4)," +
            "(4,now(),now(),'Action','/p1/p2/edit',4)," +
            "(5,now(),now(),'Action','/p1/p2/edit',5);";

    private static final String INIT_SQL_MENU = "INSERT INTO `menu` " +
            "(`id`, `create_time`, `modify_time`, `pid`, `type`, `display`, `name`, `full_name`,  `path`, `full_path`, `sequence`) VALUES " +
            "('1', now(), now(), '0', '3', '1', '查看', '查看', 'view', '/p1/p2/view', '1')," +
            "('2', now(), now(), '0', '3', '1', '编辑', '编辑', 'edit', '/p1/p2/edit', '2');";
    private static final String INIT_SQL_USER = "INSERT INTO `user`" +
            "(id, org_id, is_delete, create_time, modify_time)VALUES" +
            "(1, 1, 0, now(),now())," +
            "(2, 2, 0, now(),now())," +
            "(3, 1, 0, now(),now())," +
            "(4, 2, 0, now(),now());";

}