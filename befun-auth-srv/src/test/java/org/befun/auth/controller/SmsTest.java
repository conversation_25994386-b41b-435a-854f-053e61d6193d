package org.befun.auth.controller;

import org.apache.commons.lang3.StringUtils;
import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.constant.VerifyCodeType;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.*;
import org.befun.core.constant.ErrorCode;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.property.SmsProperty;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.service.MailService;
import org.befun.extension.service.SmsService;
import org.h2.engine.SysProperties;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class SmsTest extends AssertController {

    @Autowired
    private SmsProperty smsProperty;

    @Test
    void smsConfig() throws Exception {
        String verify_code = "您的验证码是123456，请于5分钟内填写。如非本人操作，请忽略本短信";
        String event_action_warning = "张三1️⃣，您好！您收到一条高预警事件：你中奖了，请尽快登录体验家XM处理！";
        String event_action_cooperation = "张三1️⃣，您好！您收到一条李四0️⃣的事件协作邀请，请尽快登录体验家XM处理！";
        String journey_indicator_warning = "张三1️⃣，您好！您收到一条指标预警：昨日平均额的值为100.01(过低)，请尽快登录体验家XM处理！";
        String PLATFORM_FEEDBACK = "尊敬的张三1️⃣，您好！感谢您的光临，为了带给您更好的服务体验，请对我们做出评价：https://dev-t.xmplus.cn/31dae123aca";
        String PLATFORM_EVENT_ACTION = "尊敬的张三1️⃣，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。";
        String PLATFORM_RESEARCH = "尊敬的张三1️⃣，您好！特邀您参加本次问卷调查，点击链接马上填答：https://dev-t.xmplus.cn/31dae123aca";

        smsProperty.getProviders().stream().filter(i -> i.getName().equals(smsProperty.getVendor())).findFirst().ifPresent(feige -> {
            feige.getTemplates().forEach(t -> {
                if (t.getName().equals("verify-code")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, verify_code);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(1, variableSections.size());
                    Assertions.assertEquals("123456", variableSections.get(0).getValue());
                } else if (t.getName().equals("event-action-warning")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, event_action_warning);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(3, variableSections.size());
                    Assertions.assertEquals("张三1️⃣", variableSections.get(0).getValue());
                    Assertions.assertEquals("高", variableSections.get(1).getValue());
                    Assertions.assertEquals("你中奖了", variableSections.get(2).getValue());
                } else if (t.getName().equals("event-action-cooperation")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, event_action_cooperation);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(2, variableSections.size());
                    Assertions.assertEquals("张三1️⃣", variableSections.get(0).getValue());
                    Assertions.assertEquals("李四0️⃣", variableSections.get(1).getValue());
                } else if (t.getName().equals("journey-indicator-warning")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, journey_indicator_warning);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(4, variableSections.size());
                    Assertions.assertEquals("张三1️⃣", variableSections.get(0).getValue());
                    Assertions.assertEquals("昨日平均额", variableSections.get(1).getValue());
                    Assertions.assertEquals("100.01", variableSections.get(2).getValue());
                    Assertions.assertEquals("过低", variableSections.get(3).getValue());
                } else if (t.getName().equals("PLATFORM_FEEDBACK")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, PLATFORM_FEEDBACK);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(2, variableSections.size());
                    Assertions.assertEquals("张三1️⃣", variableSections.get(0).getValue());
                    Assertions.assertEquals("31dae123aca", variableSections.get(1).getValue());
                } else if (t.getName().equals("PLATFORM_EVENT_ACTION")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, PLATFORM_EVENT_ACTION);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(1, variableSections.size());
                    Assertions.assertEquals("张三1️⃣", variableSections.get(0).getValue());
                } else if (t.getName().equals("PLATFORM_RESEARCH")) {
                    List<SmsNotifyInfo.TemplateVariableValue> variableSections = parseVariables(t, PLATFORM_RESEARCH);
                    Assertions.assertNotNull(variableSections);
                    Assertions.assertEquals(2, variableSections.size());
                    Assertions.assertEquals("张三1️⃣", variableSections.get(0).getValue());
                    Assertions.assertEquals("31dae123aca", variableSections.get(1).getValue());
                }
            });
        });

    }

    private List<SmsNotifyInfo.TemplateVariableValue> parseVariables(SmsTemplateProperty template,String content) {
        List<SmsNotifyInfo.TemplateVariableValue> variableValues = new ArrayList<>();
        if (StringUtils.isNotEmpty(template.getPattern()) && StringUtils.isNotEmpty(template.getVariables())) {
            Pattern pattern = Pattern.compile(template.getPattern());
            Matcher matcher = pattern.matcher(content);
            String[] variables = template.getVariables().split(",");
            if (matcher.find() && matcher.groupCount() == variables.length) {
                IntStream.range(0, variables.length).forEach(i -> {
                    variableValues.add(new SmsNotifyInfo.TemplateVariableValue(variables[i], matcher.group(i+1)));
                });
            }
        }
        return variableValues;
    }
}