package org.befun.auth.controller;

import org.befun.auth.AssertController;
import org.befun.auth.TestRedisConfiguration;
import org.befun.auth.constant.UserGuideIndoType;
import org.befun.auth.dto.UserChangeInfoRequestDto;
import org.befun.auth.dto.UserChangePasswordRequestDto;
import org.befun.auth.dto.UserGuideInfoRequestDto;
import org.befun.auth.dto.UserSaveRequestDto;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserRole;
import org.befun.auth.repository.ThirdPartyUserRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.repository.UserRoleRepository;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.constant.ErrorCode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class UserControllerTest extends AssertController {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserRoleRepository userRoleRepository;
    @Autowired
    private ThirdPartyUserRepository thirdPartyUserRepository;

    private static final String URL_LIST = "/users?_page=1&_limit=20" +
            "&_q=%s&departmentIds=%s&roleIds=%s&status=%s";
    private static final String URL_LIST_COOPERATION = "/users/list/cooperation";
    private static final String URL_SAVE_USER = "/users/save";
    private static final String URL_ENABLE_USER = "/users/%d/enable";
    private static final String URL_DISABLE_USER = "/users/%d/disable";
    private static final String URL_USERINFO = "/users/current/userinfo";
    private static final String URL_CURRENT_UPDATE_TRUENAME = "/users/current/update-info";
    private static final String URL_CURRENT_UPDATE_PASSWORD = "/users/current/update-password";
    private static final String URL_CURRENT_UPDATE_UPDATE_GUIDE_INFO = "/users/current/update-guide";

    @Test
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_USER_ROLE)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    void list() throws Exception {
        // token1 无条件查询，返回org1的所有数据 3 条
        getOk(String.format(URL_LIST, "", "", "", ""), mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.items.size()").value(3))
        );
        // token1 搜索关键字=4，返回org1的数据 1 条
        getOk(String.format(URL_LIST, "4", "", "", ""), mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.items.size()").value(1))
        );
        // token1 搜索关键字=3，部门id=2，返回org1的数据 1 条
        getOk(String.format(URL_LIST, "3", "2", "", ""), mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.items.size()").value(1))
        );
        // token1 搜索角色id=2，返回org1的数据 2 条
        getOk(String.format(URL_LIST, "", "", "2", ""), mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.items.size()").value(2))
        );
        // token1 搜索状态=2，返回org1的数据 2 条
        getOk(String.format(URL_LIST, "", "", "", "2"), mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.items.size()").value(2))
        );

    }

    @Test
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_USER_ROLE)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    @Sql(statements = INIT_SQL_PERMISSION)
    void listCooperation() throws Exception {
        // 查询到id=(1) id=4角色不匹配,id=3是自己
        getOk(URL_LIST_COOPERATION, mock_token_org1_user2, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.items.size()").value(1))
        );
    }

//    @Test 新增用户接口已移除
//    @Sql(statements = INIT_SQL_ROLE)
//    @Sql(statements = INIT_SQL_DEPARTMENT)
    void saveUser() throws Exception {
        UserSaveRequestDto dto = UserSaveRequestDto.builder()
                .username("test1")
                .truename("张三")
                .password("123456")
                .email("<EMAIL>")
                .mobile("1860000000")
                .departmentId(3L)
                .roleIds("1,2,3")
                .build();
        postOk(URL_SAVE_USER, dto, mock_token_org1_user1, null);
        // 查到新增的用户
        List<User> users = userRepository.findAll();
        Assertions.assertNotNull(users);
        Assertions.assertEquals(users.size(), 1);
        List<UserRole> userRoles = userRoleRepository.findAll();
        Assertions.assertNotNull(userRoles);
        Assertions.assertEquals(userRoles.size(), 3);

        // update role
        dto.setId(users.get(0).getId());
        dto.setRoleIds("1");
        postOk(URL_SAVE_USER, dto, mock_token_org1_user1, null);
        users = userRepository.findAll();
        Assertions.assertNotNull(users);
        Assertions.assertEquals(users.size(), 1);
        userRoles = userRoleRepository.findAll();
        Assertions.assertNotNull(userRoles);
        Assertions.assertEquals(userRoles.size(), 1);

        // 400 用户名已存在
        dto.setId(null);
        post400(URL_SAVE_USER, dto, mock_token_org1_user1, ErrorCode.BAD_PARAMETER);

    }

    @Test
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_USER_ROLE)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    @Sql(statements = INIT_SQL_PERMISSION)
    void userStatus() throws Exception {
        postOk(String.format(URL_DISABLE_USER, 3), null, mock_token_org1_user1, null);
        Optional<User> user = userRepository.findById(3L);
        Assertions.assertTrue(user.isPresent());
        Assertions.assertEquals(user.get().getStatus(), 2);

        postOk(String.format(URL_ENABLE_USER, 3), null, mock_token_org1_user1, null);
        user = userRepository.findById(3L);
        Assertions.assertTrue(user.isPresent());
        Assertions.assertEquals(user.get().getStatus(), 1);
    }

    @Test
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_USER_ROLE)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    @Sql(statements = INIT_SQL_PERMISSION)
    void currentInfo() throws Exception {
        getOk(URL_USERINFO, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.truename").value("truename1"))
        );
    }


    @Test
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_USER_ROLE)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    @Sql(statements = INIT_SQL_PERMISSION)
    void updateTruename() throws Exception {
        UserChangeInfoRequestDto dto = new UserChangeInfoRequestDto();
        dto.setTruename("yym1");
        postOk(URL_CURRENT_UPDATE_TRUENAME, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
        );
        User user = userRepository.findById(1L).orElse(null);
        Assertions.assertNotNull(user);
        Assertions.assertEquals(user.getTruename(), "yym1");
    }

    @Test
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_ORG)
    @Sql(statements = INIT_SQL_ROLE)
    @Sql(statements = INIT_SQL_USER_ROLE)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    @Sql(statements = INIT_SQL_PERMISSION)
    void updatePassword() throws Exception {
        UserChangePasswordRequestDto dto = new UserChangePasswordRequestDto();
        dto.setOldPassword("yeyuming1");
        dto.setNewPassword("yym1");
        postOk(URL_CURRENT_UPDATE_PASSWORD, dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
        );
        User user = userRepository.findById(1L).orElse(null);
        Assertions.assertNotNull(user);
        Assertions.assertTrue(PasswordHelper.verify("yym1", user.getPassword()));
    }

    @Test
    @Sql(statements = INIT_SQL_USER)
    void updateUserGuideInfo() throws Exception {

        UserGuideInfoRequestDto dto = new UserGuideInfoRequestDto();
        dto.setType(UserGuideIndoType.SURVEY);

        postOk(URL_CURRENT_UPDATE_UPDATE_GUIDE_INFO, dto ,mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.code").value(200))
        );
        User user = userRepository.findById(1L).orElse(null);
        Assertions.assertNotNull(user);
        Assertions.assertTrue(user.getGuideInfo().contains(UserGuideIndoType.SURVEY.name()));
    }


    private static final String INIT_SQL_USER = "INSERT INTO `user`" +
            "(id, org_id, is_admin, department_ids, role_id, username, password, truename, status, is_delete, create_time, modify_time)VALUES" +
            "(1,  1,      1,        '[[1]]',             '1', 'user1', '$2y$10$piPZMLnbUw2N3LnJFRZTw.mxdTM5lu6hpe65G/6n9Q0VM2cW/QMyG', 'truename1',  1, 0, now(),now())," +
            "(4,  1,      0,        '[[2]]',             '2', 'user3', '', 'truename3',  2, 0, now(),now())," +
            "(3,  1,      0,        '[[3]]',             '2', 'user4', '', 'truename4',  2, 0, now(),now())," +

            "(2, 2, 1, '[[4]]', '4', 'user2','', 'truename2',  2, 0, now(),now())," +
            "(5, 2, 0, '[[3]]', '3', 'user5','', 'truename5',  2, 0, now(),now())," +
            "(6, 2, 0, '[[4]]', '5', 'user4','', 'truename4',  1, 0, now(),now());";

    private static final String INIT_SQL_USER_ROLE = "INSERT INTO `user_role`" +
            "(id, user_id, role_id,`create_time`, `modify_time`)VALUES" +
            "(1,  1,       1, now(),now())," +
            "(2,  1,       2, now(),now())," +
            "(3,  1,       3, now(),now())," +
            "(4,  3,       2, now(),now())," +
            "(5,  4,       4, now(),now())," +

            "(6,  6,       4, now(),now())," +
            "(7,  6,       5, now(),now());";

    private static final String INIT_SQL_DEPARTMENT = "INSERT INTO `department` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `pid`, `title`) VALUES" +
            "('1', now(), now(), '1', 0, '广东')," +
            "('2', now(), now(), '1', 1, '深圳')," +
            "('3', now(), now(), '1', 2, '南山')," +
            "('4', now(), now(), '2', 0, '江西')," +
            "('5', now(), now(), '2', 4, '九江');";

    private static final String INIT_SQL_ROLE = "INSERT INTO `role` " +
            "(`id`,`create_time`, `modify_time`, `org_id`, `description`, `name`, `platform`, `editable`) VALUES" +
            "('1', now(), now(), '1', 'role1', '超级管理员', 'cem', 0)," +
            "('2', now(), now(), '1', 'role2', '客户管理', 'cem', 0)," +
            "('3', now(), now(), '1', 'role3', '普通用户', 'cem', 1)," +

            "('4', now(), now(), '2', 'role4', '超级管理员', 'cem', 0)," +
            "('5', now(), now(), '2', 'role5', '客户管理', 'cem', 0);";

    private static final String INIT_SQL_PERMISSION = "INSERT INTO `permission` " +
            "(`id`,`create_time`,`modify_time`,`module`,`permission`,`role_id`) VALUES" +
            "(1,now(),now(),'Action','/Events/EventAction/edit',1)," +
            "(2,now(),now(),'Action','/Events/EventAction/edit',2)," +
            "(4,now(),now(),'Action','/Events/EventAction/edit',3);";

    private static final String INIT_SQL_ORG = "INSERT INTO organization (id,name,industry_id,owner_id,max_users,is_block,code,is_template,available_date_begin,available_date_end,created,updated,version,wework_code,wework_corp_id,wework_agent_id,wework_expire,modify_time,create_time) VALUES" +
            " (1,'test_yuming',6,1,20,0,'test_yuming',0,'2021-08-19 00:00:00','2023-01-01 00:00:00','2021-08-19 16:56:31',NULL,'{\"surveyplus_version\":\"base\",\"cem_version\":\"profession\"}',NULL,NULL,NULL,NULL,NULL,NULL);";

}