xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${SHORTURL:https://dev-t.xmplus.cn}
prod-env: ${PROD_ENV:false}
spring:
  main:
    allow-circular-references: true
  config:
    import:
     - classpath:webservice.yml
     - classpath:befun-sms.yml
     - classpath:befun-xpack-wechatopen.yml
  datasource:
    driverClassName: org.h2.Driver
    url: jdbc:h2:mem:myDb1;DB_CLOSE_DELAY=-1
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create
      generate-ddl: true
  redis:
    host: localhost
    port: 6378

logging:
  level:
    root: ${LOG_LEVEL:info}
    org.hibernate.type.BasicTypeRegistry: error
    org:
      hibernate:
        SQL: info
        type: trace

befun:
  auth:
    bind-expiration-in-seconds: 300
    domain: ${BEFUN_AUTH_DOMAIN:http://auth.befun.org}
    token-expire-hours: 168
    enable-provider: true
    apps:
      - cem
    verify-code:
      code-expire-minutes: 5
      code-freeze-minutes: 1
      sms-template-name:
        register: register-verify-code
        login: login-verify-code
        bind: bind-verify-code
        reset-password: reset-password-verify-code
      email-template-name:
        register: register-verify-code
        login: login-verify-code
        bind: bind-verify-code
        reset-password: reset-password-verify-code
    invitation:
      expire-hours: 72
      invite-url: ${befun.auth.domain}/cem/invite?code=
      email-template-name: invitation-user
    recharge:
      enabled:
        recharge_wechat: true
        recharge_alipay: false
  extension:
    wechat-pay:
      enable: ${WECHAT_PAY_ENABLE:true}
      app-id: ${WECHAT_PAY_APP_ID:1}
      mch-id: ${WECHAT_PAY_MCH_ID:1}
      mck-key: ${WECHAT_PAY_MCH_KEY:1}
      v2-cert-pem-base64: ${WECHAT_PAY_V2_CERT_PEM_BASE64:1}
      v2-cert-pem-path: ${WECHAT_PAY_V2_CERT_PEM_PATH:/etc/ssl/certs/wechat-pay-v2-cert.p12}
      v3-private-key-base64: ${WECHAT_PAY_V3_PRIVATE_KEY_BASE64:1}
      v3-private-key-path: ${WECHAT_PAY_V3_PRIVATE_KEY_PATH:/etc/ssl/private/wechat-pay-v3-private.key}
      v3-cert-pem-base64: ${WECHAT_PAY_V3_CERT_PEM_BASE64:1}
      v3-cert-pem-path: ${WECHAT_PAY_V3_CERT_PEM_PATH:/etc/ssl/certs/wechat-pay-v3-cert.pem}
      use-sandbox: ${WECHAT_PAY_USE_SANDBOX:false}
      pay-notify-url: ${WECHAT_PAY_NOTIFY_URL:https://dev.xmplus.cn/api/auth/wechatPay/placeOrder/callback/cem}
    toast-message:
      enable: true
      messages:
        test: 测试
    mail:
      host: ${SMTP_HOST:smtp.partner.outlook.cn}
      port: ${SMTP_PORT:1234}
      username: ${SMTP_USER:<EMAIL>}
      from: ${SMTP_FROM:<EMAIL>}
      password: ${SMTP_PASSWORD:password}
      templates:
        - name: default
          subject: 标题${name}
          content: 尊敬的用户，已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！
        - name: login-verify-code
          subject: 邮箱登录验证码
          content: 登录验证码：${code}
        - name: bind-verify-code
          subject: 邮箱绑定验证码
          content: 绑定验证码：${code}
        - name: reset-password-verify-code
          subject: 邮箱重置密码验证码
          content: 验证码：${code}
        - name: invitation-user
          enable-html: true
          subject: ${orgName} ${truename} 邀请您加入体验家平台
          content:
            '
            <h1>Hi, ${email} </h1>
            ${orgName} ${truename} 邀请您加入体验家平台 <br/>
            请点击以下链接加入团队 <br/>
            <a href="${inviteUrl}">${inviteUrl}</a>
            '
message:
  event:
    register:
      topic: ${REGISTER_TOPIC:queuing-user-create}
    sync-register:
      topic: ${REGISTER_TOPIC:queuing-user-create-sync}
    sync-cloud:
      topic: ${CLOUD_SYNC_TOPIC:sync-cloud}

worker:
  producer:
    event.enabled.auth: false
    task.enabled.auth: false