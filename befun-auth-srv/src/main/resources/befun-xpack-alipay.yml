befun:
  extension:
    alipay:
      enable: ${ALIPAY_ENABLE:true}
      app-id: ${ALIPAY_APP_ID:2021004143689114}
      app-private-key: ${ALIPAY_APP_PRIVATE_KEY:MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCa9VOYzGqWmu3YS0VQU3vEH0/Cc2nbWB9PrCjMc+er7twPO8EbreqjdkPRwazbfwj/R+eWTQOiPqclzfMSn8B9mVL/+QakSDEM5mFvC0fkQo0mkO0td5eN4gZtfp4qB+LefdxieX0Z1WIO7HSLd8YE0PXtw50FMAbykG8WsZonmpqVHgARRI1QFeen9J6nsbvmoUIvgcwvr9l0Z0/cnKrVMQi4Ky1ZunGO5tgYfYCEuYRwMmRQsahoRioMZjYFiQ9aBpnOUA3TUDikIApQdjLuBuDVktb0e0lq+vSEOkYwkSrr9amOcW7D74Wei/Z8pothE5zzs2waw8h8gEdd8Ej/AgMBAAECggEAVanlywTGr/d6ISaLbPqZ4zQEHdU74fx3Ys4jx9MTceMcWHeKK8kbe743g8/zxWwXBzky984fgiPst2OBSvxxp32JeuSqHUujgiQiHQc5eSCpc3aPB6GjcNZRA1sKCOZPLvyZRv8uf4eNK/7f9vIB0EvMiOKo/WMtlA7wurT4KYfbKR8+R5AA60cQVswgJ1yCMYD/6T29yQkMhmUhyMvYMcJdPwKxU7CRodQdgthIlLVNjPzjmFSrx25b2+IySp3Dud5TBmB6ntGN8trha6bl7tX4YV7hoEE9aeu8ZjsFcERUik3+6gs+2CZdvaycPW18Jp3xIfs2+Qo7oaYEdybd8QKBgQDcsuHighodfTfvRNPiXO+EbSZM7c/nyTDL2JpCgHtJ0yX/8bZzKZeqQ4CNIAYlWidKjVFhvrrgJXH2gXAJynFy/9oXswOfaa/ExcInuVyXuQjz9A+16naGLyGvVmBBdX0DByZllntFVS1CApzt2eWRsn6DP76FTt6xA4QA7mGrgwKBgQCzvoVEPy1Q+pBVBeVuYuQty0gZ8QEGFd4iy9ob3BkBfjdqY4+lcIzJdDNEYNidvB3nglcw+IgdQ5ATiEMT2PWtdFiJeoJH81AfC70PzwSRFyVb5FRm3wJOVyEdXqOggxqQ51l+14qyQyc8z7XtXGDzqTnLtZfvWDAvHbzvXFQH1QKBgDabrHi3aG7ThPuXNDZWeI1z1H5EeaUIiyJlPgYmdFsX+9cQn+xu5jAi87P3sI2deQthXDmrT+IWYdR0wpwpk1i5JeWnTcYsCGnP5PKfUuHXzvdN9HU2xoz9LICOBtMrlvsg0cdhLK9DHCexLdNMjxk16UfghevGUCPMkuUIFvKxAoGALf54l4gbDnevazJBDUgc32L/JjQ3cNkzqG/Fdg+SDoG/qGeloE32mK41WC/3/hTfChieOxe9rM9XFMhvcaq44xO8fCFvKP4n6GnWjS8KGh50HtmEh3ZVXrqI7L0E3dPuflnU2tJI2Wi5p6P3B6/11yPceDYVlNdaLRRM/mr/zakCgYAjQaewScHwhon83o7nZdrFv3B+9U/6e+RvV9e/DgYNnw+QLGS3Pck/OPNcvzlGHhZPvRSSMQbVSxdE9zWgSV6k7oU532tYYRJKekBOWqHC93WkZCQSNIYMtJYxHzF6Tt0j1o/Zkr4IBvKjh22mdjUNffMERjMkPZu+WCBr4OKRTw==}
      app-public-key: ${ALIPAY_APP_PUBLIC_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmvVTmMxqlprt2EtFUFN7xB9PwnNp21gfT6wozHPnq+7cDzvBG63qo3ZD0cGs238I/0fnlk0Doj6nJc3zEp/AfZlS//kGpEgxDOZhbwtH5EKNJpDtLXeXjeIGbX6eKgfi3n3cYnl9GdViDux0i3fGBND17cOdBTAG8pBvFrGaJ5qalR4AEUSNUBXnp/Sep7G75qFCL4HML6/ZdGdP3Jyq1TEIuCstWbpxjubYGH2AhLmEcDJkULGoaEYqDGY2BYkPWgaZzlAN01A4pCAKUHYy7gbg1ZLW9HtJavr0hDpGMJEq6/WpjnFuw++Fnov2fKaLYROc87NsGsPIfIBHXfBI/wIDAQAB}
      alipay-server-url: ${ALIPAY_SERVER_URL:https://openapi.alipay.com/gateway.do}
      alipay-public-key: ${ALIPAY_PUBLIC_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr9HcfELFGEEpQA7fHpRWtrPC69dQalcdOfhNuAQVDqg2fNbwGzEK77BPYB+TqW0Au05ygN/vdyAFJM+ImUs93aA9cMoZ9yAuYPiUd9ncnpkVRDKwgoHtc37G8cE5loBvlMqix1UVtQ/tzBfQvt3UctjcvPJVc4PimfdP/Tf2hPNtKeuq7Pl4wnFazAcVmH1CfjVkBZpwFU838HjryGa1wbX+xcWLk3Go7i+eyh8w9hUvM4KLOEUNKXg73Q67oSC+9nJCwOzwPE6oiMPH1l+fGDAIpdHVYfkHXShfUQQG0zHPD0Xq/EXw4UUJQWVFCaQ7wIcHMGvMz1dzO0XGem8/hwIDAQAB}
      sign-type: ${ALIPAY_SIGN_TYPE:RSA2}
      format: ${ALIPAY_FORMAT:json}
      charset: ${ALIPAY_CHARSET:utf-8}
      encrypt-key: ${ALIPAY_ENCRYPT_KEY:6+DHxMvpZPSuzBruszdaew==}
      encrypt-type: ${ALIPAY_ENCRYPT_TYPE:AES}
      pay-method: ${ALIPAY_PAY_METHOD:get}
      pay-notify-url: ${ALIPAY_NOTIFY_URL:${xmplus.domain}/api/auth/alipay/placeOrder/callback/cem}
      use-sandbox: ${ALIPAY_USE_SANDBOX:true}
      sandbox:
        app-id: ${ALIPAY_APP_ID_SANDBOX:9021000136650910}
        app-private-key: ${ALIPAY_APP_PRIVATE_KEY_SANDBOX:MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCiH4mZR+I3+OtBumIQgGR7u9VDzQiwi9ENZDBSXxCFS2L31zIw20UgdZy5KQifdsKVMc2CwiKZ3rp+Zg1zxdKFKvuFj+u2mb3HeH+B+YyAGY+R/7aKDlJFBOTo/iX3Q6yl2TE0MvmEOaJhrvJ8FzHAKlT/zmE5lFNwYsETS9oxOq8ap7UItlofejnx1VDaw2J4rVCVW/BnLaur5g84K2tg9gOiGsbmzQdoQFH+LTW93ncFT0W8wGiZQMOQ49YoKirl0MDBmtoDbKRyXi3qqpekLLQ1Kn+OXEEeqvO3XzfVN/wnQsa/0hKhgPxAmCXOeB98WG66lkua7kuqgbZxgbybAgMBAAECggEAC1g9nrZDBDus6rqKJjF11pq8P7RXSVNfW5qJLxpr44f08vbiEj0lxzGzt9lBOzdqQlpr0RyYoz/MuQ+XHWTYaG0rkJsTmYGEoDK0aXn29PfU7hiAw4RhCWdgRVWTfowDxs89/DMc9r25uTAu07QjzRipPgVJFGhmtlSSwjDqocsmpCTgHQFhqH+AGUJwCJADR1x/xUAx7WBd8RUE8QhalvoqB1KkfpLGaH0LY7cViWq/4VOr/3+zpen++swjYFEfE9RkQf4O8zWbL4RhmVnrqpcZ9ENTokWmtVSEaEAL+G9cylohxWX4RtmmqH5MOs0SYk/d9XN58cbKwkhmgC3vkQKBgQDsvQmmg+UsFDW2kAeYJOmLqqB8YaIeeWefXSFpVRE7MGccKraCVD89Qe94XmrFbBCuqtKp74E1p2mgQzz2RIyLqAizO0LWJpHK8TkKyQD54KB2Do2EcHRk0jLAJ/VYDVBdQpHAt4wNEpmKytCggv7asUkp1X3/P+a8CAsrRcu+NwKBgQCvUFvWXlP9X7eyV4CdadGWFhKu0LMOPZUvnjSDnAY5mum58bdXX1mY2BV2VHwlCx37e3JgAbyC50EJzd5aHTJBEn5ATSoTpTmdCtOTICBZTChKd5hoxhTo+KsDnz3fwQUjUbdhYilY6R3XYhOfIooPksbomm+/1lrt6fZVGkUivQKBgGTelwIQKNGDOu+ypsm40Jnl9cOJhh4d9Hz+nYHX8SObGBP0Txdw9hEPyw0qjavYw7LoriIiHpfR4Tub6pmz036d9jcIBQ5iGLSz224w/u15PGWRBdNsZuuedGlTnUILSjayapAGpPHc/vcYO5RovFZKO7ZLdQgRHir38mpZrto5AoGBAJUB8TzwKeUmsQTVWMzyvyAZHMIXQsls0FuL1UizmZrmr5fK+jE6KvY9W2ibAHWf0FkKoLk3YJnGosIJh98CB8/FPye6LnRKMADpzdC0MqqTj39TzWfoTKeDjB9k/vsqnQpbD0mnXEtkwGdrxg+xFLWN5KCZezgLonerLyNAU9jlAoGAUNsJdsf64qdw+ETjRJJvstJlLRs4d6rCyvkd+j4dLHKJgx1OnaMNBy9mLaQX1wshyy/EXhdak9/He3SK2kF8gPpDwCg9YD69LSurLSl54o+43QzIb+BX4Q4G5TAJfumUQNs/IcH6mPurMvijSh1dPleqLoknNpUMe96w0I/7zkU=}
        app-public-key: ${ALIPAY_APP_PUBLIC_KEY_SANDBOX:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoh+JmUfiN/jrQbpiEIBke7vVQ80IsIvRDWQwUl8QhUti99cyMNtFIHWcuSkIn3bClTHNgsIimd66fmYNc8XShSr7hY/rtpm9x3h/gfmMgBmPkf+2ig5SRQTk6P4l90OspdkxNDL5hDmiYa7yfBcxwCpU/85hOZRTcGLBE0vaMTqvGqe1CLZaH3o58dVQ2sNieK1QlVvwZy2rq+YPOCtrYPYDohrG5s0HaEBR/i01vd53BU9FvMBomUDDkOPWKCoq5dDAwZraA2ykcl4t6qqXpCy0NSp/jlxBHqrzt1831Tf8J0LGv9ISoYD8QJglzngffFhuupZLmu5LqoG2cYG8mwIDAQAB}
        alipay-server-url: ${ALIPAY_SERVER_URL_SANDBOX:https://openapi-sandbox.dl.alipaydev.com/gateway.do}
        alipay-public-key: ${ALIPAY_PUBLIC_KEY_SANDBOX:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhIBkuG3LEwrpj22vidW1sHxRr4+SvadKf5fqrcftsullQBqtl/6S/a0TYi6eNjSgcuBHKi0+2kPB23Sg6rMx42EeqeaaTmr9x2XXiiR9TROqf7NoH5J0G37iMNKF4hGdWiYQijjp9vPgzM/Plq98ffCKA8a09k0mhf7xe2ptH15OHl7ZLrsi1UAp+eqgzylGZWllYakcN4ashg4VGtPgj/RaM6sFs0N0/udpq4Aqsxkc6BTmz1y0zSUJlfh/4MW8OH7EBkKZkohJ8MnTIKvlSweAC9J0EJIsHTxsgeVy56M5LipfXgNYh4XOz22ge2r8fXVG9Um4JQKLzd3k5TsB3wIDAQAB}
        sign-type: ${ALIPAY_SIGN_TYPE_SANDBOX:RSA2}
        format: ${ALIPAY_FORMAT_SANDBOX:json}
        charset: ${ALIPAY_CHARSET_SANDBOX:utf-8}
        encrypt-key: ${ALIPAY_ENCRYPT_KEY_SANDBOX:WM30OF3lSjY/Z3i4ja3Uqw==}
        encrypt-type: ${ALIPAY_ENCRYPT_TYPE_SANDBOX:AES}
        pay-method: ${ALIPAY_PAY_METHOD_SANDBOX:get}
        pay-notify-url: ${ALIPAY_NOTIFY_URL_SANDBOX:${xmplus.domain}/api/auth/alipay/placeOrder/callback/cem/sandbox}