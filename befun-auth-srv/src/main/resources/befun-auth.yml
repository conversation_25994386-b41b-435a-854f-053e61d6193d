befun:
  auth:
    logout.path: ${LOGOUT_PATH:/cem}
    bind-expiration-in-seconds: 300
    graph-captcha-in-fail-times: ${GRAPH_CAPTCHA_IN_FAIL_TIMES:1}
    domain: ${BEFUN_AUTH_DOMAIN:http://127.0.0.1:8083}
    token-expire-hours: 48
    enable-provider: true
    enable-task-user-permission-change: true
    apps:
      - cem
      - surveyplus
    verify-code:
      code-cache-hours: 24
      code-expire-minutes: 5
      code-freeze-minutes: 1
      sms-template-name:
        register: verify-code
        login: verify-code
        bind: verify-code
        reset-password: verify-code
        mfa: verify-code
        survey-question-mobile: verify-code
      email-template-name:
        register: register-verify-code
        login: login-verify-code
        bind: bind-verify-code
        reset-password: reset-password-verify-code
        mfa: mfa-verify-code
    invitation:
      expire-time: 72h
      invite-url: /cem/invite?code=
      email-template-name: invitation-user
      email-template-name-wechat-work: invitation-user-wechat-work
      email-template-name-by-user-info: invitation-user-user-info
      copy-text: '%s邀请您加入「%s」体验家平台，访问链接加入：%s'
    wechat-mp-sources:
      - name: cem
        app-id: ${WECHAT_MP_CEM_APP_ID:wx6b8f8ad170862e83}
        app-secret: ${WECHAT_MP_CEM_APP_SECRET:b0e23de9f72b7578e9537845f07b3a98}
        token: ${WECHAT_MP_CEM_TOKEN:hanyidata}
      - name: surveyplus
        app-id: ${WECHAT_MP_SURVEYPLUS_APP_ID:wx6b8f8ad170862e83}
        app-secret: ${WECHAT_MP_SURVEYPLUS_APP_SECRET:b0e23de9f72b7578e9537845f07b3a98}
        token: ${WECHAT_MP_SURVEYPLUS_TOKEN:hanyidata}
    cas:
      cas-url: ${CAS_LOGIN_URL:https://dev.xmplus.cn/cem/cas?server=#{orgCode}&app=#{app}}
      cas-url-secondary: ${CAS_LOGIN_URL:https://dev.xmplus.cn/cem/cas?server=#{orgCode}&app=#{app}}
      white-list-auto-create-user: ${CAS_WHITE_LIST_AUTO_CREATE_USER:false}
    wechat-work:
      corp-id: ${WECHAT_WORK_CORP_ID:ww87224825795b8b14}
      corp-secret: ${WECHAT_WORK_CORP_SECRET:A9yEb8ehnR9T9P5x7-e0b05HGTAp-5iNrlHXTQ2U9CnrUa8Qiy20LzOZt0yAOEnU}
      suite-id: ${WECHAT_WORK_SUITE_ID:wwa6d398bb65010e5e}
      suite-secret: ${WECHAT_WORK_SUITE_SECRET:UNiPlpLcTXAN35klwB6zZnOOQqlbF3QX7BtbsEUTiIo}
      aes-key: ${WECHAT_WORK_AES_KEY:Aw2bynwMwrKnltnINlLWnldrnx953WBFmLF4GRU8fAP}
      token: ${WECHAT_WORK_TOKEN:SChlsiT5D0}
      login-user-type: ${WECHAT_WORK_LOGIN_USER_TYPE:member}
      login-callback-url: ${WECHAT_WORK_LOGIN_CALLBACK_URL:${xmplus.domain}/cem/wxworkPage?type=login}
      bind-callback-url: ${WECHAT_WORK_BIND_CALLBACK_URL:${xmplus.domain}/cem/wxworkPage?type=bind}
      bind-auth-type: ${WECHAT_WORK_BIND_AUTH_TYPE:0}
      member-user-type: ${WECHAT_WORK_MEMBER_USER_TYPE:member}
      member-callback-url: ${WECHAT_WORK_MEMBER_CALLBACK_URL:${xmplus.domain}/cem/wxworkPage?type=member}
      order-buyer-user-id: ${WECHAT_WORK_ORDER_BUYER_USER:YeYuMing}
      order-month: ${WECHAT_WORK_ORDER_MONTH:12}
      redirect-domain: ${xmplus.domain}
    org-default-config:
      customerVisible: '[{"prop":"username","label":"姓名","visible":true,"editable":false},{"prop":"journeyIndicator","label":"体验指标","visible":true,"editable":false},{"prop":"journeyEventStat","label":"预警统计","visible":true,"editable":false},{"prop":"latestJourneyRecord","label":"客户历程","visible":true,"editable":true},{"prop":"mobile","label":"手机号码","visible":true,"editable":true},{"prop":"email","label":"邮箱","visible":false,"editable":true},{"prop":"gender","label":"性别","visible":false,"editable":true},{"prop":"birthday","label":"出生日期","visible":false,"editable":true},{"prop":"area","label":"所在地区","visible":false,"editable":true},{"prop":"departmentName","label":"所属层级","visible":false,"editable":true},{"prop":"belongToUidNames","label":"所属员工","visible":false,"editable":true},{"prop":"groups","label":"所属分组","visible":false,"editable":true},{"prop":"tags","label":"标签","visible":true,"editable":true},{"prop":"createUserName","label":"创建人","visible":false,"editable":true},{"prop":"createTime","label":"创建时间","visible":false,"editable":true},{"prop":"modifyUserName","label":"修改人","visible":false,"editable":true},{"prop":"modifyTime","label":"修改时间","visible":false,"editable":true},{"prop":"countJourneyRecord","label":"客户历程数","visible":false,"editable":true},{"prop":"countSendSurvey","label":"问卷发送数","visible":false,"editable":true},{"prop":"countJoinSurvey","label":"问卷参与数","visible":false,"editable":true},{"prop":"countCompleteSurvey","label":"问卷完成数","visible":false,"editable":true},{"prop":"countEvent","label":"预警触发数","visible":false,"editable":true}]'
      surveyVerify: false
    youzan:
      client-id: ${YOUZAN_CLIENT_ID:165f362a0b87ffca6f}
      client-secret: ${YOUZAN_SECRET_ID:c1e3aeb4dd25a4341dc0f8575dfb98c1}
      enable-mock: ${YOUZAN_ENABLE_MOCK:false}
      support-events:
        trade_TradeBuyerPay: 买家付款
        trade_TradeSuccess: 交易完成
        trade_refund_RefundSellerAgree: 卖家同意退款（终态）
        trade_refund_RefundSuccess: 买家退货成功(终态)
        trade_refund_SysRefund: 一键退款/系统退款
        trade_refund_RefundClosed: 买家取消退款
    saml:
      login-url: ${SAML_LOGIN_URL:${xmplus.domain}/cem/saml?token=#{token}}
      sp-key: ${SAML_SP_KEY:}
      sp-crt: ${SAML_SP_CRT:}
      sp-url: ${SAML_SP_URL:${xmplus.domain}/api/auth/login/callback/saml-#{orgCode}/cem}
      sp-metadata-url: ${SAML_SP_METADATA_URL:${xmplus.domain}/api/auth/login/saml-#{orgCode}/cem/metadata}
      ignore-ssl: ${SAML_IGNORE_SSL:true}
      ssl-path: ${SAML_SSL_PATH:}


