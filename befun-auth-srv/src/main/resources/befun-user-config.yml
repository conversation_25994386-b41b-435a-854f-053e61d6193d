befun:
  auth:
    user-config:
      customer-query:
        - property-name: username
          property-label: 姓名
          query-item-type: textInput
        - property-name: externalUserId
          property-label: 客户编号
          query-item-type: textInput
        - property-name: journeyIndicatorScore
          property-label: 体验指标
          query-item-type: numberInput
        - property-name: mostEventRuleTimes
          property-label: 预警统计
          query-item-type: numberInput
        - property-name: latestJourneyRecord
          property-label: 客户历程
          query-item-type: textInput
        - property-name: mobile
          property-label: 手机号码
          query-item-type: textInput
        - property-name: email
          property-label: 邮箱
          query-item-type: textInput
        - property-name: gender
          property-label: 性别
          query-item-type: oneToOneSelect
          property-source: customer
          property-column: gender
          property-type: date
          enable-batch-update: true
          input-type: SINGLE_CHOICE
        - property-name: birthday
          property-label: 出生日期
          query-item-type: dateSelect
          property-source: customer
          property-column: birthday
          property-type: string
          enable-batch-update: true
          input-type: DATE
        - property-name: province
          property-label: 所在省
          query-item-type: oneToOneSelect
          property-source: customer
          property-column: province
          property-type: string
          enable-batch-update: true
          input-type: SINGLE_CHOICE
        - property-name: city
          property-label: 所在市
          query-item-type: oneToOneSelect
          property-source: customer
          property-column: city
          property-type: string
          enable-batch-update: true
          input-type: SINGLE_CHOICE
        - property-name: district
          property-label: 所在区
          query-item-type: oneToOneSelect
          property-source: customer
          property-column: district
          property-type: string
          enable-batch-update: true
          input-type: SINGLE_CHOICE
        - property-name: address
          property-label: 详细地址
          query-item-type: textInput
          property-source: customer
          property-column: address
          property-type: string
          enable-batch-update: true
          input-type: TEXT
        - property-name: wechatConfigId
          property-label: 微信公众号
          query-item-type: oneToManyInSelect
        - property-name: openId
          property-label: OpenId
          query-item-type: textInput
        - property-name: departmentId
          property-label: 所属层级
          query-item-type: oneToOneSelect
          property-source: customer
          property-column: department_id
          property-type: long
          enable-batch-update: true
          input-type: SINGLE_CHOICE
        - property-name: belongToUids
          property-label: 所属员工
          query-item-type: oneToManyLikeSelect
          property-source: customer
          property-column: belong_to_uids
          property-type: string
          enable-batch-update: true
          input-type: MULTIPLE_CHOICE
        - property-name: groupId
          property-label: 所属分组
          query-item-type: oneToManyInSelect
          property-source: customer
          enable-batch-update: true
          input-type: MULTIPLE_CHOICE
        - property-name: tags
          property-label: 标签
          query-item-type: textInput
          property-source: customer
          property-column: tags
          property-type: arrayString
          enable-batch-update: true
          input-type: ARRAY_TEXT
        - property-name: createdByUid
          property-label: 创建人
          query-item-type: oneToOneSelect
        - property-name: createTime
          property-label: 创建时间
          query-item-type: dateSelect
        - property-name: modifiedByUid
          property-label: 修改人
          query-item-type: oneToOneSelect
        - property-name: modifyTime
          property-label: 修改时间
          query-item-type: dateSelect
        - property-name: countJourneyRecord
          property-label: 客户历程数
          query-item-type: numberInput
        - property-name: countSendSurvey
          property-label: 问卷发送数
          query-item-type: numberInput
        - property-name: countJoinSurvey
          property-label: 问卷参与数
          query-item-type: numberInput
        - property-name: countCompleteSurvey
          property-label: 问卷完成数
          query-item-type: numberInput
        - property-name: countEvent
          property-label: 预警触发数
          query-item-type: numberInput
      customer-query-extend-field:
        TEXT: textInput
        NUMBER: numberInput
        DATE: dateSelect
        SINGLE_CHOICE: oneToManyInSelect
        MULTIPLE_CHOICE: oneToManyLikeSelect
      customer-query-type:
        textInput:
          - query-type-label: 包含
            query-type: like
            query-value-type: inputText
          - query-type-label: 不包含
            query-type: notLike
            query-value-type: inputText
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        numberInput:
          - query-type-label: 等于
            query-type: eq
            query-value-type: inputNumber
          - query-type-label: 不等于
            query-type: neq
            query-value-type: inputNumber
          - query-type-label: 大于
            query-type: gt
            query-value-type: inputNumber
          - query-type-label: 大于等于
            query-type: ge
            query-value-type: inputNumber
          - query-type-label: 小于
            query-type: lt
            query-value-type: inputNumber
          - query-type-label: 小于等于
            query-type: le
            query-value-type: inputNumber
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        oneToOneSelect:
          - query-type-label: 等于
            query-type: eq
            query-value-type: selectMulti
          - query-type-label: 不等于
            query-type: neq
            query-value-type: selectMulti
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        oneToManyInSelect:
          - query-type-label: 包含
            query-type: in
            query-value-type: selectMulti
          - query-type-label: 不包含
            query-type: notIn
            query-value-type: selectMulti
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        oneToManyLikeSelect:
          - query-type-label: 包含
            query-type: like
            query-value-type: selectMulti
          - query-type-label: 不包含
            query-type: notLike
            query-value-type: selectMulti
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        dateSelect:
          - query-type-label: 等于
            query-type: eq
            query-value-type: selectDate
          - query-type-label: 不等于
            query-type: neq
            query-value-type: selectDate
          - query-type-label: 早于
            query-type: lt
            query-value-type: selectDate
          - query-type-label: 晚于
            query-type: gt
            query-value-type: selectDate
          - query-type-label: 介于
            query-type: range
            query-value-type: selectDateRange
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
          - query-type-label: 最近N天
            query-type: beforeDays
            query-value-type: inputNumber
#          - query-type-label: 后N天
#            query-type: afterDays
#            query-value-type: inputNumber

