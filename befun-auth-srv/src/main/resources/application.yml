xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${XMPLUS_SHORT:https://dev-t.xmplus.cn}
surveyplus:
  domain: ${SURVEYPLUS_DOMAIN:https://dev.surveyplus.cn}
server:
  shutdown: graceful
  port: ${PORT:8083}
  servlet:
    context-path: /api/auth
  error:
    whitelabel:
      enabled: false

spring:
  main:
    allow-circular-references: true
  lifecycle:
    timeout-per-shutdown-phase: 60s
  config:
    import:
      - classpath:befun-auth.yml
      - classpath:befun-auth-pay.yml
      - classpath:befun-mail.yml
      - classpath:befun-sms-chuanglan.yml
      - classpath:befun-task.yml
      - classpath:befun-metrics.yml
      - classpath:befun-template-file.yml
      - classpath:befun-toast.yml
      - classpath:befun-user-config.yml
      - classpath:befun-xpack-wechatopen.yml
      - classpath:befun-xpack-wechatpay.yml
      - classpath:befun-xpack-alipay.yml
      - classpath:befun-xpack-captcha.yml
  mvc:
    throw-exception-if-no-handler-found: true
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ${MYSQL_URL:*********************************************************************************************************}
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:Health2020@SZ}
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  jpa:
    show-sql: ${SHOW_SQL:true}
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
    rest:
      default-media-type: application/json
  cache:
    type: redis
  redis:
    host: ${REDIS_HOST:*********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:bsxrzQ2l2zm}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
#  activemq:
#    broker-url: "tcp://127.0.0.1:61616"
#    user: "admin"
#    password: "admin"
#    send-timeout: 10000

springdoc:
  swagger-ui.enabled: ${ENABLE_DOC:false}
  api-docs.enabled: ${ENABLE_DOC:false}

befun.extension.http-log.enable: ${LOG_HTTP:false}
befun.extension.operate-log.enable: ${LOG_OPERATE:true}

logging:
  level:
    root: ${LOG_LEVEL:info}
    org.befun: ${LOG_LEVEL_BEFUN:debug}
    org.befun.task.service: error
    me.chanjar.weixin.mp.bean.message.WxMpXmlMessage: ${LOG_WECHAT_MESSAGE:debug}
    org.hibernate:
      SQL: ${LOG_LEVEL_SQL:info}


befun.server.user-event.enable: true

