package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.AuthWhiteListDto;
import org.befun.auth.dto.auth.CasAuthDto;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.ThirdPartyAuthWhiteListService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Tag(name = "第三方登录-白名单")
@RestController
@RequestMapping("/third-party-auth-white-lists")
@PreAuthorize("isAuthenticated()")
@Validated
public class ThirdPartyAuthWhiteListController {

    @Autowired
    private ThirdPartyAuthWhiteListService thirdPartyAuthWhiteListService;

    @GetMapping("get/{type}")
    @Operation(summary = "获得CAS白名单")
    public ResourceResponseDto<AuthWhiteListDto> getSSOWhiteList(
            @PathVariable("type") String type,
            @Parameter(name = "app", description = "默认cem") String app
    ) {
        return new ResourceResponseDto<>(thirdPartyAuthWhiteListService.getWhiteList(type,app));
    }

    @PostMapping("sync/{type}")
    @Operation(summary = "同步CAS白名单")
    public ResourceResponseDto<Boolean> syncCasWhiteList(
            @PathVariable("type") String type,
            @NotNull @RequestBody AuthWhiteListDto dto) {
        return new ResourceResponseDto<>(thirdPartyAuthWhiteListService.syncWhiteList(type, dto.getApp(), dto.getWhiteCode()));
    }

}
