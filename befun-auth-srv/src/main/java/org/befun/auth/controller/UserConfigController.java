package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.userconfig.UserConfigBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigDeleteDto;
import org.befun.auth.dto.userconfig.UserConfigDto;
import org.befun.auth.dto.userconfig.UserConfigSaveDto;
import org.befun.auth.service.UserConfigService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "用户配置")
@Validated
@RestController
@RequestMapping("/users")
@PreAuthorize("isAuthenticated()")
public class UserConfigController {

    @Autowired
    private UserConfigService userConfigService;

    @GetMapping("/config/get")
    @Operation(summary = "获取配置")
    public ResourceResponseDto<UserConfigDto> getConfig(
            @Parameter(required = true, description = "用户配置类型,可用值:customerQuery") @RequestParam UserConfigType type) {
        return new ResourceResponseDto<>(userConfigService.getConfig(type));
    }

    @GetMapping("/config/getBuilder")
    @Operation(summary = "获取配置模板")
    public ResourceResponseDto<UserConfigBuilderDto> getConfigBuilder(
            @Parameter(required = true, description = "用户配置类型,可用值:customerQuery") @RequestParam UserConfigType type) {
        return new ResourceResponseDto<>(userConfigService.getConfigBuilder(type));
    }

    @PostMapping("/config/save")
    @Operation(summary = "保存配置")
    public ResourceResponseDto<UserConfigDto> saveConfig(
            @Parameter(required = true, description = "用户配置类型") @Valid @RequestBody UserConfigSaveDto dto) {
        return new ResourceResponseDto<>(userConfigService.saveConfig(dto.getType(), dto.getConfig()));
    }

    @PostMapping("/config/delete")
    @Operation(summary = "删除配置")
    public ResourceResponseDto<Boolean> deleteConfig(
            @Parameter(required = true, description = "用户配置类型") @Valid @RequestBody UserConfigDeleteDto dto) {
        return new ResourceResponseDto<>(userConfigService.deleteConfig(dto.getType(), dto.getConfigId()));
    }
}
