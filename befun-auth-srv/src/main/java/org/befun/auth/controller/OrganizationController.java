package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.dto.orgconfig.OrgConfigSaveDto;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "企业")
@Validated
@RestController
@RequestMapping("/organizations")
@PreAuthorize("isAuthenticated()")
public class OrganizationController {

    @Autowired
    private OrganizationConfigService organizationConfigService;

    @GetMapping("/config/get")
    @Operation(summary = "企业配置-获取(如果不存在则为null), 多个类型(,)分隔")
    public ResourceResponseDto<OrgConfigDto> getConfig(
            @Schema(required = true, description = "企业配置类型:customerVisible,surveyVerify,extendCustomerField,mfa,baseInfo,eventShare") @RequestParam String type) {
        return new ResourceResponseDto<>(organizationConfigService.getConfig(type));
    }

    @GetMapping("/config/getOrDefault")
    @Operation(summary = "企业配置-获取(如果不存在则返回默认的), 多个类型(,)分隔")
    public ResourceResponseDto<OrgConfigDto> getOrDefaultConfig(
            @Schema(required = true, description = "企业配置类型:customerVisible,surveyVerify,extendCustomerField,mfa,baseInfo,eventShare") @RequestParam String type) {
        return new ResourceResponseDto<>(organizationConfigService.getOrDefaultConfig(type));
    }

    @GetMapping("/config/getBuilder")
    @Operation(summary = "企业配置-获取完整的配置信息")
    public ResourceResponseDto<OrgConfigBuilderDto> getConfigBuilder(
            @Parameter(required = true, description = "企业配置类型") @RequestParam OrganizationConfigType type,
            @Parameter(description = "是否使用默认的配置：true 使用默认配置 false 使用已保存的配置") @RequestParam(required = false, defaultValue = "false") Boolean defaultConfig) {
        return new ResourceResponseDto<>(organizationConfigService.getConfigBuilder(type, defaultConfig));
    }

    @PostMapping("/config/save")
    @Operation(summary = "企业配置-保存")
    public ResourceResponseDto<OrgConfigDto> saveConfig(
            @Parameter(required = true, description = "企业配置类型") @Valid @RequestBody OrgConfigSaveDto dto) {
        return new ResourceResponseDto<>(organizationConfigService.saveConfig(dto.allTypes(), dto.getConfig()));
    }

    @PostMapping("/config/reset")
    @Operation(summary = "企业配置-重置为默认配置")
    public ResourceResponseDto<OrgConfigBuilderDto> resetConfig(
            @Parameter(required = true, description = "企业配置类型") @RequestParam OrganizationConfigType type) {
        return new ResourceResponseDto<>(organizationConfigService.resetConfig(type));
    }
}
