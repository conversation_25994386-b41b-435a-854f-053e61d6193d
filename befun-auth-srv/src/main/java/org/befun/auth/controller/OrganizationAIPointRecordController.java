package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.auth.repository.OrganizationAiPointRecordRepository;
import org.befun.auth.service.OrganizationAiPointRecordService;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "企业AI账单")
@Validated
@RestController
@ResourceController(
        entityClass = OrganizationAiPointRecord.class,
        repositoryClass = OrganizationAiPointRecordRepository.class,
        serviceClass = OrganizationAiPointRecordService.class,
        excludeActions = {COUNT, CREATE, UPDATE_ONE, DELETE_ONE, BATCH_UPDATE, FIND_ONE},
        docTag = "企业AI",
        docCrud = "企业AI账单",
        permission = "isAuthenticated()"
)
@RequestMapping("/organization/aiPointRecords")
@PreAuthorize("isAuthenticated()")
public class OrganizationAIPointRecordController {

}
