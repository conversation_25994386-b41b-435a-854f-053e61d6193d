package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.dto.usertask.UserTaskDto;
import org.befun.auth.dto.usertask.UserTaskQueryDto;
import org.befun.auth.service.UserTaskService;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.task.dto.TaskProgressDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户任务")
@Validated
@RestController
@RequestMapping("/user-tasks")
@PreAuthorize("isAuthenticated()")
public class UserTaskController {

    @Autowired
    private UserTaskService userTaskService;

    @GetMapping
    @Operation(summary = "全部用户任务")
    public ResourcePageResponseDto<UserTaskDto> findAll(@ResourceQueryCustom UserTaskQueryDto dto) {
        return new ResourcePageResponseDto<>(userTaskService.findAll(dto));
    }

    @GetMapping("/{id}/progress")
    @Operation(summary = "用户任务进度")
    public ResourceResponseDto<TaskProgressDto> taskProgress(@Parameter(required = true, description = "任务id") @PathVariable Long id) {
        return new ResourceResponseDto<>(userTaskService.progress(id));
    }

    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消用户任务")
    public ResourceResponseDto<Boolean> cancelTask(@Parameter(required = true, description = "任务id") @PathVariable Long id) {
        return new ResourceResponseDto<>(userTaskService.cancel(id));
    }

}
