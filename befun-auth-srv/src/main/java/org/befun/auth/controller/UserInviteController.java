package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.dto.*;
import org.befun.auth.entity.UserInvitationDto;
import org.befun.auth.service.UserInviteService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

@Tag(name = "用户-邀请")
@Validated
@RestController
@RequestMapping("/users/invite")
public class UserInviteController {

    @Autowired
    private UserInviteService userInviteService;
    @Autowired
    private PasswordHelper passwordHelper;

    @PostMapping("user-info")
    @Operation(summary = "使用账号信息邀请用户")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<UserInviteResponseDto> inviteMemberByUserInfo(@Valid @RequestBody UserInviteUserInfoRequestDto dto) {
        return new ResourceResponseDto<>(userInviteService.inviteMemberByUserInfo(dto));
    }

    @PostMapping("email")
    @Operation(summary = "使用邮箱邀请用户")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<UserInviteResponseDto> inviteMembers(@Valid @RequestBody UserInviteRequestDto dto) {
        return new ResourceResponseDto<>(userInviteService.inviteMembers(dto));
    }

    @PostMapping("email/notify")
    @Operation(summary = "重新发送邀请邮件-code会刷新")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<UserInvitationDto> inviteMemberNotify(@Valid @RequestBody UserInviteNotifyRequestDto dto) {
        return new ResourceResponseDto<>(userInviteService.inviteNotify(dto.getInviteId(), dto.getApp()));
    }

    @GetMapping("invite-url")
    @Operation(summary = "获得邀请链接和复制文案")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<UserInviteUrlResponseDto> inviteUrl(@RequestParam @Min(1) long inviteId, @RequestParam(required = false, defaultValue = "cem") String app) {
        return new ResourceResponseDto<>(userInviteService.inviteUrl(inviteId, app));
    }

    @GetMapping("active/status")
    @Operation(summary = "检查邀请链接的状态")
    public ResourceResponseDto<UserInviteStatusResponseDto> inviteActiveStatus(@NotEmpty @Parameter(name = "code", description = "邀请码") @RequestParam("code") String code) {
        TenantContext.clear();
        return new ResourceResponseDto<>(userInviteService.inviteStatus(code));
    }

    @PostMapping("active")
    @Operation(summary = "激活邀请")
    public ResourceResponseDto<Boolean> inviteActive(@Valid @RequestBody UserInviteActiveRequestDto dto) {
        TenantContext.clear();
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>(userInviteService.inviteActive(dto.getCode(), dto.getTruename(), dto.getPassword()));
    }

}
