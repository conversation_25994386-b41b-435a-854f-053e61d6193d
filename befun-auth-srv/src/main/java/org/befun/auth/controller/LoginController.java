package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.*;
import org.befun.auth.provider.saml.SamlProvider;
import org.befun.auth.service.AuthService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

import static org.befun.auth.constant.ThirdPartyAuthType.*;

@Slf4j
@Tag(name = "登录")
@RestController
@Validated
public class LoginController {

    @Autowired
    private AuthService authService;
    @Autowired
    private PasswordHelper passwordHelper;
    @Autowired(required = false)
    private SamlProvider samlProvider;

    @Value("${befun.auth.logout.path:/cem}")
    private String logoutPath;

    @Deprecated(since = "1.6.9")
    @PostMapping("/login/username")
    @Operation(summary = "登录-密码登录(默认app=cem)")
    public ResourceResponseDto<LoginResponseDto> loginUsername(
            @Valid @NotNull @RequestBody LoginPasswordRequestDto dto) {
        TenantContext.clear();
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("password", "cem", dto));
    }

    @Deprecated
    @GetMapping("/login/refresh-token")
    @Operation(summary = "登录-刷新token(已过期，使用路径上有app的地址)")
    public ResourceResponseDto<LoginResponseDto> refreshToken(@NotEmpty @RequestParam String refreshToken) {
        TenantContext.clear();
        LoginRefreshTokenRequestDto dto = new LoginRefreshTokenRequestDto();
        dto.setRefreshToken(refreshToken);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("refresh_token", "cem", dto));
    }

    @GetMapping("/login/refresh-token/{app}")
    @Operation(summary = "登录-刷新token")
    public ResourceResponseDto<LoginResponseDto> refreshToken(@NotEmpty @RequestParam String refreshToken, @PathVariable("app") String app) {
        TenantContext.clear();
        LoginRefreshTokenRequestDto dto = new LoginRefreshTokenRequestDto();
        dto.setRefreshToken(refreshToken);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("refresh_token", app, dto));
    }


    @PostMapping("/login/refresh-token/{app}")
    @Operation(summary = "登录-刷新token（mfa,authCode）")
    public ResourceResponseDto<LoginResponseDto> refreshToken(
            @Parameter(name = "app", description = "cem|surveyplus") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginRefreshTokenRequestDto dto) {
        TenantContext.clear();
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("refresh_token", app, dto));
    }

    @PostMapping("/login/password/{app}/status")
    @Operation(summary = "登录-密码登录(检测用户登录状态)")
    public ResourceResponseDto<LoginStatusDto> loginPasswordStatus(
            @Parameter(name = "app", description = "cem|surveyplus") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginPasswordRequestDto dto) {
        TenantContext.clear();
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>(authService.loginStatus("password", app, dto));
    }

    @PostMapping("/login/{source}/{app}/status")
    @Operation(summary = "登录-验证码登录(检测用户登录状态)")
    public ResourceResponseDto<LoginStatusDto> loginVerifyCodeStatus(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "cem|surveyplus") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginVerifyCodeRequestDto dto) {
        TenantContext.clear();
        return new ResourceResponseDto<>(authService.loginStatus(source, app, dto));
    }

    @GetMapping("/login/password/{app}/rsaPubKey")
    @Operation(summary = "登录-密码登录(获取加密公钥)")
    public ResourceResponseDto<LoginPasswordRsaPubKeyDto> loginPasswordRsaPubKey(@Parameter(name = "app", description = "默认cem") @PathVariable("app") String app) {
        return new ResourceResponseDto<>(passwordHelper.pubKey());
    }

    @PostMapping("/login/password/{app}")
    @Operation(summary = "登录-密码登录")
    public ResourceResponseDto<LoginResponseDto> loginPassword(
            @Parameter(name = "app", description = "cem|surveyplus") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginPasswordRequestDto dto) {
        TenantContext.clear();
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("password", app, dto));
    }

    @PostMapping("/login/{source}/{app}")
    @Operation(summary = "登录-验证码登录")
    public ResourceResponseDto<LoginResponseDto> loginVerifyCode(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "cem|surveyplus") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginVerifyCodeRequestDto dto) {
        TenantContext.clear();
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, dto));
    }

    @GetMapping("/login/{source}/{app}")
    @Operation(summary = "登录-获得第三方授权地址")
    public void loginBySource(
            @Parameter(name = "source", description = "wechat_work") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletResponse response) throws IOException {
        String redirectUrl = authService.authorize(source, app);
        log.info("oauth login: source={}, app={}, authUrl={}", source, app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/login/callback/{source}/{app}")
    @Operation(summary = "登录-第三方授权回调地址")
    public ResourceResponseDto<LoginResponseDto> loginCallback(
            @Parameter(name = "source", description = "wechat_work") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            AuthCallback callback) {
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, callback));
    }

    @Deprecated(since = "1.10.3")
    @GetMapping("/login/verify/cas-{orgCode}/{app}")
    @Operation(summary = "登录-校验企业编号是否有效，cas是否配置, 使用/login/verify/sso-{orgCode}/{app}")
    public ResourceResponseDto<LoginCasVerifyResponseDto> checkCasLogin(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletRequest request,
            HttpServletResponse response) {
        TenantContext.clear();
        String source = CAS.getSourcePrefix() + orgCode;
        return new ResourceResponseDto<>((LoginCasVerifyResponseDto) authService.verify(app, source, request, response));
    }

    @GetMapping("/login/verify/sso-{orgCode}/{app}")
    @Operation(summary = "登录-校验企业编号是否有效，sso是否配置")
    public ResourceResponseDto<LoginSSOVerifyResponseDto> checkSSOLogin(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletRequest request,
            HttpServletResponse response) {
        TenantContext.clear();

        //  由于用户可能分不清sso 默认使用cas登录 然后再使用oauth登录，所以这里不再区分oauth和cas
        LoginSSOVerifyResponseDto verifyResponseDto = null;
        for (ThirdPartyAuthType type : List.of(CAS, OAUTH, SAML)) {
            String source = type.getSourcePrefix() + orgCode;
            verifyResponseDto = (LoginSSOVerifyResponseDto) authService.verify(app, source, request, response);
            if (LoginSSOVerifyResponseDto.success().getSuccess() == (verifyResponseDto.getSuccess())) {
                break;
            }
        }

        return new ResourceResponseDto<>(verifyResponseDto);

    }

    @Deprecated(since = "1.10.3")
    @GetMapping("/login/cas-{orgCode}/{app}")
    @Operation(summary = "登录-获得cas授权地址 使用/login/sso-{orgCode}/{app}")
    public void loginByCas(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletResponse response) throws IOException {
        TenantContext.clear();
        String source = CAS.getSourcePrefix() + orgCode;
        String redirectUrl = authService.authorize(source, app);
        log.info("cas login: source={}, app={}, authUrl={}", source, app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/login/sso-{orgCode}/{app}")
    @Operation(summary = "登录-获得sso授权地址")
    public void loginBySSO(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletRequest request, HttpServletResponse response) throws IOException {
        TenantContext.clear();

        for (ThirdPartyAuthType type : List.of(CAS, OAUTH, SAML)) {
            String source = type.getSourcePrefix() + orgCode;
            if (LoginSSOVerifyResponseDto.success().getSuccess() == (((LoginSSOVerifyResponseDto) authService.verify(app, source, request, response)).getSuccess())) {
                String redirectUrl = authService.authorize(source, app);
                log.info("{} login: source={}, app={}, authUrl={}", type.getCamelName(), source, app, redirectUrl);
                response.sendRedirect(redirectUrl);
                break;
            }
        }
    }

    @GetMapping("/login/callback/cas-{orgCode}/{app}")
    @Operation(summary = "登录-cas授权回调地址")
    public ResourceResponseDto<LoginResponseDto> casLoginCallback(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "ticket") @RequestParam("ticket") String ticket) {
        TenantContext.clear();
        String source = CAS.getSourcePrefix() + orgCode;
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, ticket));
    }

    @GetMapping("/login/callback/oauth-{orgCode}/{app}")
    @Operation(summary = "登录-oauth授权回调地址")
    public ResourceResponseDto<LoginResponseDto> oauthLoginCallback(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletRequest request
    ) {
        TenantContext.clear();
        String source = OAUTH.getSourcePrefix() + orgCode;
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, request.getParameterMap()));
    }

    @GetMapping("/login/saml-{orgCode}/{app}")
    @Operation(summary = "登录-获得saml授权地址 使用/login/saml-{orgCode}/{app}")
    public void loginBySaml(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletResponse response) throws IOException {
        TenantContext.clear();
        String source = SAML.getSourcePrefix() + orgCode;
        String redirectUrl = authService.authorize(source, app);
        log.info("saml login: source={}, app={}, authUrl={}", source, app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @PostMapping("/login/callback/saml-{orgCode}/{app}")
    @Operation(summary = "登录-saml授权回调地址")
    public void samlLoginCallback(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        TenantContext.clear();
        String source = SAML.getSourcePrefix() + orgCode;
        String redirectUrl = (String) authService.login(source, app, request.getParameter("SAMLResponse"));
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/login/saml-{orgCode}/{app}/metadata")
    @Operation(summary = "登录-saml元数据")
    public String samlLoginMetadata(
            @Parameter(name = "orgCode", description = "企业编号") @PathVariable("orgCode") String orgCode,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletRequest request
    ) {
        TenantContext.clear();
        String source = SAML.getSourcePrefix() + orgCode;
        return samlProvider.metadata(source, app);
    }

    @PostMapping("/logout")
    @Operation(summary = "退出登录")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<LogoutResponseDto> logout(HttpServletRequest request, HttpServletResponse response) {
        return new ResourceResponseDto<>(authService.logout(request, response));
    }

    @GetMapping("/login/token/{app}")
    @Operation(summary = "登录-通过临时token获得登录信息（第一次请求有效）")
    public ResourceResponseDto<LoginResponseDto> loginResponse(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "token", description = "密钥") String token
    ) {
        TenantContext.clear();
        return new ResourceResponseDto<>(authService.loginResponse(token));
    }
}
