package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.recharge.RechargeAmountDto;
import org.befun.auth.pay.dto.recharge.RechargeRequestDto;
import org.befun.auth.pay.dto.recharge.RechargeResponseDto;
import org.befun.auth.pay.dto.recharge.RechargeStatusDto;
import org.befun.auth.pay.dto.wallet.WalletBalanceDto;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.auth.service.OrganizationWalletService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Tag(name = "企业钱包")
@Validated
@RestController
@RequestMapping("/organization/wallet")
@PreAuthorize("isAuthenticated()")
public class OrganizationWalletController {

    @Autowired
    private OrganizationWalletService organizationWalletService;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;

    @GetMapping("balance")
    @Operation(summary = "账户余额")
    public ResourceResponseDto<WalletBalanceDto> balance() {
        return new ResourceResponseDto<>(new WalletBalanceDto(organizationWalletService.balance(TenantContext.requireCurrentTenant())));
    }

    @GetMapping("recharge/amount")
    @Operation(summary = "账户充值-计算手续费和最终金额")
    public ResourceResponseDto<RechargeAmountDto> rechargeAmount(
            @NotNull @Parameter(description = "充值方式", required = true) @RequestParam RechargeType rechargeType,
            @Min(1) @NotNull @Parameter(description = "充值金额", required = true) @RequestParam Integer rechargeAmount) {
        return new ResourceResponseDto<>(organizationRechargeService.rechargeAmount(rechargeAmount, rechargeType, PayServiceRate.wallet));
    }

    @PostMapping("recharge")
    @Operation(summary = "账户充值")
    public ResourceResponseDto<RechargeResponseDto> recharge(@Valid @RequestBody RechargeRequestDto dto) {
        if (dto.getRechargeType() == null || !dto.getRechargeType().isEnableExternalRecharge()) {
            throw new BadRequestException("不支持的账户充值方式");
        }
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationRechargeService.recharge(orgId, userId, null, dto, PayServiceRate.wallet));
    }

    @GetMapping("recharge/status")
    @Operation(summary = "账户充值-查询充值状态")
    public ResourceResponseDto<RechargeStatusDto> rechargeStatus(@RequestParam Long rechargeId) {
        return new ResourceResponseDto<>(organizationRechargeService.rechargeStatus(rechargeId));
    }

    @GetMapping("recharge/payInfo")
    @Operation(summary = "账户充值-支付信息")
    public ResourceResponseDto<RechargeResponseDto> rechargePayInfo(@RequestParam Long rechargeId) {
        return new ResourceResponseDto<>(organizationRechargeService.rechargePayInfo(rechargeId));
    }

}
