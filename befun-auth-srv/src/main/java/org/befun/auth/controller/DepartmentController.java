package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.dto.DepartmentImportDto;
import org.befun.auth.dto.DepartmentRequestDto;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.dto.UserDepartmentMoveRequestDto;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.DepartmentDto;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.auth.service.DepartmentImportService;
import org.befun.auth.service.DepartmentService;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "部门")
@Validated
@RestController
@ResourceController(
        entityClass = Department.class,
        repositoryClass = DepartmentRepository.class,
        serviceClass = DepartmentService.class,
        excludeActions = {FIND_ALL, CREATE, COUNT, UPDATE_ONE, BATCH_UPDATE, FIND_ONE},
        docTag = "部门",
        docCrud = "部门",
        permission = "isAuthenticated()"
)
@PreAuthorize("isAuthenticated()")
@RequestMapping("/departments")
public class DepartmentController {

    @Autowired
    private DepartmentService service;
    @Autowired
    private DepartmentImportService departmentImportService;

    @GetMapping("tree")
    @Operation(summary = "部门树结构")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<List<DepartmentTreeDto>> tree() {
        DepartmentTreeDto root = service.treeByOrg(TenantContext.getCurrentTenant());
        return new ResourceResponseDto<>(root == null ? List.of() : List.of(root));
    }

    @PostMapping("save")
    @Operation(summary = "保存部门")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.SYS_MANAGE_LEVEL_MANAGE_EDIT)
    public ResourceResponseDto<DepartmentDto> save(@RequestBody DepartmentRequestDto dto) {
        return new ResourceResponseDto<>(service.saveDepartment(dto));
    }

    @PostMapping("{id}/move")
    @Operation(summary = "添加用户")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> move(@PathVariable long id, @RequestBody UserDepartmentMoveRequestDto dto) {
        return new ResourceResponseDto<>(service.addUsers(id, dto.getUserIds()));
    }

    @GetMapping("download")
    @Operation(summary = "导入部门-下载部门数据")
    @PreAuthorize("isAuthenticated()")
    public void download(HttpServletResponse response) {
        departmentImportService.download(response);
    }

    @PostMapping("import/check")
    @Operation(summary = "导入部门-校验")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.SYS_MANAGE_LEVEL_MANAGE_EDIT)
    public ResourceListResponseDto<DepartmentImportDto> checkImport(@RequestParam("file") MultipartFile file) {
        return new ResourceListResponseDto<>(departmentImportService.check(file));
    }

    @PostMapping("import")
    @Operation(summary = "导入部门-保存")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.SYS_MANAGE_LEVEL_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> imports(@RequestParam("file") MultipartFile file) {
        return new ResourceResponseDto<>(departmentImportService.sync(file));
    }

    @PostMapping("related")
    @Operation(summary = "关联答卷")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> related() {
        return new ResourceResponseDto<>(service.relatedResponse());
    }

    @GetMapping("clean-cache")
    @Operation(summary = "删除缓存")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> cleanCache() {
        service.clearCache(TenantContext.getCurrentTenant());
        return new ResourceResponseDto<>(true);
    }

}
