package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.entity.ApiKey;
import org.befun.auth.entity.ApiKeyDto;
import org.befun.auth.repository.ApiKeyRepository;
import org.befun.auth.service.ApiKeyService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "开放ApiKey")
@RestController
@ResourceController(
        entityClass = ApiKey.class,
        repositoryClass = ApiKeyRepository.class,
        serviceClass = ApiKeyService.class,
        permission = "isAuthenticated()",
        collectionType = ResourceCollectionType.SINGLE_TYPE,
        excludeActions = {CREATE, COUNT, UPDATE_ONE, DELETE_ONE, BATCH_UPDATE, FIND_ONE},
        docTag = "开放ApiKey",
        docCrud = "开放ApiKey"
)
@RequestMapping("/api-keys")
@PreAuthorize("isAuthenticated()")
public class ApiKeyController extends BaseController<ApiKeyService> {

    @PostMapping("refresh")
    @Operation(
            summary = "刷新",
            description = "刷新（如果不存在则创建）"
    )
    public ResourceResponseDto<ApiKeyDto> refresh() {
        return new ResourceResponseDto<>(service.refresh());
    }

}
