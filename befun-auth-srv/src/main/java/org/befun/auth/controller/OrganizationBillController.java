package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.entity.Department;
import org.befun.auth.pay.entity.OrganizationBill;
import org.befun.auth.pay.repository.OrganizationBillRepository;
import org.befun.auth.pay.service.OrganizationBillService;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;
import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ONE;

@Tag(name = "企业钱包")
@Validated
@RestController
@ResourceController(
        entityClass = OrganizationBill.class,
        repositoryClass = OrganizationBillRepository.class,
        serviceClass = OrganizationBillService.class,
        excludeActions = {COUNT, CREATE, UPDATE_ONE, DELETE_ONE, BATCH_UPDATE, FIND_ONE},
        docTag = "企业钱包",
        docCrud = "企业钱包账单",
        permission = "isAuthenticated()"
)
@RequestMapping("/organization/bills")
@PreAuthorize("isAuthenticated()")
public class OrganizationBillController {
}
