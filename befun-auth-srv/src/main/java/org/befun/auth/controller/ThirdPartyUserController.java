package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.repository.ThirdPartyUserRepository;
import org.befun.auth.service.ThirdPartyUserService;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "第三方登录-已绑定账号")
@RestController
@ResourceController(
        entityClass = ThirdPartyUser.class,
        repositoryClass = ThirdPartyUserRepository.class,
        serviceClass = ThirdPartyUserService.class,
        excludeActions = {UPDATE_ONE, CREATE, BATCH_UPDATE, FIND_ONE, COUNT},
        docTag = "第三方登录-已绑定账号",
        permission = "isAuthenticated()",
        docCrud = "已绑定账号"
)
@RequestMapping("/third-party-users")
@PreAuthorize("isAuthenticated()")
public class ThirdPartyUserController {
}
