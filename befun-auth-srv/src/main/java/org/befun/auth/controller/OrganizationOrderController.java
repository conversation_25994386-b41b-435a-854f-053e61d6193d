package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.dto.order.*;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "企业订单")
@Validated
@RestController
@RequestMapping("/organization/order")
@PreAuthorize("isAuthenticated()")
public class OrganizationOrderController {

    @Autowired
    private OrganizationOrderService organizationOrderService;

    @GetMapping("sms/amount")
    @Operation(summary = "短信订单-计算扣费金额")
    public ResourceResponseDto<SmsOrderAmountResponseDto> smsOrderAmount(
            @Parameter(description = "充值数量，默认为0") @RequestParam(required = false, defaultValue = "0") Integer sms,
            @Parameter(description = "充值方式(计算手续费)，默认微信") @RequestParam(required = false, defaultValue = "recharge_wechat") RechargeType rechargeType
    ) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.amountOrder(orgId, userId, OrderType.order_sms, new SmsOrderAmountRequestDto(sms, rechargeType)));
    }

    @PostMapping("sms")
    @Operation(summary = "短信订单")
    public ResourceResponseDto<PlaceOrderResponseDto> smsOrder(@Valid @RequestBody SmsOrderRequestDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.placeOrder(orgId, userId, OrderType.order_sms, dto));
    }

    @GetMapping("redPacket/amount")
    @Operation(summary = "红包订单-计算扣费金额")
    public ResourceResponseDto<RedPacketOrderAmountResponseDto> redPacketOrderAmount(
            @Parameter(description = "红包金额，默认为0") @RequestParam(required = false, defaultValue = "0") Integer redPacket,
            @Parameter(description = "充值方式(计算手续费)，默认微信") @RequestParam(required = false, defaultValue = "recharge_wechat") RechargeType rechargeType
    ) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.amountOrder(orgId, userId, OrderType.order_red_packet, new RedPacketOrderAmountRequestDto(rechargeType, redPacket)));
    }

    @PostMapping("redPacket")
    @Operation(summary = "红包订单")
    public ResourceResponseDto<PlaceOrderResponseDto> redPacketOrder(@Valid @RequestBody RedPacketOrderRequestDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.placeOrder(orgId, userId, OrderType.order_red_packet, dto));
    }

    @GetMapping("version/amount")
    @Operation(summary = "版本订单-计算扣费金额")
    public ResourceResponseDto<VersionOrderAmountResponseDto> versionOrderAmount() {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.amountOrder(orgId, userId, OrderType.order_org_version, true));
    }

    @PostMapping("version")
    @Operation(summary = "版本订单")
    public ResourceResponseDto<PlaceOrderResponseDto> versionOrder(@Valid @RequestBody VersionOrderRequestDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.placeOrder(orgId, userId, OrderType.order_org_version, dto));
    }

    @GetMapping("adminxChannel/amount")
    @Operation(summary = "社区样本订单-计算扣费金额")
    public ResourceResponseDto<AdminxChannelOrderAmountResponseDto> adminxChannelOrderAmount(
            @Parameter(description = "社区渠道id") @RequestParam Long channelId,
            @Parameter(description = "充值方式，默认微信") @RequestParam(required = false, defaultValue = "recharge_wechat") RechargeType rechargeType
    ) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        AdminxChannelOrderAmountRequestDto dto = new AdminxChannelOrderAmountRequestDto(channelId, rechargeType);
        return new ResourceResponseDto<>(organizationOrderService.amountOrder(orgId, userId, OrderType.order_adminx_channel, dto));
    }

    @PostMapping("adminxChannel")
    @Operation(summary = "社区样本订单")
    public ResourceResponseDto<PlaceOrderResponseDto> adminxChannelOrder(@Valid @RequestBody AdminxChannelOrderRequestDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.placeOrder(orgId, userId, OrderType.order_adminx_channel, dto));
    }

    @GetMapping("aiPoint/amount")
    @Operation(summary = "AI点数订单-计算扣费金额")
    public ResourceResponseDto<AiPointOrderAmountResponseDto> aiPointOrderAmount(
            @Parameter(description = "充值数量") @RequestParam Integer aiPoint,
            @Parameter(description = "充值方式(计算手续费)，默认微信") @RequestParam(required = false, defaultValue = "recharge_wechat") RechargeType rechargeType
    ) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.amountOrder(orgId, userId, OrderType.order_ai_point, new AiPointOrderAmountRequestDto(aiPoint, rechargeType)));
    }

    @PostMapping("aiPoint")
    @Operation(summary = "AI点数订单")
    public ResourceResponseDto<PlaceOrderResponseDto> aiPointOrder(@Valid @RequestBody AiPointOrderRequestDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        return new ResourceResponseDto<>(organizationOrderService.placeOrder(orgId, userId, OrderType.order_ai_point, dto));
    }

    @GetMapping("status")
    @Operation(summary = "查询订单状态")
    public ResourceResponseDto<OrderStatusDto> orderStatus(@RequestParam Long orderId) {
        return new ResourceResponseDto<>(organizationOrderService.orderStatus(orderId));
    }

    @GetMapping("info")
    @Operation(summary = "查询订单信息")
    public ResourceResponseDto<OrderInfoDto> orderInfo(@RequestParam Long orderId) {
        return new ResourceResponseDto<>(organizationOrderService.orderInfo(orderId));
    }
}
