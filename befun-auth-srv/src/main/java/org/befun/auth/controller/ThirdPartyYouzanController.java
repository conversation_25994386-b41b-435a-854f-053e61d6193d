package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.auth.AuthYouzanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "有赞")
@RestController
public class ThirdPartyYouzanController {

    @Autowired
    private AuthYouzanService authYouzanService;

    @Schema(description = "授权回调")
    @GetMapping("youzan/callback/auth/{app}")
    public String authCallback(
            @Parameter(description = "默认cem") @PathVariable("app") String app,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) String code) {
        if (StringUtils.isNotEmpty(message)) {
            // 授权 message 解析写入日志
            authYouzanService.receiveAuthMessage(message, app);
        } else if (StringUtils.isNotEmpty(code)) {
            // 授权 code 获取 access token
            authYouzanService.receiveAuthCode(code);
        }
        return "{\"code\":0,\"msg\":\"success\"}";
    }

    @Operation(summary = "消息回调")
    @PostMapping("youzan/callback/message/{app}")
    public String journeyTriggerByYouzan(
            @Parameter(description = "默认cem") @PathVariable("app") String app,
            @RequestBody String message,
            @RequestHeader("Event-Sign") String eventSign,
            @RequestHeader("Event-Type") String eventType,
            @RequestHeader("Client-Id") String clientId) {
        // 解析消息，如果有wxopenId 则增加旅程事件
        log.info("接收到youzan交易消息回调：Event-Sign={}, Event-Type={}, Client-Id={}", eventSign, eventType, clientId);
        authYouzanService.receiveTradeMessage(message, eventSign, eventType, clientId, app);
        return "{\"code\":0,\"msg\":\"success\"}";
    }


}
