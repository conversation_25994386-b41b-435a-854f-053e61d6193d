package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.pay.dto.wallet.SmsBalanceDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "企业短信")
@Validated
@RestController
@RequestMapping("/organization/sms")
@PreAuthorize("isAuthenticated()")
public class OrganizationSmsController {

    @Autowired
    private ISmsAccountService smsAccountService;

    @GetMapping("balance")
    @Operation(summary = "短信余额")
    public ResourceResponseDto<SmsBalanceDto> balance() {
        return new ResourceResponseDto<>(new SmsBalanceDto(smsAccountService.balance(TenantContext.requireCurrentTenant())));
    }

}
