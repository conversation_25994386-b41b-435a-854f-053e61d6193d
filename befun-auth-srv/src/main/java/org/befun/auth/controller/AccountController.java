package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.dto.BindVerifyCodeRequestDto;
import org.befun.auth.dto.ResetPasswordRequestDto;
import org.befun.auth.service.AuthService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Tag(name = "用户")
@RestController
@Validated
public class AccountController {

    @Autowired
    private AuthService authService;
    @Autowired
    private PasswordHelper passwordHelper;

    @PostMapping("/reset-password/{source}/{app}")
    @Operation(summary = "找回密码-重置")
    public ResourceResponseDto<Boolean> resetPassword(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody ResetPasswordRequestDto dto) {
        TenantContext.clear(); // 清空登录信息，之后的查询需要全局查询
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>(authService.resetPasswordByVerifyCode(source, app, dto));
    }

    @PostMapping("/bind/account/{source}/{app}")
    @Operation(summary = "绑定-手机|邮箱")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> bind(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody BindVerifyCodeRequestDto dto) {
        dto.setOrgId(TenantContext.getCurrentTenant());
        dto.setUserId(TenantContext.getCurrentUserId());
        TenantContext.clear(); // 清空登录信息，之后的查询需要全局查询
        return new ResourceResponseDto<>(authService.bind(source, app, dto));
    }


}
