package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.auth.pay.thirdpartypay.AuthWechatPay;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Tag(name = "支付宝支付")
@RestController
@Validated
public class AlipayPayController {

    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Autowired
    private AuthWechatPay authWechatPay;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private HttpServletResponse response;

    /**
     * 支付宝的回调通知包括
     * 支付成功
     * 部分退款
     * <a href="https://opendocs.alipay.com/support/01rawc?pathHash=4ad70fe3">交易退款接口是否会触发异步通知</a>
     */
    @PostMapping("/alipay/placeOrder/callback/{app}")
    @Operation(summary = "支付宝支付-下单支付后的回调地址")
    public void alipayCallback(
            @PathVariable String app,
            @RequestParam Map<String, String> body) {
        try {
            TenantContext.clear();
            Map<String, String> header = new HashMap<>();
            organizationRechargeService.rechargeCallback(RechargeType.recharge_alipay, header, body);
            response.setStatus(HttpStatus.SC_OK);
            response.getWriter().write("success");
            response.getWriter().flush();
        } catch (Throwable e) {
            log.error("支付宝支付回调失败", e);
            try {
                response.sendError(HttpStatus.SC_INTERNAL_SERVER_ERROR, "fail");
            } catch (IOException ex) {
                log.error("支付宝支付回调失败", e);
            }
        }
    }

    @PostMapping("/alipay/placeOrder/callback/{app}/sandbox")
    @Operation(summary = "支付宝支付-下单支付后的回调地址(沙箱)")
    public void alipayCallbackSandbox(
            @PathVariable String app,
            @RequestParam Map<String, Object> body) {
        try {
            TenantContext.clear();
            Map<String, String> header = new HashMap<>();
            header.put("sandbox", "true");
            organizationRechargeService.rechargeCallback(RechargeType.recharge_alipay, header, body);
            response.setStatus(HttpStatus.SC_OK);
            response.getWriter().write("success");
            response.getWriter().flush();
        } catch (Throwable e) {
            log.error("支付宝支付回调失败", e);
            try {
                response.sendError(HttpStatus.SC_INTERNAL_SERVER_ERROR, "fail");
            } catch (IOException ex) {
                log.error("支付宝支付回调失败", e);
            }
        }
    }

    @PostMapping("/alipay/message/callback/{app}")
    @Operation(summary = "支付宝支付-消息回调地址")
    public void alipayMessage(
            @PathVariable String app,
            @RequestParam Map<String, String> body) {
        try {
            TenantContext.clear();
            log.info("alipay message: {}", JsonHelper.toJson(body));
            response.setStatus(HttpStatus.SC_OK);
            response.getWriter().write("success");
            response.getWriter().flush();
        } catch (Throwable e) {
            log.error("支付宝支付消息回调失败", e);
            try {
                response.sendError(HttpStatus.SC_INTERNAL_SERVER_ERROR, "fail");
            } catch (IOException ex) {
                log.error("支付宝支付消息回调失败", e);
            }
        }
    }

    @PostMapping("/alipay/refund/callback/{app}")
    @Operation(summary = "支付宝支付-退款后的回调地址")
    public void wechatRefundCallback(
            @PathVariable String app,
            @RequestBody String body) {
        try {
            TenantContext.clear();
            Map<String, String> header = authWechatPay.parseRequestHeader(request);
            organizationRechargeService.refundCallback(RechargeType.recharge_alipay, header, body);
            response.setStatus(HttpStatus.SC_OK);
            response.getWriter().write("success");
            response.getWriter().flush();
        } catch (Throwable e) {
            log.error("支付宝支付消息回调失败", e);
            try {
                response.sendError(HttpStatus.SC_INTERNAL_SERVER_ERROR, "fail");
            } catch (IOException ex) {
                log.error("支付宝支付消息回调失败", e);
            }
        }
    }
}
