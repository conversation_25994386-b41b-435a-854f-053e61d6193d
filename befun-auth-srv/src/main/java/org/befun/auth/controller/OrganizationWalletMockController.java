package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Tag(name = "企业钱包")
@Validated
@RestController
@RequestMapping("/organization/wallet")
public class OrganizationWalletMockController {

    @Autowired
    private OrganizationRechargeService organizationRechargeService;

    @GetMapping("recharge/mockPay")
    @Operation(summary = "账户充值-模拟支付回调")
    public ResourceResponseDto<String> rechargeMockPay(@RequestParam Long rechargeId,
                                                       @RequestParam(required = false, defaultValue = "true") boolean success) {
        TenantContext.clear();
        OrganizationRecharge recharge = organizationRechargeService.get(rechargeId);
        if (recharge != null && recharge.getType() != null && recharge.getType().isEnableMock()) {
            organizationRechargeService.rechargeCallback(recharge.getType(), Map.of("mockPaySuccess", success + ""), rechargeId.toString());
            return new ResourceResponseDto<>("success");
        } else {
            return new ResourceResponseDto<>("failure");
        }
    }
}
