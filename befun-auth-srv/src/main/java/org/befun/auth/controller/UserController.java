package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.constant.UserGuideIndoType;
import org.befun.auth.dto.*;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.UserService;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.validation.ValidSearchText;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Set;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "用户")
@RestController
@ResourceController(
        entityClass = User.class,
        repositoryClass = UserRepository.class,
        serviceClass = UserService.class,
        excludeActions = {COUNT, UPDATE_ONE, BATCH_UPDATE, CREATE, FIND_ONE},
        methodDtoClass = {
                @ResourceMethodDto(method = FIND_ALL, dtoClass = UserQueryDto.class, valid = true)
        },
        permission = "isAuthenticated()",
        docTag = "用户",
        docCrud = "用户"
)
@Validated
@RequestMapping("/users")
@PreAuthorize("isAuthenticated()")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("list/cooperation")
    @Operation(summary = "事件协作用户列表")
    public ResourcePageResponseDto<SimpleUser> cooperationUsers(
            @RequestParam(value = "page", required = false, defaultValue = "1") int page,
            @RequestParam(value = "limit", required = false, defaultValue = "20") int limit,
            @ValidSearchText @RequestParam(value = "_q", required = false) String q) {
        return new ResourcePageResponseDto<>(userService.cooperationUsers(page, limit, q));
    }

    @GetMapping("list/surveyVerify")
    @Operation(summary = "问卷审核用户列表")
    public ResourcePageResponseDto<SimpleUser> surveyVerifyUsers(
            @RequestParam(value = "page", required = false, defaultValue = "1") int page,
            @RequestParam(value = "limit", required = false, defaultValue = "20") int limit,
            @ValidSearchText @RequestParam(value = "_q", required = false) String q) {
        return new ResourcePageResponseDto<>(userService.surveyVerifyUsers(page, limit, q));
    }

    @PostMapping("{id}/enable")
    @Operation(summary = "启用用户")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> enableUser(@PathVariable long id) {
        return new ResourceResponseDto<>(userService.enableUser(id));
    }

    @PostMapping("{id}/disable")
    @Operation(summary = "禁用用户")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> disableUser(@PathVariable long id) {
        return new ResourceResponseDto<>(userService.disableUser(id));
    }

    @PostMapping("{id}/update-role-permission")
    @Operation(summary = "修改用户层级和角色")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> updateRolePermission(@PathVariable long id, @Valid @RequestBody UserUpdateRequestDto dto) {
        return new ResourceResponseDto<>(userService.updateUserDepartmentAndRole(id, dto.getDepartmentIds(), dto.getRoleIds()));
    }

    @PostMapping("{id}/update-info")
    @Operation(summary = "修改用户信息")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> updateInfo(@PathVariable long id, @Valid @RequestBody UserUpdateInfoRequestDto dto) {
        return new ResourceResponseDto<>(userService.updateUserInfo(id, dto));
    }

    @PostMapping("{id}/update-role")
    @Operation(summary = "修改用户角色")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> updateRole(@PathVariable long id, @RequestBody UserRoleRequestDto dto) {
        return new ResourceResponseDto<>(userService.updateUserRole(id, dto));
    }

    @GetMapping("current/userinfo")
    @Operation(summary = "我的用户信息")
    public ResourceResponseDto<UserInfoResponseDto> currentInfo() {
        return new ResourceResponseDto<>(userService.currentUserInfo());
    }

    @PostMapping("current/update-info")
    @Operation(summary = "修改我的用户信息")
    public ResourceResponseDto<Boolean> updateTruename(@Valid @RequestBody UserChangeInfoRequestDto dto) {
        return new ResourceResponseDto<>(userService.updateInfo(dto));
    }

    @PostMapping("current/update-organization")
    @Operation(summary = "修改我的用户组织信息")
    public ResourceResponseDto<Boolean> updateOrganization(@Valid @RequestBody SimpleOrganization dto) {
        return new ResourceResponseDto<>(userService.updateOrganization(dto));
    }

    @PostMapping("current/update-password")
    @Operation(summary = "修改我的密码")
    public ResourceResponseDto<Boolean> updatePassword(@RequestBody UserChangePasswordRequestDto dto) {
        return new ResourceResponseDto<>(userService.updatePassword(dto));
    }

    @GetMapping("current/permissions")
    @Operation(summary = "我的权限列表")
    public ResourceResponseDto<PermissionResponseDto> getPermissions() {
        return new ResourceResponseDto<>(userService.getPermissions());
    }

    @PostMapping("current/update-guide")
    @Operation(summary = "修改我的操作指南")
    public ResourceResponseDto<Set<UserGuideIndoType>> updateGuideInfo(@RequestBody UserGuideInfoRequestDto type) {
        return new ResourceResponseDto<>(userService.updateUserGuideInfo(type));
    }

    @PostMapping("change-owner-resources")
    @Operation(summary = "移交资源")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_TRANSFER)
    public ResourceResponseDto<Boolean> changeResources(
            @RequestBody ChangeOwnerResourcesDto dto) {
        return new ResourceResponseDto<>(userService.changeOwnerResources(dto));
    }


}
