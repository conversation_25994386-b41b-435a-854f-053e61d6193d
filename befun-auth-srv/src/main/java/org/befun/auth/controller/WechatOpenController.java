package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.dto.auth.WechatOpenAuthDto;
import org.befun.auth.dto.auth.wechatopen.WechatOpenAuthCodeDto;
import org.befun.auth.dto.auth.wechatopen.WechatOpenPreAuthCodeDto;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;

@Slf4j
@Tag(name = "微信开放平台第三方平台")
@RestController
@Validated
public class WechatOpenController {

    @Autowired
    private AuthWechatOpenService authWechatOpenService;

    @GetMapping("/bind/wechat-open/authorize/{app}")
    @Operation(summary = "绑定微信公众号(1)-授权地址，浏览器重定向到此地址")
    public void authorize(@Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
                          HttpServletResponse response) throws IOException {
        String redirectUrl = authWechatOpenService.authorize(app);
        log.info("wechat open authorize url: app={}, authUrl={}", app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @PreAuthorize("isAuthenticated()")
    @PostMapping("/bind/wechat-open/authorize/callback/{app}")
    @Operation(summary = "绑定微信公众号(2)-绑定当前授权的公众号到当前企业")
    public ResourceResponseDto<WechatOpenAuthDto> authorizeCallback(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @NotNull @Valid @RequestBody WechatOpenAuthCodeDto authCode) {
        log.info("wechat open authorize callback: app={}, authCode={}", app, authCode.getAuthCode());
        return new ResourceResponseDto<>(authWechatOpenService.authorizeCallback(authCode.getAuthCode(), app));
    }

    @PostMapping("/wechat-open/event/{app}")
    @Operation(summary = "微信公众号-事件回调地址", description = "配置在微信开放第三方平台应用的后台")
    public String eventCallbackUrl(
            @PathVariable("app") String app,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam(name = "encrypt_type", required = false) String encryptType,
            @RequestParam(name = "msg_signature", required = false) String msgSignature,
            @RequestBody(required = false) String body) {
        TenantContext.clear();
        return authWechatOpenService.eventCallbackUrl(app, timestamp, nonce, encryptType, msgSignature, body);
    }

    @PostMapping("/wechat-open/message/{app}/{appId}")
    @Operation(summary = "微信公众号-消息回调地址", description = "配置在微信开放第三方平台应用的后台")
    public String messageCallbackUrl(
            @PathVariable("app") String app,
            @PathVariable("appId") String appId,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam(name = "encrypt_type", required = false) String encryptType,
            @RequestParam(name = "msg_signature", required = false) String msgSignature,
            @RequestBody(required = false) String body) {
        TenantContext.clear();
        return authWechatOpenService.messageCallbackUrl(app, appId, timestamp, nonce, encryptType, msgSignature, body);
    }

}
