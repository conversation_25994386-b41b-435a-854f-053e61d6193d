package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.entity.OrganizationSmsRecord;
import org.befun.auth.pay.entity.OrganizationBill;
import org.befun.auth.pay.repository.OrganizationBillRepository;
import org.befun.auth.pay.service.OrganizationBillService;
import org.befun.auth.repository.OrganizationSmsRecordRepository;
import org.befun.auth.service.OrganizationSmsRecordService;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "企业短信")
@Validated
@RestController
@ResourceController(
        entityClass = OrganizationSmsRecord.class,
        repositoryClass = OrganizationSmsRecordRepository.class,
        serviceClass = OrganizationSmsRecordService.class,
        excludeActions = {COUNT, CREATE, UPDATE_ONE, DELETE_ONE, BATCH_UPDATE, FIND_ONE},
        docTag = "企业短信",
        docCrud = "企业短信账单",
        permission = "isAuthenticated()"
)
@RequestMapping("/organization/smsRecords")
@PreAuthorize("isAuthenticated()")
public class OrganizationSmsRecordController {

}
