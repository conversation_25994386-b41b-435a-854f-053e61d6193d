package org.befun.auth.controller;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.auth.*;
import org.befun.auth.dto.auth.baidu.BaiduTongjiAuthSiteDto;
import org.befun.auth.dto.auth.baidu.BaiduTongjiAuthUrlDto;
import org.befun.auth.dto.auth.oauth.OauthAuthDto;
import org.befun.auth.dto.auth.youzan.YouzanBindDto;
import org.befun.auth.dto.auth.youzan.YouzanSupportEventDto;
import org.befun.auth.dto.linker.LinkerBaiduTongjiParamDto;
import org.befun.auth.dto.linker.LinkerResponseDataDto;
import org.befun.auth.dto.linker.LinkerShenCeParamDto;
import org.befun.auth.service.auth.*;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static org.befun.auth.constant.ThirdPartyAuthType.*;

@SuppressWarnings("unchecked")
@Tag(name = "第三方账号-配置")
@RestController
@RequestMapping("/third-party-auths")
@PreAuthorize("isAuthenticated()")
@Validated
public class ThirdPartyAuthController {

    @Autowired
    private ThirdPartyAuthHelper thirdpartyAuthHelper;
    @Autowired
    private AuthBaiduTongjiService authBaiduTongjiService;
    @Autowired
    private AuthEmailSenderService authEmailSenderService;
    @Autowired
    private AuthYouzanService authYouzanService;
    @Autowired
    private AuthShenCeService authShenCeService;

    @JsonView(ResourceViews.Basic.class)
    @GetMapping("get-all/{app}")
    @Operation(summary = "配置查询：指定查询的类型，会返回与参数类型相同的配置")
    public ResourceResponseDto<AllAuthDto> getAll(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "types", description = "授权类型(多个,分隔): " +
                    "cas | CAS, " +
                    "cas | OAUTH, " +
                    "saml | SAML, " +
                    "wechatWork | WECHAT_WORK, " +
                    "wechatOpen | WECHAT_OPEN, " +
                    "baiduTongji | BAIDU_TONGJI, " +
                    "emailSender | EMAIL_SENDER, " +
                    "youzan | YOUZAN") String types
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.getConfigByTypes(types, app));
    }

    @GetMapping("count/{app}")
    @Operation(summary = "配置查询-查询指定的类型已配置数量")
    public ResourceResponseDto<AuthCountDto> count(
            @Parameter(description = "默认cem") @PathVariable("app") String app,
            @Parameter(description = "配置类型", required = true) @RequestParam ThirdPartyAuthType type
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.countByType(type, app));
    }

    //****************************************************************************************************************************
    //****************************************************** cas *****************************************************************
    //****************************************************************************************************************************

    @Deprecated(since = "1.9.6")
    @JsonView(ResourceViews.Basic.class)
    @GetMapping("cas")
    @Operation(summary = "cas配置查询，使用 get-all/{app}")
    public ResourceResponseDto<CasAuthDto> getCas0(
            @Parameter(name = "app", description = "默认cem") String app) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.getSingleConfigByType(CAS, app));
    }

    @Deprecated(since = "1.9.6")
    @JsonView(ResourceViews.Basic.class)
    @PostMapping("cas/save")
    @Operation(summary = "cas配置保存，使用 cas/save/{app}")
    public ResourceResponseDto<CasAuthDto> saveCas0(@NotNull @RequestBody CasAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(CAS, dto, dto.getApp()));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("cas/save/{app}")
    @Operation(summary = "cas配置保存")
    public ResourceResponseDto<CasAuthDto> saveCas(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody CasAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(CAS, dto, app));
    }

    //****************************************************************************************************************************
    //****************************************************** oauth *****************************************************************
    //****************************************************************************************************************************

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("oauth/save/{app}")
    @Operation(summary = "oauth配置保存")
    public ResourceResponseDto<OauthAuthDto> saveOauth(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody OauthAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(OAUTH, dto, app));
    }

    //****************************************************************************************************************************
    //****************************************************** oauth *****************************************************************
    //****************************************************************************************************************************

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("saml/save/{app}")
    @Operation(summary = "saml配置保存")
    public ResourceResponseDto<SamlAuthDto> saveSaml(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody SamlAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(SAML, dto, app));
    }

    //****************************************************************************************************************************
    //************************************************** wechat_work *************************************************************
    //****************************************************************************************************************************

    @JsonView(ResourceViews.Basic.class)
    @GetMapping("wechat-work/{app}")
    @Operation(summary = "企业微信配置查询")
    public ResourceResponseDto<WechatWorkAuthDto> getWechatWork(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.getSingleConfigByType(WECHAT_WORK, app));
    }

    @DeleteMapping("wechat-work/delete/{app}")
    @Operation(summary = "企业微信配置删除")
    public ResourceResponseDto<Boolean> deleteWechatWork(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.deleteSingleByType(WECHAT_WORK, app));
    }

    //****************************************************************************************************************************
    //************************************************** wechat_open *************************************************************
    //****************************************************************************************************************************
    // 绑定：WechatOpenController#authorizeWechatOpen WechatOpenController#authorizeWechatOpenCallback
    // 查询：直接使用 getAll
    // 删除：需要通过微信公众号后台，解除授权，然后会推送解除授权事件给我们，再进行解除绑定
    @DeleteMapping("wechat-open/{configId}/delete/{app}")
    @Operation(summary = "微信公众号配置删除")
    public ResourceResponseDto<Boolean> deleteWechatOpen(
            @Parameter(description = "配置id") @PathVariable("configId") Long configId,
            @Parameter(description = "是否删除关联的微信用户") @RequestParam(required = false, defaultValue = "false") boolean deleteUser,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app
    ) {
        Map<String, Object> extParams = new HashMap<>();
        extParams.put("deleteUser", deleteUser);
        return new ResourceResponseDto<>(thirdpartyAuthHelper.deleteByConfigId(WECHAT_OPEN, configId, extParams));
    }

    //****************************************************************************************************************************
    //************************************************** baidu_tongji ************************************************************
    //****************************************************************************************************************************

    @GetMapping("baidu-tongji/auth-url/{app}")
    @Operation(summary = "百度统计授权地址查询")
    public ResourceResponseDto<BaiduTongjiAuthUrlDto> getBaiduTongjiAuthUrl(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @NotEmpty @RequestParam String apiKey
    ) {
        return new ResourceResponseDto<>(authBaiduTongjiService.getAuthUrl(apiKey, app));
    }

    @GetMapping("baidu-tongji/get-sites/{app}")
    @Operation(summary = "百度统计站点列表查询")
    public ResourceListResponseDto<BaiduTongjiAuthSiteDto> getBaiduTongjiSites(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "authAndGet", description = "1 授权并查询站点列表 0 直接用绑定的百度账号查询站点列表") @RequestParam Integer authAndGet,
            @Parameter(name = "apiKey", description = "authAndGet=1时，需要此参数") @RequestParam(required = false) String apiKey,
            @Parameter(name = "apiSecret", description = "authAndGet=1时，需要此参数") @RequestParam(required = false) String apiSecret,
            @Parameter(name = "code", description = "authAndGet=1时，需要此参数") @RequestParam(required = false) String code
    ) {
        return new ResourceListResponseDto<>(authBaiduTongjiService.getSites(authAndGet, apiKey, apiSecret, code, app));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("baidu-tongji/save/{app}")
    @Operation(summary = "百度统计配置保存")
    public ResourceResponseDto<BaiduTongjiAuthDto> saveBaiduTongji(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody BaiduTongjiAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(BAIDU_TONGJI, dto, app));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("baidu-tongji/update-site/{app}")
    @Operation(summary = "百度统计站点更新")
    public ResourceResponseDto<BaiduTongjiAuthDto> updateBaiduTongjiSite(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody BaiduTongjiAuthSiteDto dto) {
        return new ResourceResponseDto<>(authBaiduTongjiService.updateSite(app, dto));
    }

    @GetMapping("baidu-tongji/data/{app}")
    @Operation(summary = "百度统计站点数据查询")
    public ResourceResponseDto<LinkerResponseDataDto> getBaiduTongjiData(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            String start_date,
            String end_date,
            String method,
            String metrics,
            String gran,
            String visitor) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LinkerBaiduTongjiParamDto params = new LinkerBaiduTongjiParamDto(
                LocalDate.parse(start_date, formatter),
                LocalDate.parse(end_date, formatter),
                method, metrics, gran, visitor);
        return new ResourceResponseDto<>(authBaiduTongjiService.getData(app, params));
    }

    //****************************************************************************************************************************
    //***************************************************** email ****************************************************************
    //****************************************************************************************************************************

    @Deprecated(since = "1.9.6")
    @JsonView(ResourceViews.Basic.class)
    @GetMapping("email-sender")
    @Operation(summary = "邮件配置查询，使用 get-all/{app}")
    public ResourceResponseDto<EmailSenderAuthDto> getEmailSender0(@Parameter(name = "app", description = "默认cem") String app) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.getSingleConfigByType(EMAIL_SENDER, app));
    }

    @Deprecated(since = "1.9.6")
    @JsonView(ResourceViews.Basic.class)
    @PostMapping("email-sender/save")
    @Operation(summary = "邮件配置保存，使用 email-sender/save/{app}")
    public ResourceResponseDto<EmailSenderAuthDto> saveEmailSender0(@Valid @NotNull @RequestBody EmailSenderAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(EMAIL_SENDER, dto, dto.getApp()));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("email-sender/save/{app}")
    @Operation(summary = "邮件配置保存")
    public ResourceResponseDto<EmailSenderAuthDto> saveEmailSender(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody EmailSenderAuthDto dto
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(EMAIL_SENDER, dto, app));
    }

    @Deprecated(since = "1.9.6")
    @JsonView(ResourceViews.Basic.class)
    @PostMapping("email-sender/test")
    @Operation(summary = "邮件配置测试，使用 email-sender/test/{app}")
    public ResourceResponseDto<Boolean> testEmailSender0(@Valid @NotNull @RequestBody EmailSenderAuthDto dto) {
        return new ResourceResponseDto<>(authEmailSenderService.testConfig(dto.getConfig()));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("email-sender/test/{app}")
    @Operation(summary = "邮件配置测试")
    public ResourceResponseDto<Boolean> testEmailSender(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody EmailSenderAuthDto dto) {
        return new ResourceResponseDto<>(authEmailSenderService.testConfig(dto.getConfig()));
    }

    @DeleteMapping("email-sender/delete/{app}")
    @Operation(summary = "邮件配置删除")
    public ResourceResponseDto<Boolean> deleteEmailSender(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.deleteSingleByType(EMAIL_SENDER, app));
    }

    //****************************************************************************************************************************
    //***************************************************** youzan ****************************************************************
    //****************************************************************************************************************************

    @JsonView(ResourceViews.Basic.class)
    @GetMapping("youzan/support-events/{app}")
    @Operation(summary = "有赞商城支持的事件")
    public ResourceListResponseDto<YouzanSupportEventDto> supportEventsYouzan(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app) {
        return new ResourceListResponseDto<>(authYouzanService.supportEvents());
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("youzan/bind/{app}")
    @Operation(summary = "有赞商城绑定")
    public ResourceResponseDto<YouzanAuthDto> bindYouzan(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody YouzanBindDto dto) {
        return new ResourceResponseDto<>(authYouzanService.bind(app, dto));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("youzan/save/{app}")
    @Operation(summary = "有赞商城保存")
    public ResourceResponseDto<YouzanAuthDto> saveYouzan(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody YouzanAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(YOUZAN, dto, app));
    }

    @DeleteMapping("youzan/delete/{app}")
    @Operation(summary = "有赞商城删除")
    public ResourceResponseDto<Boolean> deleteYouzan(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app
    ) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.deleteSingleByType(YOUZAN, app));
    }

    //****************************************************************************************************************************
    //***************************************************** shenCe ****************************************************************
    //****************************************************************************************************************************

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("shen-ce/save/{app}")
    @Operation(summary = "神策保存")
    public ResourceResponseDto<ShenCeAuthDto> saveShenCe(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody ShenCeAuthDto dto) {
        return new ResourceResponseDto<>(thirdpartyAuthHelper.saveSingleConfigByType(SHEN_CE, dto, app));
    }

    @PostMapping("shen-ce/data/{app}")
    @Operation(summary = "神策数据查询", hidden = true)
    public ResourceResponseDto<LinkerResponseDataDto> getShenCeData(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @RequestBody LinkerShenCeParamDto params) {
        return new ResourceResponseDto<>(authShenCeService.getData(app, params));
    }
}
