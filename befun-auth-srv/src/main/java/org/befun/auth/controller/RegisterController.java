package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.auth.dto.RegisterInfoDto;
import org.befun.auth.dto.RegisterVerifyCodeDto;
import org.befun.auth.entity.User;
import org.befun.auth.service.RegisterService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * <AUTHOR>
 */
@Tag(name = "注册")
@RestController
@Validated
public class RegisterController {

    @Autowired
    private RegisterService registerService;
    @Autowired
    private PasswordHelper passwordHelper;

    /**
     * @see VerifyCodeController#verifyCodeStatus(String, String, String, String, String)
     */
    @Deprecated()
    @PostMapping("/register/{source}/verify-code")
    public ResourceResponseDto verifyCode(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Valid @RequestBody RegisterVerifyCodeDto dto) {
        registerService.checkVerifyCode(dto.getAccount(), dto.getVerifyCode());
        return new ResourceResponseDto<>();
    }

    @SneakyThrows
    @PostMapping("/register")
    @Operation(summary = "注册")
    public ResourceResponseDto register(@Valid @RequestBody RegisterInfoDto dto) {
        dto.checkParams();
        dto.clearAppVersion();
        dto.confirmPassword(passwordHelper::decryptRsa);
        User user = registerService.registerByMobile(dto);
        registerService.userRegisterEvent(user, dto);
        return new ResourceResponseDto<>(user);
    }

    @SneakyThrows
    @PostMapping("/register-by-api")
    @Operation(summary = "注册")
    public ResourceResponseDto registerByApi(@Valid @RequestBody RegisterInfoDto dto) {
        dto.confirmPassword(passwordHelper::decryptRsa);
        User user = registerService.registerByApi(dto);
        return new ResourceResponseDto<>(user);
    }

}
