package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.dto.RoleRequestDto;
import org.befun.auth.dto.RoleResponseDto;
import org.befun.auth.dto.RoleTemplateResponseDto;
import org.befun.auth.dto.RoleUserRequestDto;
import org.befun.auth.entity.Role;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.service.RoleService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourcePermission;
import org.befun.core.rest.annotation.processor.ResourceMethodName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "角色")
@RestController
@ResourceController(
        entityClass = Role.class,
        repositoryClass = RoleRepository.class,
        serviceClass = RoleService.class,
        excludeActions = {CREATE, UPDATE_ONE, BATCH_UPDATE, COUNT, FIND_ONE},
        permission = "isAuthenticated()",
        permissions = {
                @ResourcePermission(
                        action = ResourceMethodName.DELETE_ONE,
                        requirePermissions = PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT
                )
        },
        docTag = "角色",
        docCrud = "角色"
)
@Validated
@RequestMapping("/roles")
@PreAuthorize("isAuthenticated()")
public class RoleController extends BaseController<RoleService> {

    @Autowired
    private RoleService roleService;

    @GetMapping("template")
    @Operation(summary = "角色模板（新建角色时可用此模板创建角色）")
    public ResourceResponseDto<RoleTemplateResponseDto> template() {
        return new ResourceResponseDto<>(roleService.template());
    }

    @GetMapping("{id}/detail")
    @Operation(summary = "角色详情")
    public ResourceResponseDto<RoleResponseDto> detail(@PathVariable long id) {
        return new ResourceResponseDto<>(roleService.detail(id));
    }

    @PostMapping("{id}/add-user")
    @Operation(summary = "向角色中添加用户")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> addUser(@PathVariable long id, @Valid @RequestBody RoleUserRequestDto dto) {
        return new ResourceResponseDto<>(roleService.addUser(id, dto));
    }

    @PostMapping("{id}/remove-user")
    @Operation(summary = "从角色中移除用户")
    @RequirePermissions(PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> removeUser(@PathVariable long id, @Valid @RequestBody RoleUserRequestDto dto) {
        return new ResourceResponseDto<>(roleService.removeUser(id, dto));
    }

    @PostMapping("save")
    @Operation(summary = "保存角色")
    @RequirePermissions(PermissionPath.SYS_MANAGE_ROLE_MANAGE_EDIT)
    public ResourceResponseDto<Boolean> save(@Valid @RequestBody RoleRequestDto dto) {
        return new ResourceResponseDto<>(roleService.save(dto));
    }

    @PostMapping("{id}/enable")
    @Operation(summary = "启用角色")
    @RequirePermissions(PermissionPath.SYS_MANAGE_ROLE_MANAGE_ENABLE)
    public ResourceResponseDto<Boolean> enable(@PathVariable long id) {
        return new ResourceResponseDto<>(roleService.enable(id));
    }
}
