package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.befun.auth.pay.constant.RechargeType;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.auth.pay.thirdpartypay.AuthWechatPay;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Slf4j
@Tag(name = "微信支付")
@RestController
@Validated
public class WechatPayController {

    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Autowired
    private AuthWechatPay authWechatPay;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private HttpServletResponse response;

    @PostMapping("/wechatPay/placeOrder/callback/{app}")
    @Operation(summary = "微信支付-下单支付后的回调地址")
    public void wechatPayCallback(
            @PathVariable String app,
            @RequestBody String body) {
        try {
            TenantContext.clear();
            Map<String, String> header = authWechatPay.parseRequestHeader(request);
            organizationRechargeService.rechargeCallback(RechargeType.recharge_wechat, header, body);
            response.setStatus(HttpStatus.SC_OK);
        } catch (Throwable e) {
            log.error("微信支付回调失败", e);
            try {
                response.sendError(HttpStatus.SC_INTERNAL_SERVER_ERROR, JsonHelper.toJson(Map.of("code", "FAIL", "message", "失败")));
            } catch (IOException ex) {
                log.error("微信支付回调失败", e);
            }
        }
    }

    @PostMapping("/wechatPay/refund/callback/{app}")
    @Operation(summary = "微信支付-退款后的回调地址")
    public void wechatRefundCallback(
            @PathVariable String app,
            @RequestBody String body) {
        try {
            TenantContext.clear();
            Map<String, String> header = authWechatPay.parseRequestHeader(request);
            organizationRechargeService.refundCallback(RechargeType.recharge_wechat, header, body);
            response.setStatus(HttpStatus.SC_OK);
        } catch (Throwable e) {
            log.error("微信退款回调失败", e);
            try {
                response.sendError(HttpStatus.SC_INTERNAL_SERVER_ERROR, JsonHelper.toJson(Map.of("code", "FAIL", "message", "失败")));
            } catch (IOException ex) {
                log.error("微信退款回调失败", e);
            }
        }
    }

}
