package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.dto.SendVerifyCodeRequestDto;
import org.befun.auth.dto.VerifyCodeStatusRequestDto;
import org.befun.auth.service.AuthService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Tag(name = "验证码")
@RestController
@Validated
public class VerifyCodeController {

    @Autowired
    private AuthService authService;

    @PostMapping("/send/verify-code/{useFor}/{source}/{app}")
    @Operation(summary = "发送验证码-注册|登录|绑定|重置密码|mfa|问卷手机题")
    public ResourceResponseDto<Boolean> sendVerifyCode(
            @Parameter(name = "useFor", description = "register|login|bind|reset-password|mfa|survey-question-mobile") @PathVariable("useFor") String useFor,
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody SendVerifyCodeRequestDto dto) {
        TenantContext.clear();
        return new ResourceResponseDto<>(authService.sendVerifyCode(source, app, useFor, dto));
    }

    @GetMapping("/verify-code/status/{useFor}/{source}/{app}")
    @Operation(summary = "校验验证码-注册|登录|绑定|重置密码|mfa|问卷手机题")
    public ResourceResponseDto<Boolean> verifyCodeStatus(
            @Parameter(name = "useFor", description = "register|login|bind|reset-password|mfa|survey-question-mobile") @PathVariable("useFor") String useFor,
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @NotEmpty @Parameter(name = "account", description = "账号： 手机号或者邮箱或者mfaToken") @RequestParam("account") String account,
            @NotEmpty @Parameter(name = "code", description = "验证码") @RequestParam("code") String code) {
        TenantContext.clear();
        VerifyCodeStatusRequestDto dto = new VerifyCodeStatusRequestDto(account, code);
        return new ResourceResponseDto<>(authService.verifyCodeStatus(source, app, useFor, dto));
    }

}
