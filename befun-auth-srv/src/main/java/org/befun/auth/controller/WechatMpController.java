package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.dto.AuthPingResponseDto;
import org.befun.auth.dto.BindResponseDto;
import org.befun.auth.dto.BindResultResponseDto;
import org.befun.auth.dto.auth.authcode.AuthCodeDto;
import org.befun.auth.dto.auth.authcode.AuthCodeStatusDto;
import org.befun.auth.provider.wechat.mp.WechatMpAuthProvider;
import org.befun.auth.service.AuthService;
import org.befun.auth.service.SystemNotificationService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Tag(name = "微信公众号")
@RestController
@Validated
public class WechatMpController {

    @Autowired
    private AuthService authService;

    @Autowired
    private SystemNotificationService notificationService;

    @GetMapping("/authCode/wechat_mp/{app}")
    @Operation(summary = "授权码-获得公众号授权二维码")
    public ResourceResponseDto<AuthCodeDto> authCode(
            @Parameter(name = "app", description = "cem | surveyplus") @PathVariable("app") String app) {
        AuthCodeDto response = authService.buildAuthCode(app, WechatMpAuthProvider.SOURCE);
        return new ResourceResponseDto<>(response);
    }

    @GetMapping("/authCode/wechat_mp/{app}/status")
    @Operation(summary = "授权码-查询公众号授权码状态")
    public ResourceResponseDto<AuthCodeStatusDto> authCodeStatus(
            @Parameter(description = "cem | surveyplus") @PathVariable String app,
            @Parameter(description = "授权码") @RequestParam String code) {
        AuthCodeStatusDto response = authService.authCodeStatus(app, WechatMpAuthProvider.SOURCE, code);
        return new ResourceResponseDto<>(response);
    }

    @GetMapping("/bind/{source}/{app}")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "绑定-获得公众号绑定二维码")
    public ResourceResponseDto<BindResponseDto> bindBySource(
            @Parameter(name = "source", description = "公众号：wechat_mp") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app) {
        BindResponseDto response = authService.bindByQrCode(app, source);
        return new ResourceResponseDto<>(response);
    }

    @GetMapping("/bind/{id}")
    @Operation(summary = "绑定-查询绑定结果")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<BindResultResponseDto> queryBindingStatus(@PathVariable("id") String id) {
        BindResultResponseDto result = authService.checkBindingResult(id);
        return new ResourceResponseDto<>(result);
    }

    @GetMapping("/route/{source}/{app}")
    @Operation(summary = "回调地址-第三方校验")
    public void verifySource(
            @PathVariable("app") String app,
            @PathVariable("source") String source,
            HttpServletRequest request,
            HttpServletResponse response) {
        authService.verify(app, source, request, response);
    }

    @PostMapping("/route/{source}/{app}")
    @Operation(summary = "回调地址-接受第三方消息")
    public void routeSourceMessage(
            @PathVariable("app") String app,
            @PathVariable("source") String source,
            @RequestBody String requestBody,
            HttpServletRequest request,
            HttpServletResponse response) {
        authService.onMessage(app, source, request, requestBody, response);
    }

    @Operation(hidden = true)
    @GetMapping("/notify/{source}/{app}/{template}")
    public ResourceResponseDto<String> notify(
            @PathVariable("app") String app,
            @PathVariable("source") String source,
            @PathVariable("template") String template,
            HttpServletRequest request,
            HttpServletResponse response) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", "winston");
        notificationService.notifyToUser(app, 144L,
                new NotificationType[]{
                        NotificationType.WECHAT,
                        NotificationType.EMAIL,
                        NotificationType.SMS
                },
                template, params);
        return new ResourceResponseDto<>("ok");
    }

    @Operation(hidden = true)
    @GetMapping("/ping/{source}/{app}")
    public ResourceResponseDto<AuthPingResponseDto> ping(
            @PathVariable("app") String app,
            @PathVariable("source") String source,
            HttpServletRequest request,
            HttpServletResponse response) {
        AuthPingResponseDto pingResponseDto = authService.ping(app, source, request, response);
        return new ResourceResponseDto<>(pingResponseDto);
    }
}
