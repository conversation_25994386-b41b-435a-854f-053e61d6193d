package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.dto.permission.ResourcePermissionWrapDto;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.core.dto.*;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.validation.ValidSearchText;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Slf4j
@Tag(name = "数据权限")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/resource-permissions")
public class ResourcePermissionController {

    @Autowired
    private ResourcePermissionService resourcePermissionService;

    @Operation(summary = "查询与数据有关系的所有用户")
    @GetMapping(path = "{resource}/{resourceId}/relationUsers")
    public ResourceListResponseDto<ResourcePermissionUserDto> relationUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceListResponseDto<>(resourcePermissionService.relationUsers(TenantContext.requireCurrentTenant(), resourceId, resource));
    }

    @Operation(summary = "查询我与数据的关系")
    @GetMapping(path = "{resource}/{resourceId}/relation")
    public ResourceResponseDto<ResourcePermissionRelationType> relation(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceResponseDto<>(resourcePermissionService.calculateCurrentRelations(resourceId, resource));
    }

    @Operation(summary = "全部用户列表")
    @GetMapping(path = "{resource}/{resourceId}/all-users")
    public ResourceListResponseDto<ResourcePermissionUserDto> allUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceListResponseDto<>(resourcePermissionService.allUsers(resourceId, resource));
    }

    @Operation(summary = "全部角色列表")
    @GetMapping(path = "{resource}/{resourceId}/all-roles")
    public ResourceListResponseDto<ResourcePermissionRoleDto> allRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceListResponseDto<>(resourcePermissionService.allRoles(resourceId, resource));
    }

    @Operation(summary = "全部(用户|角色)列表")
    @GetMapping(path = "{resource}/{resourceId}/all")
    public ResourceResponseDto<ResourcePermissionWrapDto> all(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceResponseDto<>(resourcePermissionService.all(resourceId, resource));
    }

    @Operation(summary = "未加入用户列表")
    @GetMapping(path = "{resource}/{resourceId}/not-exists-users")
    public ResourcePageResponseDto<ResourcePermissionUserDto> notExistsUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @ValidSearchText @RequestParam(required = false) String _q,
            @RequestParam(required = false, defaultValue = "1") int _page,
            @RequestParam(required = false, defaultValue = "20") int _limit) {
        return new ResourcePageResponseDto<>(resourcePermissionService.notExistsUsers(resourceId, resource, _q, _page, _limit));
    }

    @Operation(summary = "未加入角色列表")
    @GetMapping(path = "{resource}/{resourceId}/not-exists-roles")
    public ResourcePageResponseDto<ResourcePermissionRoleDto> notExistsRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @ValidSearchText @RequestParam(required = false) String _q,
            @RequestParam(required = false, defaultValue = "1") int _page,
            @RequestParam(required = false, defaultValue = "20") int _limit) {
        return new ResourcePageResponseDto<>(resourcePermissionService.notExistsRoles(resourceId, resource, _q, _page, _limit));
    }

    @Operation(summary = "已加入用户列表")
    @GetMapping(path = "{resource}/{resourceId}/exists-users")
    public ResourceListResponseDto<ResourcePermissionUserDto> existsUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceListResponseDto<>(resourcePermissionService.existsUsers(resourceId, resource));
    }

    @Operation(summary = "已加入角色列表")
    @GetMapping(path = "{resource}/{resourceId}/exists-roles")
    public ResourceListResponseDto<ResourcePermissionRoleDto> existsRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceListResponseDto<>(resourcePermissionService.existsRoles(resourceId, resource));
    }

    @Operation(summary = "已加入(用户|角色)列表")
    @GetMapping(path = "{resource}/{resourceId}/exists")
    public ResourceResponseDto<ResourcePermissionWrapDto> exists(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId) {
        return new ResourceResponseDto<>(resourcePermissionService.exists(resourceId, resource));
    }

    @Operation(summary = "同步已加入用户列表")
    @PostMapping(path = "{resource}/{resourceId}/sync-users")
    public ResourceResponseDto<ResourcePermissionRelationType> syncUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "最终用户列表") @RequestBody @NotNull List<ResourcePermissionUserDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.syncUsers(resourceId, resource, data));
    }

    @Operation(summary = "同步已加入角色列表")
    @PostMapping(path = "{resource}/{resourceId}/sync-roles")
    public ResourceResponseDto<ResourcePermissionRelationType> syncRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "最终角色列表") @RequestBody @NotNull List<ResourcePermissionRoleDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.syncRoles(resourceId, resource, data));
    }

    @Operation(summary = "同步已加入(用户|角色)列表")
    @PostMapping(path = "{resource}/{resourceId}/sync")
    public ResourceResponseDto<ResourcePermissionRelationType> sync(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "最终(用户|角色)列表") @RequestBody @NotNull ResourcePermissionWrapDto data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.sync(resourceId, resource, data));
    }

    @Operation(summary = "加入用户")
    @PostMapping(path = "{resource}/{resourceId}/add-users")
    public ResourceResponseDto<ResourcePermissionRelationType> addUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待加入用户列表") @RequestBody @NotNull List<ResourcePermissionUserDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.addUsers(resourceId, resource, data));
    }

    @Operation(summary = "加入角色")
    @PostMapping(path = "{resource}/{resourceId}/add-roles")
    public ResourceResponseDto<ResourcePermissionRelationType> addRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待加入角色列表") @RequestBody @NotNull List<ResourcePermissionRoleDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.addRoles(resourceId, resource, data));
    }

    @Operation(summary = "加入(用户|角色)")
    @PostMapping(path = "{resource}/{resourceId}/add")
    public ResourceResponseDto<ResourcePermissionRelationType> add(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待加入(用户|角色)列表") @RequestBody @NotNull ResourcePermissionWrapDto data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.add(resourceId, resource, data));
    }

    @Operation(summary = "移除用户")
    @PostMapping(path = "{resource}/{resourceId}/remove-users")
    public ResourceResponseDto<ResourcePermissionRelationType> removeUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待移除用户列表") @RequestBody @NotNull List<ResourcePermissionUserDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.removeUsers(resourceId, resource, data));
    }

    @Operation(summary = "移除角色")
    @PostMapping(path = "{resource}/{resourceId}/remove-roles")
    public ResourceResponseDto<ResourcePermissionRelationType> removeRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待移除角色列表") @RequestBody @NotNull List<ResourcePermissionRoleDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.removeRoles(resourceId, resource, data));
    }

    @Operation(summary = "移除(用户|角色)")
    @PostMapping(path = "{resource}/{resourceId}/remove")
    public ResourceResponseDto<ResourcePermissionRelationType> remove(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待移除(用户|角色)列表") @RequestBody @NotNull ResourcePermissionWrapDto data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.remove(resourceId, resource, data));
    }

    @Operation(summary = "修改用户权限")
    @PostMapping(path = "{resource}/{resourceId}/update-users")
    public ResourceResponseDto<ResourcePermissionRelationType> updateUsers(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待修改用户列表") @RequestBody @NotNull List<ResourcePermissionUserDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.updateUsers(resourceId, resource, data));
    }

    @Operation(summary = "修改角色权限")
    @PostMapping(path = "{resource}/{resourceId}/update-roles")
    public ResourceResponseDto<ResourcePermissionRelationType> updateRoles(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待修改角色列表") @RequestBody @NotNull List<ResourcePermissionRoleDto> data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.updateRoles(resourceId, resource, data));
    }

    @Operation(summary = "修改(用户|角色)权限")
    @PostMapping(path = "{resource}/{resourceId}/update")
    public ResourceResponseDto<ResourcePermissionRelationType> update(
            @Parameter(description = "资源类型") @PathVariable ResourcePermissionType resource,
            @Parameter(description = "资源id") @PathVariable long resourceId,
            @Parameter(description = "待修改(用户|角色)列表") @RequestBody @NotNull ResourcePermissionWrapDto data
    ) {
        return new ResourceResponseDto<>(resourcePermissionService.update(resourceId, resource, data));
    }
}
