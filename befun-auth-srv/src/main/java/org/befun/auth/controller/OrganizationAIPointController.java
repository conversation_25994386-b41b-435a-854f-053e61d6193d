package org.befun.auth.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.pay.dto.wallet.AiPointBalanceDto;
import org.befun.auth.service.OrganizationAIPointService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "企业AI")
@Validated
@RestController
@RequestMapping("/organization/aiPoint")
@PreAuthorize("isAuthenticated()")
public class OrganizationAIPointController {

    @Autowired
    private OrganizationAIPointService organizationAIPointService;

    @GetMapping("balance")
    @Operation(summary = "AI点数")
    public ResourceResponseDto<AiPointBalanceDto> balance() {
        return new ResourceResponseDto<>(new AiPointBalanceDto(organizationAIPointService.balance(TenantContext.requireCurrentTenant())));
    }
}
