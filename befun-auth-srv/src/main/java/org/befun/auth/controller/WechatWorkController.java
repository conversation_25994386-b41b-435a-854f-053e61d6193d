package org.befun.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.befun.auth.dto.*;
import org.befun.auth.dto.auth.WechatWorkAuthDto;
import org.befun.auth.provider.wechat.work.WechatWorkAuthProvider;
import org.befun.auth.service.AuthService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;

@Slf4j
@Tag(name = "企业微信")
@RestController
@Validated
public class WechatWorkController {

    @Autowired
    private AuthService authService;
    @Autowired
    private WechatWorkAuthProvider wechatWorkAuthProvider;

    @GetMapping("/bind/account/wechat_work/pre-authorize/{app}")
    @Operation(summary = "绑定成员(1)-获取state")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<WechatWorkPreBindDto> preAuthorizeBindWechatWorkByMember(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        WechatWorkPreBindDto state = wechatWorkAuthProvider.preBindMemberState(orgId, userId, app);
        log.info("wechat work pre bind member: app={}, state={}", app, state.getState());
        return new ResourceResponseDto<>(state);
    }

    @GetMapping("/bind/account/wechat_work/authorize/{app}")
    @Operation(summary = "绑定成员(2)-授权地址，浏览器重定向到此地址")
    public void authorizeBindWechatWorkByMember(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "state") @RequestParam("state") String state,
            HttpServletResponse response) throws IOException {
        String redirectUrl = wechatWorkAuthProvider.bindMemberAuthorize(state);
        log.info("wechat work bind member url: app={}, authUrl={}", app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/bind/account/wechat_work/authorize/callback/{app}")
    @Operation(summary = "绑定成员(3)-回调")
    public ResourceResponseDto<Boolean> authorizeBindWechatWorkByMemberCallback(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "code") @RequestParam("code") String code,
            @Parameter(name = "state") @RequestParam("state") String state) {
        WechatWorkLoginCallbackDto callback = new WechatWorkLoginCallbackDto();
        callback.setCode(code);
        callback.setState(state);
        log.info("wechat work bind member callback: app={}, code={}, state={}", app, code, state);
        return new ResourceResponseDto<>(wechatWorkAuthProvider.bindMemberAuthorizeCallback(callback, app));
    }

    @GetMapping("/bind/wechat_work/pre-authorize/{app}")
    @Operation(summary = "绑定企业(1)-预授权")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<WechatWorkPreBindDto> preAuthorizeWechatWork(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        WechatWorkPreBindDto state = wechatWorkAuthProvider.preBindOrgState(orgId, userId, app);
        log.info("wechat work pre bind org: app={}, state={}", app, state.getState());
        return new ResourceResponseDto<>(state);
    }

    @GetMapping("/bind/wechat_work/authorize/{app}")
    @Operation(summary = "绑定企业(2)-授权地址，浏览器重定向到此地址")
    public void authorizeWechatWork(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "state") @RequestParam("state") String state,
            HttpServletResponse response) throws IOException {
        String redirectUrl = wechatWorkAuthProvider.bindOrgAuthorize(state);
        log.info("wechat work bind org url: app={}, authUrl={}", app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/bind/wechat_work/authorize/callback/{app}")
    @Operation(summary = "绑定企业(3)-回调")
    public ResourceResponseDto<WechatWorkAuthDto> authorizeWechatWorkCallback(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "code") @RequestParam("code") String code,
            @Parameter(name = "state") @RequestParam("state") String state) {
        AuthCallback callback = new AuthCallback();
        callback.setCode(code);
        callback.setState(state);
        log.info("wechat work bind org callback: app={}, code={}, state={}", app, code, state);
        return new ResourceResponseDto<>(wechatWorkAuthProvider.bindOrgCallback(code, state, app));
    }

    @GetMapping("/login/wechat_work/{app}")
    @Operation(summary = "登录(1)-登录地址，浏览器重定向到此地址")
    public void loginByWechatWork(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            HttpServletResponse response) throws IOException {
        TenantContext.clear();
        String source = "wechat_work";
        String redirectUrl = authService.authorize(source, app);
        log.info("wechat work login: source={}, app={}, authUrl={}", source, app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/login/callback/wechat_work/{app}")
    @Operation(summary = "登录(2)-回调")
    public ResourceResponseDto<LoginResponseDto> casLoginCallback(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "code") @RequestParam("code") String code) {
        TenantContext.clear();
        String source = "wechat_work";
        WechatWorkLoginCallbackDto callback = new WechatWorkLoginCallbackDto();
        callback.setCode(code);
        log.info("wechat work login callback: source={}, app={}, code={}", source, app, code);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, callback));
    }

    @PostMapping("/bind/wechat_work/{app}")
    @Operation(summary = "登录-绑定|新建账号", description = "企业微信登录回调没有匹配账号时，调用此接口")
    public ResourceResponseDto<Boolean> wechatWorkBind(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @RequestBody @Valid @NotNull WechatWorkBindDto dto) {
        TenantContext.clear();
        log.info("wechat work login bind: app={}, type={}, bindToken={}, email={}", app, dto.getType(), dto.getBindToken(), dto.getEmail());
        return new ResourceResponseDto<>(wechatWorkAuthProvider.loginAfterBindOrCreate(app, dto));
    }

    @GetMapping("/wechat_work/ticket/{app}")
    @Operation(summary = "企业微信配置-校验应用ticket回调地址", description = "配置在企业微信的后台")
    public String wechatWorkTicketVerify(
            @PathVariable("app") String app,
            @RequestParam("msg_signature") String sign,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam("echostr") String echostr) {
        TenantContext.clear();
        return wechatWorkAuthProvider.verifyTicketUrl(app, sign, timestamp, nonce, echostr);
    }

    @PostMapping("/wechat_work/ticket/{app}")
    @Operation(summary = "企业微信配置-应用ticket回调地址", description = "配置在企业微信的后台")
    public String wechatWorkTicket(
            @PathVariable("app") String app,
            @RequestParam("msg_signature") String sign,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestBody @NotNull String body) {
        TenantContext.clear();
        return wechatWorkAuthProvider.parseTicket(app, sign, timestamp, nonce, body);
    }

    @GetMapping("/wechat_work/message/{app}/{corpId}")
    @Operation(summary = "企业微信配置-校验应用消息回调地址", description = "配置在企业微信的后台")
    public String wechatWorkMessageVerify(
            @PathVariable("app") String app,
            @PathVariable("corpId") String corpId,
            @RequestParam("msg_signature") String sign,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam("echostr") String echostr) {
        TenantContext.clear();
        return wechatWorkAuthProvider.verifyMessageUrl(app, sign, timestamp, nonce, echostr);
    }

    @PostMapping("/wechat_work/message/{app}/{corpId}")
    @Operation(summary = "企业微信配置-应用消息回调地址", description = "配置在企业微信的后台")
    public String wechatWorkMessage(
            @PathVariable("app") String app,
            @PathVariable("corpId") String corpId,
            @RequestParam("msg_signature") String sign,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestBody @NotNull String body) {
        TenantContext.clear();
        return wechatWorkAuthProvider.parseMessage(app, corpId, sign, timestamp, nonce, body);
    }

}
