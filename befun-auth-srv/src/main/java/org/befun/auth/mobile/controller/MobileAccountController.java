package org.befun.auth.mobile.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.dto.BindVerifyCodeRequestDto;
import org.befun.auth.dto.ResetPasswordRequestDto;
import org.befun.auth.service.AuthService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Tag(name = "移动端-用户")
@RequestMapping("/m")
@RestController
@Validated
public class MobileAccountController {

    @Autowired
    private AuthService authService;
    @Autowired
    private PasswordHelper passwordHelper;

    @PostMapping("/reset-password/{source}/{app}")
    @Operation(summary = "找回密码-重置")
    public ResourceResponseDto<Boolean> resetPassword(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody ResetPasswordRequestDto dto) {
        TenantContext.clear(); // 清空登录信息，之后的查询需要全局查询
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>(authService.resetPasswordByVerifyCode(source, app, dto));
    }
}
