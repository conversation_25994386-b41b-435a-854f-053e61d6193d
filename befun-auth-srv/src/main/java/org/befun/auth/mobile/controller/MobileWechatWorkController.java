package org.befun.auth.mobile.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.dto.LoginResponseDto;
import org.befun.auth.dto.WechatWorkBindDto;
import org.befun.auth.dto.WechatWorkLoginCallbackDto;
import org.befun.auth.provider.wechat.work.WechatWorkAuthProvider;
import org.befun.auth.service.AuthService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;

@Slf4j
@Tag(name = "移动端-企业微信")
@RestController
@RequestMapping("/m")
@Validated
public class MobileWechatWorkController {

    @Autowired
    private AuthService authService;
    @Autowired
    private WechatWorkAuthProvider wechatWorkAuthProvider;

    @GetMapping("/login/wechat_work/{app}")
    @Operation(summary = "登录(1)-登录地址，浏览器重定向到此地址")
    public void loginByWechatWork(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @NotEmpty String redirectPath,
            HttpServletResponse response) throws IOException {
        TenantContext.clear();
        String source = "wechat_work";
        String redirectUrl = wechatWorkAuthProvider.mobileAuthorize(source, app, redirectPath);
        log.info("wechat work mobile login: source={}, app={}, authUrl={}", source, app, redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @GetMapping("/login/callback/wechat_work/{app}")
    @Operation(summary = "登录(2)-回调")
    public ResourceResponseDto<LoginResponseDto> wechatWorkLoginCallback(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Parameter(name = "code") @RequestParam("code") String code) {
        TenantContext.clear();
        String source = "wechat_work";
        WechatWorkLoginCallbackDto callback = new WechatWorkLoginCallbackDto();
        callback.setCode(code);
        callback.setType("mobile");
        log.info("wechat work mobile login callback: source={}, app={}, code={}", source, app, code);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, callback));
    }

    @PostMapping("/bind/wechat_work/{app}")
    @Operation(summary = "登录-绑定|新建账号", description = "企业微信登录回调没有匹配账号时，调用此接口")
    public ResourceResponseDto<Boolean> wechatWorkBind(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @RequestBody @Valid @NotNull WechatWorkBindDto dto) {
        TenantContext.clear();
        log.info("wechat work mobile login bind: app={}, type={}, bindToken={}, email={}", app, dto.getType(), dto.getBindToken(), dto.getEmail());
        return new ResourceResponseDto<>(wechatWorkAuthProvider.loginAfterBindOrCreate(app, dto));
    }
}
