package org.befun.auth.mobile.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.UserGuideIndoType;
import org.befun.auth.dto.*;
import org.befun.auth.entity.User;
import org.befun.auth.entity.UserDto;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.UserService;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Set;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "移动端-用户")
@RestController
@Validated
@RequestMapping("/m/users")
@PreAuthorize("isAuthenticated()")
public class MobileUserController {

    @Autowired
    private UserService userService;

    @GetMapping("current/userinfo")
    @Operation(summary = "我的用户信息")
    public ResourceResponseDto<UserInfoResponseDto> currentInfo() {
        return new ResourceResponseDto<>(userService.currentUserInfo());
    }

    @PostMapping("current/update-info")
    @Operation(summary = "修改我的用户信息")
    public ResourceResponseDto<Boolean> updateTruename(@Valid @RequestBody UserChangeInfoRequestDto dto) {
        return new ResourceResponseDto<>(userService.updateInfo(dto));
    }

    @PostMapping("current/update-password")
    @Operation(summary = "修改我的密码")
    public ResourceResponseDto<Boolean> updatePassword(@RequestBody UserChangePasswordRequestDto dto) {
        return new ResourceResponseDto<>(userService.updatePassword(dto));
    }

    @GetMapping("current/permissions")
    @Operation(summary = "我的权限列表")
    public ResourceResponseDto<PermissionResponseDto> getPermissions() {
        return new ResourceResponseDto<>(userService.getPermissions());
    }
}
