package org.befun.auth.mobile.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.LoginPlatform;
import org.befun.auth.dto.*;
import org.befun.auth.service.AuthService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Slf4j
@Tag(name = "移动端-登录")
@RequestMapping("/m")
@RestController
@Validated
public class MobileLoginController {

    @Autowired
    private AuthService authService;
    @Autowired
    private PasswordHelper passwordHelper;

    @Deprecated
    @GetMapping("/login/refresh-token")
    @Operation(summary = "登录-刷新token(已过期，使用路径上有app的地址)")
    public ResourceResponseDto<LoginResponseDto> refreshToken(@NotEmpty @RequestParam String refreshToken) {
        TenantContext.clear();
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("refresh_token", "cem", refreshToken));
    }

    @GetMapping("/login/refresh-token/{app}")
    @Operation(summary = "登录-刷新token")
    public ResourceResponseDto<LoginResponseDto> refreshToken(@NotEmpty @RequestParam String refreshToken, @PathVariable("app") String app) {
        TenantContext.clear();
        LoginRefreshTokenRequestDto dto = new LoginRefreshTokenRequestDto();
        dto.setRefreshToken(refreshToken);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("refresh_token", app, dto));
    }

    @PostMapping("/login/refresh-token/{app}")
    @Operation(summary = "登录-刷新token（mfa）")
    public ResourceResponseDto<LoginResponseDto> refreshToken(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginRefreshTokenRequestDto dto) {
        TenantContext.clear();
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("refresh_token", app, dto));
    }

    @GetMapping("/login/password/{app}/rsaPubKey")
    @Operation(summary = "登录-密码登录(获取加密公钥)")
    public ResourceResponseDto<LoginPasswordRsaPubKeyDto> loginPasswordRsaPubKey(@Parameter(name = "app", description = "默认cem") @PathVariable("app") String app) {
        return new ResourceResponseDto<>(passwordHelper.pubKey());
    }

    @PostMapping("/login/password/{app}")
    @Operation(summary = "登录-密码登录")
    public ResourceResponseDto<LoginResponseDto> loginPassword(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginPasswordRequestDto dto) {
        TenantContext.clear();
        dto.confirmPassword(passwordHelper::decryptRsa);
        dto.setPlatform(LoginPlatform.mobile);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("password", app, dto));
    }

    @PostMapping("/login/{source}/{app}")
    @Operation(summary = "登录-验证码登录")
    public ResourceResponseDto<LoginResponseDto> loginVerifyCode(
            @Parameter(name = "source", description = "mobile|email") @PathVariable("source") String source,
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginVerifyCodeRequestDto dto) {
        TenantContext.clear();
        dto.setPlatform(LoginPlatform.mobile);
        return new ResourceResponseDto<>((LoginResponseDto) authService.login(source, app, dto));
    }

}
