package org.befun.auth.mobile.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.dto.*;
import org.befun.auth.entity.UserInvitationDto;
import org.befun.auth.service.UserInviteService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

@Tag(name = "移动端-用户-邀请")
@Validated
@RestController
@RequestMapping("/m/users/invite")
public class MobileUserInviteController {

    @Autowired
    private UserInviteService userInviteService;
    @Autowired
    private PasswordHelper passwordHelper;

    @GetMapping("active/status")
    @Operation(summary = "检查邀请链接的状态")
    public ResourceResponseDto<UserInviteStatusResponseDto> inviteActiveStatus(@NotEmpty @Parameter(name = "code", description = "邀请码") @RequestParam("code") String code) {
        TenantContext.clear();
        return new ResourceResponseDto<>(userInviteService.inviteStatus(code));
    }

    @PostMapping("active")
    @Operation(summary = "激活邀请")
    public ResourceResponseDto<Boolean> inviteActive(@Valid @RequestBody UserInviteActiveRequestDto dto) {
        TenantContext.clear();
        dto.confirmPassword(passwordHelper::decryptRsa);
        return new ResourceResponseDto<>(userInviteService.inviteActive(dto.getCode(), dto.getTruename(), dto.getPassword()));
    }

}
