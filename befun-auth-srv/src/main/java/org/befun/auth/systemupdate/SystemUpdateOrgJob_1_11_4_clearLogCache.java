package org.befun.auth.systemupdate;

import lombok.extern.slf4j.Slf4j;
import org.befun.extension.systemupdate.SystemUpdateOrgJob;
import org.befun.extension.systemupdate.SystemVersion;
import org.befun.extension.systemupdate.SystemVersions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SystemUpdateOrgJob_1_11_4_clearLogCache implements SystemUpdateOrgJob {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public SystemVersion systemVersion() {
        return SystemVersions.V_1_11_4;
    }



    @Override
    public void triggerOrgJob(Long orgId) {
        try {
            log.info("清理x-pack-log log-status");
            stringRedisTemplate.opsForHash().delete("cache:xpack-config:log", "log-status");
        } catch (Throwable e) {
            log.error("升级失败：job={}, {}", jobName(), e.getMessage());
        }
    }


}
