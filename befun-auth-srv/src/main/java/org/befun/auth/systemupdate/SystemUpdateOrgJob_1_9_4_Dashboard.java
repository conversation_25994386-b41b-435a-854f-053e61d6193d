package org.befun.auth.systemupdate;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.Permissions;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.extension.systemupdate.SystemUpdateOrgJob;
import org.befun.extension.systemupdate.SystemVersion;
import org.befun.extension.systemupdate.SystemVersions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SystemUpdateOrgJob_1_9_4_Dashboard implements SystemUpdateOrgJob {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private RoleService roleService;

    @Override
    public SystemVersion systemVersion() {
        return SystemVersions.V_1_9_4;
    }


    @Override
    public void triggerOrgJob(Long orgId) {
        try {
            addVerify(orgId);
        } catch (Throwable e) {
            log.error("升级失败：job={}, {}", jobName(), e.getMessage());
        }
    }

    public void addVerify(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return;
        }

        var role = roleService.getSuperAdminByOrg(orgId);

        if (role == null) {
            return;
        }

        Set<String> permissions = Optional.ofNullable(permissionRepository.findByRoleId(role.getId()))
                .map(l -> l.stream().map(Permission::getPermission).collect(Collectors.toSet())).orElse(new HashSet<>());

        // 客户画像权限
        Permissions action1 = Permissions.WORKBENCH_DASHBOARD_VIEW;
        Permissions action2 = Permissions.WORKBENCH_DASHBOARD_EDIT;
        boolean[] exists = new boolean[]{false, false};
        for (String permission : permissions) {
            if (action1.getPath().equals(permission)) {
                exists[0] = true;
            }
            if (action2.getPath().equals(permission)) {
                exists[1] = true;
            }
        }
        List<Permission> add = new ArrayList<>();
        if (!exists[0] && !exists[1]) {
            add.add(new Permission(role.getId(), "Action", action1.getPath()));
            add.add(new Permission(role.getId(), "Action", action2.getPath()));
        }
        if (!add.isEmpty()) {
            permissionRepository.saveAll(add);
        }
    }

}
