package org.befun.auth.systemupdate;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.Permissions;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.extension.systemupdate.SystemUpdateOrgJob;
import org.befun.extension.systemupdate.SystemVersion;
import org.befun.extension.systemupdate.SystemVersions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SystemUpdateOrgJob_1_8_7_RolePermission implements SystemUpdateOrgJob {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private RoleService roleService;

    @Override
    public SystemVersion systemVersion() {
        return SystemVersions.V_1_8_7;
    }

    @Override
    public void triggerOrgJob(Long orgId) {
        //给超管添加画像权限
        try {
            addVerify(orgId);
        } catch (Throwable e) {
            //ignore
            log.error("升级失败：job={}, {}", jobName(), e.getMessage());
        }
    }

    /**
     * 给超管添加客户画像权限
     */
    public void addVerify(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return;
        }

        var role = roleService.getSuperAdminByOrg(orgId);

        if (role == null) {
            return;
        }

        Set<String> permissions = Optional.ofNullable(permissionRepository.findByRoleId(role.getId()))
                .map(l -> l.stream().map(Permission::getPermission).collect(Collectors.toSet())).orElse(new HashSet<>());

        // 客户画像权限
        Permissions action1 = Permissions.CUSTOMER_PORTRAIT_VIEW;
        Permissions action2 = Permissions.CUSTOMER_PORTRAIT_EDIT;
        // free base 版本添加 管理后台可见/编辑的权限
        Permissions action3 = Permissions.SYS_MANAGE_USER_MANAGE_VIEW;
        Permissions action4 = Permissions.SYS_MANAGE_ROLE_MANAGE_VIEW;
        Permissions action5 = Permissions.SYS_MANAGE_LEVEL_MANAGE_VIEW;
        Permissions action6 = Permissions.SYS_MANAGE_USER_MANAGE_EDIT;
        Permissions action7 = Permissions.SYS_MANAGE_ROLE_MANAGE_EDIT;
        Permissions action8 = Permissions.SYS_MANAGE_LEVEL_MANAGE_EDIT;
        boolean[] exists = new boolean[]{false, false, false, false, false, false, false, false};
        for (String permission : permissions) {
            if (action1.getPath().equals(permission)) {
                exists[0] = true;
            }
            if (action2.getPath().equals(permission)) {
                exists[1] = true;
            }
            if (action3.getPath().equals(permission)) {
                exists[2] = true;
            }
            if (action4.getPath().equals(permission)) {
                exists[3] = true;
            }
            if (action5.getPath().equals(permission)) {
                exists[4] = true;
            }
            if (action6.getPath().equals(permission)) {
                exists[5] = true;
            }
            if (action7.getPath().equals(permission)) {
                exists[6] = true;
            }
            if (action8.getPath().equals(permission)) {
                exists[7] = true;
            }
        }
        List<Permission> add = new ArrayList<>();
        if (!exists[0] && !exists[1]) {
            add.add(new Permission(role.getId(), "Action", action1.getPath()));
            add.add(new Permission(role.getId(), "Action", action2.getPath()));
        }
        if (List.of(AppVersion.FREE, AppVersion.BASE).contains(organizationService.parseOrgVersion2(organizationService.require(orgId)))) {
            if (!exists[2]) {
                add.add(new Permission(role.getId(), "Action", action3.getPath()));
            }
            if (!exists[3]) {
                add.add(new Permission(role.getId(), "Action", action4.getPath()));
            }
            if (!exists[4]) {
                add.add(new Permission(role.getId(), "Action", action5.getPath()));
            }
            if (!exists[5]) {
                add.add(new Permission(role.getId(), "Action", action6.getPath()));
            }
            if (!exists[6]) {
                add.add(new Permission(role.getId(), "Action", action7.getPath()));
            }
            if (!exists[7]) {
                add.add(new Permission(role.getId(), "Action", action8.getPath()));
            }
        }
        if (!add.isEmpty()) {
            permissionRepository.saveAll(add);
        }
    }
}
