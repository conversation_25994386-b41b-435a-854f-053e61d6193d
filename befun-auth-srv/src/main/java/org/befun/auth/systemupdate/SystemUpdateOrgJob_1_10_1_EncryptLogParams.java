package org.befun.auth.systemupdate;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.entity.ApiKey;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.ApiKeyService;
import org.befun.auth.service.OperateLogService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.core.utils.AESUtils;
import org.befun.extension.entity.OperateLog;
import org.befun.extension.repository.OperateLogRepository;
import org.befun.extension.systemupdate.SystemUpdateOrgJob;
import org.befun.extension.systemupdate.SystemVersion;
import org.befun.extension.systemupdate.SystemVersions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class SystemUpdateOrgJob_1_10_1_EncryptLogParams implements SystemUpdateOrgJob {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private OperateLogRepository operateLogRepository;

    @Autowired
    private ApiKeyService apiKeyService;

    @Override
    public SystemVersion systemVersion() {
        return SystemVersions.V_1_10_1;
    }


    @Override
    @Async
    public void triggerOrgJob(Long orgId) {
        try {
            encryptParams(orgId);
        } catch (Throwable e) {
            log.error("升级失败：job={}, {}", jobName(), e.getMessage());
        }
    }

    public void encryptParams(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return;
        }

        ApiKey apiKey = apiKeyService.getOrgApiKey(orgId);

        if(apiKey == null && apiKey.getApiSecret()==null){
            return;
        }

        PageRequest page = PageRequest.of(0, 200);
        Page<OperateLog> operateLogs = getOperateLogs(orgId, page);
        log.info("开始加密日志参数，orgId={},总数={}",orgId,operateLogs.getTotalElements());
        do {
            operateLogs.forEach(operateLog -> {
                String params = operateLog.getParams();
                if(params!=null && params.startsWith("{") && params.endsWith("}")){
                    try {
                        operateLog.setParams(AESUtils.encrypt(apiKey.getApiSecret(),params));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    operateLogRepository.save(operateLog);
                }
            });
            operateLogs = getOperateLogs(orgId, operateLogs.nextPageable());

        }while (operateLogs.getPageable().isPaged());

    }

    private Page<OperateLog> getOperateLogs(Long orgId, Pageable page) {
        return operateLogRepository.findAll((r, q, b) -> b.and(b.equal(r.get("orgId"), orgId)), page);
    }

}
