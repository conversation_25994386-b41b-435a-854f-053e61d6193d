package org.befun.auth.systemupdate;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.PermissionPath;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.extension.systemupdate.SystemUpdateOrgJob;
import org.befun.extension.systemupdate.SystemVersion;
import org.befun.extension.systemupdate.SystemVersions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class SystemUpdateOrgJob_1_10_1_FreeVersionPermission implements SystemUpdateOrgJob {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private RoleService roleService;

    @Override
    public SystemVersion systemVersion() {
        return SystemVersions.V_1_10_1;
    }


    @Override
    public void triggerOrgJob(Long orgId) {
        try {
            addPermission(orgId);
        } catch (Throwable e) {
            log.error("升级失败：job={}, {}", jobName(), e.getMessage());
        }
    }

    public void addPermission(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return;
        }

        var role = roleService.getSuperAdminByOrg(orgId);

        if (role == null) {
            return;
        }

        Set<String> permissions = Optional.ofNullable(permissionRepository.findByRoleId(role.getId()))
                .map(l -> l.stream().map(Permission::getPermission).collect(Collectors.toSet())).orElse(new HashSet<>());

        List<Permission> add = Stream.of(
                        PermissionPath.CUSTOMER_LIFE_EDIT,
                        PermissionPath.TOUCH_MANAGE_SEND_MANAGE_VIEW,
                        PermissionPath.SYS_MANAGE_LEVEL_MANAGE_EDIT,
                        PermissionPath.SYS_MANAGE_ROLE_MANAGE_EDIT)
                .filter(i -> !permissions.contains(i))
                .map(i -> new Permission(role.getId(), "Action", i))
                .collect(Collectors.toList());

        if (!add.isEmpty()) {
            permissionRepository.saveAll(add);
        }
    }

}
