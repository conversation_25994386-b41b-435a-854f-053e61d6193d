package org.befun.auth;

import cn.hanyi.cem.core.CemCoreAutoConfiguration;
import cn.hanyi.common.file.storage.FileStorageAutoConfiguration;
import com.jayway.jsonpath.spi.cache.CacheProvider;
import com.jayway.jsonpath.spi.cache.NOOPCache;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.befun.extension.XPackAutoConfiguration;
import org.befun.task.TaskAutoConfiguration;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.security.saml2.Saml2RelyingPartyAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.TimeZone;

@SpringBootApplication(
        scanBasePackages = {"org.befun.core", "org.befun.auth"},
        exclude = {
                SecurityAutoConfiguration.class,
                FileStorageAutoConfiguration.class,
                Saml2RelyingPartyAutoConfiguration.class
        })
@EnableAsync
public class BefunAuthApplication implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }

    @Configuration
    @EntityScan({
            "org.befun.core.entity",
            AuthAutoConfiguration.PACKAGE_ENTITY,
            TaskAutoConfiguration.PACKAGE_ENTITY,
            XPackAutoConfiguration.PACKAGE_ENTITY,
            CemCoreAutoConfiguration.PACKAGE_ENTITY,
    })
    @EnableJpaRepositories(basePackages = {
            "org.befun.core.repo",
            AuthAutoConfiguration.PACKAGE_REPOSITORY,
            TaskAutoConfiguration.PACKAGE_REPOSITORY,
            XPackAutoConfiguration.PACKAGE_REPOSITORY,
            CemCoreAutoConfiguration.PACKAGE_REPOSITORY,
    },
            repositoryBaseClass = BaseRepositoryImpl.class)
    public static class CemJPAConfig {
    }

    public static void main(String[] args) {
        CacheProvider.setCache(new NOOPCache());
        System.setProperty("org.springframework.boot.logging.LoggingSystem", "none");
        SpringApplication.run(BefunAuthApplication.class, args);
    }

}
