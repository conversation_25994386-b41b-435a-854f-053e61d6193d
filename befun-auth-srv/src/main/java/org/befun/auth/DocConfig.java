package org.befun.auth;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.befun.extension.doc.OverrideQueryOperation;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "springdoc.api-docs.enabled", havingValue = "true")
public class DocConfig {

    @Bean
    public GroupedOpenApi pc() {
        String[] packagedToMatch = {"org.befun.auth.controller", "org.befun.extension.controller"};
        OverrideQueryOperation overrideQueryOperation = new OverrideQueryOperation();
        return GroupedOpenApi.builder()
                .group("auth-pc")
                .pathsToMatch("/**")
                .addOperationCustomizer(overrideQueryOperation)
                .addOpenApiCustomiser(overrideQueryOperation)
                .packagesToScan(packagedToMatch).build();
    }

    @Bean
    public GroupedOpenApi mobile() {
        OverrideQueryOperation overrideQueryOperation = new OverrideQueryOperation();
        String[] packagedToMatch = {"org.befun.auth.mobile.controller"};
        return GroupedOpenApi.builder()
                .group("auth-mobile")
                .pathsToMatch("/**")
                .addOperationCustomizer(overrideQueryOperation)
                .addOpenApiCustomiser(overrideQueryOperation)
                .packagesToScan(packagedToMatch).build();
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("auth api")
                        .version("1.8.0")
                        .description("auth api")
                        .termsOfService("https://dev.xmplus.cn/api/doc/doc.html"));
    }
}
