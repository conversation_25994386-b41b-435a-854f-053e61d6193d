<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.befun.auth</groupId>
        <artifactId>parent</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>befun-auth-trigger</artifactId>
    <!--  要修改版本号，直接改最外层pom的revision  -->
    <version>${revision}.${sha1}-${changelist}</version>
    <name>befun-auth-trigger</name>
    <description>befun auth trigger project</description>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
    </dependencies>
</project>
