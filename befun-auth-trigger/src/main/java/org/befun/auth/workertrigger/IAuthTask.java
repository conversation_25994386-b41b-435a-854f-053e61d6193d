package org.befun.auth.workertrigger;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

public interface IAuthTask {

    default List<IAuthTaskConsumer> getConsumers() {
        return null;
    }

    private void foreachConsumers(Consumer<IAuthTaskConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }

    default void smsNotificationDelay(Long orgId, Long userId, String templateName, String mobile, Map<String, Object> parameters, Duration delay) {
        foreachConsumers(c -> c.smsNotificationDelay(orgId, userId, templateName, mobile, parameters, delay));
    }

    default void youzanMessageTriggerSendManage(Long orgId, Long userId, Long sendManageId, String thirdpartyMessageType, Long thirdpartyMessageId) {
        foreachConsumers(c -> c.youzanMessageTriggerSendManage(orgId, userId, sendManageId, thirdpartyMessageType, thirdpartyMessageId));
    }

    default void departmentRelatedResponse(Long orgId, Long userId, Long departmentId) {
        foreachConsumers(c -> c.departmentRelatedResponse(orgId, userId, departmentId));
    }

}
