package org.befun.auth.workertrigger;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

public interface IAuthEvent {

    default List<IAuthEventConsumer> getConsumers() {
        return null;
    }

    private void foreachConsumers(Consumer<IAuthEventConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }

    default void apiKeyRefresh(Long orgId, Long triggerUserId, Long apiKeyId) {
        foreachConsumers(c -> c.apiKeyRefresh(orgId, triggerUserId, apiKeyId));
    }

    /**
     * 企业部门刷新（删除了多个部门，或者导入了多个部门）
     */
    default void departmentRefresh(Long orgId, Long triggerUserId) {
        foreachConsumers(c -> c.departmentRefresh(orgId, triggerUserId));
    }

    default void departmentCreate(Long orgId, Long triggerUserId, Long departmentId) {
        foreachConsumers(c -> c.departmentCreate(orgId, triggerUserId, departmentId));
    }

    default void departmentUpdate(Long orgId, Long triggerUserId, Long departmentId) {
        foreachConsumers(c -> c.departmentUpdate(orgId, triggerUserId, departmentId));
    }

    default void departmentDelete(Long orgId, Long triggerUserId, Long departmentId) {
        foreachConsumers(c -> c.departmentDelete(orgId, triggerUserId, departmentId));
    }

    /**
     * 当指定的资源的有权限的用户被添加时，触发此事件
     */
    default void resourcePermissionAddUser(Long orgId, Long userId, Long resourceId, String resourceType, List<Long> targetUserId) {
        foreachConsumers(c -> c.resourcePermissionAddUser(orgId, userId, resourceId, resourceType, targetUserId));
    }

    /**
     * 当指定的资源的有权限的用户被移除时，触发此事件
     */
    default void resourcePermissionRemoveUser(Long orgId, Long userId, Long resourceId, String resourceType, List<Long> targetUserId) {
        foreachConsumers(c -> c.resourcePermissionRemoveUser(orgId, userId, resourceId, resourceType, targetUserId));
    }

    default void userRegister(Long orgId, Long userId, String password, String companyName, String customer) {
        foreachConsumers(c -> c.userRegister(orgId, userId, password, companyName, customer));
    }

    default void userLogin(Long orgId, Long userId) {
        foreachConsumers(c -> c.userLogin(orgId, userId));
    }

    default void userDelete(Long orgId, Long userId) {
        foreachConsumers(c -> c.userDelete(orgId, userId));
    }

    default void updateOrgName(Long orgId, Long userId, String name, String code) {
        foreachConsumers(c -> c.updateOrgName(orgId, userId, name, code));
    }

    default void rechargeCompleted(Long orgId, Long userId, Long rechargeId, String status) {
        foreachConsumers(c -> c.rechargeCompleted(orgId, userId, rechargeId, status));
    }

    default void rechargeExpired(Long orgId, Long userId, Long rechargeId, Duration delay) {
        foreachConsumers(c -> c.rechargeExpired(orgId, userId, rechargeId, delay));
    }

    default void rechargeRefundQuery(Long orgId, Long userId, Long rechargeId, Long rechargeRefundId, Duration delay) {
        foreachConsumers(c -> c.rechargeRefundQuery(orgId, userId, rechargeId, rechargeRefundId, delay));
    }

    default void rechargeRefundCompleted(Long orgId, Long userId, Long rechargeId, Long rechargeRefundId, String status) {
        foreachConsumers(c -> c.rechargeRefundCompleted(orgId, userId, rechargeId, rechargeRefundId, status));
    }

    default void orderCompleted(Long orgId, Long userId, Long orderId, String status) {
        foreachConsumers(c -> c.orderCompleted(orgId, userId, orderId, status));
    }

    default void wechatOpenAuthorize(Long orgId, Long userId, Long thirdpartyAuthId, String appId) {
        foreachConsumers(c -> c.wechatOpenAuthorize(orgId, userId, thirdpartyAuthId, appId));
    }

    default void wechatOpenDelete(Long orgId, Long userId, Long thirdpartyAuthId, boolean deleteUser) {
        foreachConsumers(c -> c.wechatOpenDelete(orgId, userId, thirdpartyAuthId, deleteUser));
    }

    default void wechatOpenSubscribe(String appId, String openId) {
        foreachConsumers(c -> c.wechatOpenSubscribe(appId, openId));
    }

    default void wechatOpenUnsubscribe(String appId, String openId) {
        foreachConsumers(c -> c.wechatOpenUnsubscribe(appId, openId));
    }
}
