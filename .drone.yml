kind: pipeline
type: docker
name: default

platform:
  os: linux
  arch: amd64

clone:
  depth: 1

steps:
  - name: branch-build
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
      - name: auth
        path: /tmp/repository
    environment:
      MAVEN_CONFIG: /drone/src/.m2
    commands:
      - mvn install -U -Dtest=StartUpTest -DfailIfNoTests=false -Dmaven.repo.local=/tmp/repository

  - name: publish
    image: plugins/docker
    pull: if-not-exists
    volumes:
      - name: docker-auths
        path: /root/.docker/config.json
    settings:
      repo: registry-vpc.cn-shenzhen.aliyuncs.com/surveyplus/befun-auth
      registry: registry-vpc.cn-shenzhen.aliyuncs.com
      tag: bochk

  - name: trigger
    image: plugins/webhook
    pull: if-not-exists
    settings:
      mecthod: POST
      urls:
        - https://cs.console.aliyun.com/hook/trigger?token=*****************************************************************************************************************************************************************************************************************************************************************************************************


  - name: notify
    image: fifsky/drone-wechat-work
    pull: if-not-exists
    settings:
      url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0accac60-9d20-446f-8e8a-4f758da26b57
      msgtype: markdown
      content: |
        {{if eq .Status "success" }}
        #### 🎉 ${DRONE_REPO} 构建成功
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > [点击查看](${DRONE_BUILD_LINK})
        {{else}}
        #### ❌ ${DRONE_REPO} 构建失败
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > 请立即修复!!!
        > [点击查看](${DRONE_BUILD_LINK})
        {{end}}
    when:
      status:
        - failure
        - success

volumes:
  - name: cache
    host:
      path: /data/drone/cache
  - name: m2-global-settings
    host:
      path: /data/drone/settings.xml
  - name: docker-auths
    host:
      path: /root/.docker/config.json
  - name: ssh_rsa
    host:
      path: /root/.ssh/id_rsa
  - name: auth
    host:
      path: /data/drone/cache/auth

trigger:
  branch:
    - project/*


image_pull_secrets:
  - dockerconfig
