#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

dir_core=$DIR_CORE
dir_auth=$DIR_AUTH
dir_ctm=$DIR_CTM
dir_survey=$DIR_SURVEY
dir_worker=$DIR_WORKER

function applyProject() {
  newVersion=$1
  echo $newVersion
  #从 .env 文件中获取当前的版本信息
  oldVersion=`grep 'REVISION' sh/.env | cut -b 10-`
  echo $oldVersion
  git checkout develop
  git pull
  git merge feature/${oldVersion}
  git push
  if [newVersion != oldVersion];then
    sed "s/${oldVersion}/${newVersion}/" sh/.env
  fi
#  git checkout -b feature/${newVersion}
}


cd ..
echo "开始提交所有模块：`pwd`"

#cd ${dir_core}
#applyProject
#
#cd ../${dir_worker}
#applyProject

cd ${dir_auth}
applyProject $1

#cd ../${dir_ctm}
#applyProject
#
#cd ../${dir_survey}
#applyProject
