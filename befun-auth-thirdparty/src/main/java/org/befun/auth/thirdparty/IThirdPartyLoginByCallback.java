package org.befun.auth.thirdparty;

import org.befun.auth.dto.LoginResponseDto;

public interface IThirdPartyLoginByCallback {

    /**
     * 生成授权链接
     */
    String loginByCallbackRedirectUrl(String source, String app);

    /**
     * 第三方授权成功后回调（携带回调参数）登录
     */
    LoginResponseDto loginByCallback(String source, String app, Object loginCallbackParams);

    /**
     * 第三方授权成功后回调登录，发现没有绑定账号时，新建或者绑定账号
     */
    boolean loginByCallbackBindAccount(String source, String app, Object bindCallbackParams);
}
