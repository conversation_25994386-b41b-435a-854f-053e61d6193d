package org.befun.auth.thirdparty.wechatmp;

/**
 * 公众号
 */
public class AuthWechatMpService {


    /**
     * 回调域名验证
     */
    public void verify(){

    }

    /**
     * 接受回调消息
     */
    public void receiverMessage(){

    }

    /**
     * 绑定-生成绑定标识
     */
    public void preBind() {

    }

    /**
     * 绑定-生成绑定标识
     */
    public void bind() {

    }

    /**
     * 绑定-查询绑定状态
     */
    public void bindStatus() {

    }

    public void sendTemplateMessage() {

    }


}
